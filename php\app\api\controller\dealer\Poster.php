<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2023 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\api\controller\dealer;

use think\response\Json;
use app\api\controller\Controller;
use app\api\model\dealer\User as DealerUserModel;
use app\api\service\User as UserService;
use app\common\service\qrcode\Poster as PosterService;

/**
 * 推广二维码
 * Class Poster
 * @package app\api\controller\user\dealer
 */
class Poster extends Controller
{
    /**
     * 获取推广二维码
     * @param string $channel
     * @return Json
     * @throws \cores\exception\BaseException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function qrcode(string $channel = 'H5'): Json
    {
        // 分销商用户详情
        $dealer = DealerUserModel::detail(UserService::getCurrentLoginUserId());
        // 生成二维码
        $Qrcode = new PosterService($dealer, $channel);
        return $this->renderSuccess(['imageUrl' => $Qrcode->getImage()]);
    }
}