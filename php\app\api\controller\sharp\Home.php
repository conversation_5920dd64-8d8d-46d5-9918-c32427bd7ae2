<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2023 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\api\controller\sharp;

use think\response\Json;
use app\api\controller\Controller;
use app\api\service\sharp\Active as ActiveService;

/**
 * 整点秒杀-秒杀首页
 * Class Home
 * @package app\api\controller\sharp
 */
class Home extends Controller
{
    /**
     * 秒杀活动首页
     * @return Json
     * @throws \cores\exception\BaseException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function data(): Json
    {
        // 获取秒杀活动会场首页数据
        $service = new ActiveService;
        $data = $service->getHallHome();
        return $this->renderSuccess($data);
    }
}