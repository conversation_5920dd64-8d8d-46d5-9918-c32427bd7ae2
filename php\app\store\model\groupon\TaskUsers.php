<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\store\model\groupon;

use app\common\model\groupon\TaskUsers as TaskUsersModel;

/**
 * 拼团拼单成员模型
 * Class TaskUsers
 * @package app\store\model\groupon
 */
class TaskUsers extends TaskUsersModel
{
    /**
     * 获取列表数据
     * @param int $taskId 拼单ID
     * @return \think\Paginator
     * @throws \think\db\exception\DbException
     */
    public function getList(int $taskId): \think\Paginator
    {
        return $this->with(['user.avatar', 'robot.avatar'])
            ->where('task_id', '=', $taskId)
            ->order(['create_time' => 'desc'])
            ->paginate(15);
    }
}