{"remainingRequest": "D:\\2025\\gaodux\\gaodux5\\vue\\store\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025\\gaodux\\gaodux5\\vue\\store\\src\\views\\page\\modules\\phone\\Phone.vue?vue&type=template&id=714e16ff&scoped=true&", "dependencies": [{"path": "D:\\2025\\gaodux\\gaodux5\\vue\\store\\src\\views\\page\\modules\\phone\\Phone.vue", "mtime": 1747065600000}, {"path": "D:\\2025\\gaodux\\gaodux5\\vue\\store\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751804606056}, {"path": "D:\\2025\\gaodux\\gaodux5\\vue\\store\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751804606056}, {"path": "D:\\2025\\gaodux\\gaodux5\\vue\\store\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751804605661}, {"path": "D:\\2025\\gaodux\\gaodux5\\vue\\store\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1751804675994}, {"path": "D:\\2025\\gaodux\\gaodux5\\vue\\store\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751804606056}, {"path": "D:\\2025\\gaodux\\gaodux5\\vue\\store\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751804675994}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}