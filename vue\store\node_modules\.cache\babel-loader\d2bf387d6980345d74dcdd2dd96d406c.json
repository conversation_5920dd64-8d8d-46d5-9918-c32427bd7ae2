{"remainingRequest": "D:\\2025\\gaodux\\gaodux5\\vue\\store\\node_modules\\babel-loader\\lib\\index.js!D:\\2025\\gaodux\\gaodux5\\vue\\store\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\2025\\gaodux\\gaodux5\\vue\\store\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025\\gaodux\\gaodux5\\vue\\store\\src\\views\\page\\modules\\phone\\Phone.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\2025\\gaodux\\gaodux5\\vue\\store\\src\\views\\page\\modules\\phone\\Phone.vue", "mtime": 1747065600000}, {"path": "D:\\2025\\gaodux\\gaodux5\\vue\\store\\babel.config.js", "mtime": 1656432000000}, {"path": "D:\\2025\\gaodux\\gaodux5\\vue\\store\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751804606056}, {"path": "D:\\2025\\gaodux\\gaodux5\\vue\\store\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751804605661}, {"path": "D:\\2025\\gaodux\\gaodux5\\vue\\store\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751804606056}, {"path": "D:\\2025\\gaodux\\gaodux5\\vue\\store\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751804675994}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IFByb3BUeXBlcyBmcm9tICdhbnQtZGVzaWduLXZ1ZS9lcy9fdXRpbC92dWUtdHlwZXMnOwppbXBvcnQgZHJhZ2dhYmxlIGZyb20gJ3Z1ZWRyYWdnYWJsZSc7CmltcG9ydCB7IGluQXJyYXkgfSBmcm9tICdAL3V0aWxzL3V0aWwnOwppbXBvcnQgKiBhcyBQYWdlSWNvbiBmcm9tICdAL2Fzc2V0cy9pY29ucy9wYWdlJzsKaW1wb3J0ICogYXMgSWNvbiBmcm9tICdAL2NvcmUvaWNvbnMnOwppbXBvcnQgeyBVVGFnIH0gZnJvbSAnLi9tb2R1bGVzJzsKCi8vIOemgeatouaLluaLveeahOe7hOS7tgp2YXIgdW5kcmFnTGlzdCA9IFsnc2VydmljZSddOwpleHBvcnQgZGVmYXVsdCB7CiAgcHJvcHM6IHsKICAgIC8vIOmhtemdouaVsOaNrgogICAgZGF0YTogUHJvcFR5cGVzLm9iamVjdC5kZWYoe30pLAogICAgLy8g5b2T5YmN6YCJ5Lit55qE5YWD57Sg57Si5byVCiAgICBzZWxlY3RlZEluZGV4OiBQcm9wVHlwZXMub25lT2ZUeXBlKFtQcm9wVHlwZXMubnVtYmVyLCBQcm9wVHlwZXMuc3RyaW5nXSkuZGVmKDApCiAgfSwKICBjb21wb25lbnRzOiB7CiAgICBkcmFnZ2FibGU6IGRyYWdnYWJsZSwKICAgIFVUYWc6IFVUYWcKICB9LAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICAvLyDnpoHmraLmi5bmi73nmoTnu4Tku7YKICAgICAgdW5kcmFnTGlzdDogdW5kcmFnTGlzdAogICAgfTsKICB9LAogIGNvbXB1dGVkOiB7fSwKICBiZWZvcmVDcmVhdGU6IGZ1bmN0aW9uIGJlZm9yZUNyZWF0ZSgpIHsKICAgIHRoaXMuSWNvbiA9IEljb247CiAgICB0aGlzLlBhZ2VJY29uID0gUGFnZUljb247CiAgICB0aGlzLmluQXJyYXkgPSBpbkFycmF5OwogIH0sCiAgbWV0aG9kczogewogICAgLyoqDQogICAgICog5pON5L2c5bel5YW35qCPDQogICAgICogQHBhcmFtIGl0ZW0NCiAgICAgKiBAcGFyYW0gaW5kZXgNCiAgICAgKi8KICAgIGFjdGlvblRvb2xzOiBmdW5jdGlvbiBhY3Rpb25Ub29scyhpdGVtLCBpbmRleCkgewogICAgICB2YXIgaXRlbXMgPSB0aGlzLmRhdGEuaXRlbXM7CiAgICAgIHZhciBpc1VuZHJhZyA9IGluQXJyYXkoaXRlbS50eXBlLCB1bmRyYWdMaXN0KTsKICAgICAgcmV0dXJuIFt7CiAgICAgICAgdGl0bGU6ICfkuIrnp7snLAogICAgICAgIHR5cGU6ICd1cCcsCiAgICAgICAgZGlzYWJsZWQ6IGlzVW5kcmFnIHx8IGluZGV4ID09IDAgfHwgaW5BcnJheShpdGVtc1tpbmRleCAtIDFdLnR5cGUsIHVuZHJhZ0xpc3QpCiAgICAgIH0sIHsKICAgICAgICB0aXRsZTogJ+S4i+enuycsCiAgICAgICAgdHlwZTogJ2Rvd24nLAogICAgICAgIGRpc2FibGVkOiBpc1VuZHJhZyB8fCBpdGVtcy5sZW5ndGggPD0gaW5kZXggKyAxIHx8IGluQXJyYXkoaXRlbXNbaW5kZXggKyAxXS50eXBlLCB1bmRyYWdMaXN0KQogICAgICB9LCB7CiAgICAgICAgdGl0bGU6ICflpI3liLYnLAogICAgICAgIHR5cGU6ICdjb3B5JywKICAgICAgICBkaXNhYmxlZDogZmFsc2UKICAgICAgfSwgewogICAgICAgIHRpdGxlOiAn5Yig6ZmkJywKICAgICAgICB0eXBlOiAnZGVsZXRlJywKICAgICAgICBkaXNhYmxlZDogZmFsc2UKICAgICAgfV07CiAgICB9LAogICAgLyoqDQogICAgICog5ouW5YqoZGl55YWD57Sg5pu05paw5b2T5YmN57Si5byVDQogICAgICogQHBhcmFtIGUNCiAgICAgKi8KICAgIGhhbmRlbERyYWdJdGVtOiBmdW5jdGlvbiBoYW5kZWxEcmFnSXRlbShlKSB7CiAgICAgIHRoaXMuJGVtaXQoJ29uRWRpdGVyJywgZS5uZXdJbmRleCk7CiAgICB9LAogICAgLyoqDQogICAgICog54K55Ye75b2T5YmN6YCJ5Lit55qERGl55YWD57SgDQogICAgICogQHBhcmFtIGluZGV4DQogICAgICovCiAgICBoYW5kZWxDbGlja0l0ZW06IGZ1bmN0aW9uIGhhbmRlbENsaWNrSXRlbShpbmRleCkgewogICAgICB0aGlzLiRlbWl0KCdvbkVkaXRlcicsIGluZGV4KTsKICAgIH0sCiAgICAvLyDmk43kvZzlt6XlhbfmoI/ngrnlh7vkuovku7YKICAgIGhhbmRsZUFjdGlvblRvb2xJdGVtOiBmdW5jdGlvbiBoYW5kbGVBY3Rpb25Ub29sSXRlbShhY0l0ZW0sIGluZGV4KSB7CiAgICAgICFhY0l0ZW0uZGlzYWJsZWQgJiYgdGhpcy4kZW1pdCgnb25BY3Rpb25JdGVtJywgYWNJdGVtLnR5cGUsIGluZGV4KTsKICAgIH0sCiAgICAvLyDmuLLmn5Pnu4Tku7blpJblsYLlrrnlmajnmoTmoLflvI8KICAgIHJlbmRlckl0ZW1TdHlsZTogZnVuY3Rpb24gcmVuZGVySXRlbVN0eWxlKGl0ZW0pIHsKICAgICAgaWYgKGl0ZW0udHlwZSA9PT0gJ3NlcnZpY2UnKSB7CiAgICAgICAgcmV0dXJuIHsKICAgICAgICAgIHBvc2l0aW9uOiAnYWJzb2x1dGUnLAogICAgICAgICAgcmlnaHQ6IGl0ZW0uc3R5bGUucmlnaHQgKyAncHgnLAogICAgICAgICAgYm90dG9tOiBpdGVtLnN0eWxlLmJvdHRvbSArICdweCcsCiAgICAgICAgICB6SW5kZXg6IDk5OQogICAgICAgIH07CiAgICAgIH0KICAgICAgcmV0dXJuIHt9OwogICAgfQogIH0KfTs="}, {"version": 3, "mappings": "AA8wCA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AAEA;EACAA;IACA;IACAC;IACA;IACAC;EACA;EACAC;IACAC;IACAC;EACA;EACAJ;IACA;MACA;MACAK;IACA;EACA;EACAC,WAEA;EACAC;IACA;IACA;IACA;EACA;EACAC;IAEA;AACA;AACA;AACA;AACA;IACAC;MACA;MACA;MACA,QACA;QACAC;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,EACA;IACA;IAEA;AACA;AACA;AACA;IACAC;MACA;IACA;IAEA;AACA;AACA;AACA;IACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;QACA;UACAC;UACAC;UACAC;UACAC;QACA;MACA;MACA;IACA;EAEA;AACA", "names": ["props", "data", "selectedIndex", "components", "draggable", "UTag", "undragList", "computed", "beforeCreate", "methods", "actionTools", "title", "type", "disabled", "handelDragItem", "handelClickItem", "handleActionToolItem", "renderItemStyle", "position", "right", "bottom", "zIndex"], "sourceRoot": "src/views/page/modules/phone", "sources": ["Phone.vue"], "sourcesContent": ["<template>\r\n  <div ref=\"phone-content\" class=\"phone-content\">\r\n    <!-- 顶部导航栏 -->\r\n    <div\r\n      class=\"phone-top optional\"\r\n      :class=\"{ selected: 'page' === selectedIndex, [data.page.style.titleTextColor]: true }\"\r\n      :style=\"{ backgroundColor: data.page.style.titleBackgroundColor }\"\r\n      @click=\"handelClickItem('page')\"\r\n    >\r\n      <p\r\n        class=\"title\"\r\n        :style=\"{ color: data.page.style.titleTextColor }\"\r\n      >{{ data.page.params.title }}</p>\r\n    </div>\r\n    <!-- 内容区域 -->\r\n    <div class=\"phone-main\">\r\n      <draggable\r\n        :list=\"data.items\"\r\n        class=\"content\"\r\n        @update=\"handelDragItem\"\r\n        v-bind=\"{ animation: 120, filter: '.undrag' }\"\r\n      >\r\n        <!-- 内容元素 -->\r\n        <div\r\n          class=\"devise-item optional\"\r\n          v-for=\"(item, index) in data.items\"\r\n          :key=\"index\"\r\n          @click=\"handelClickItem(index)\"\r\n          :class=\"{ selected: index === selectedIndex, undrag: inArray(item.type, undragList) }\"\r\n          :style=\"renderItemStyle(item)\"\r\n        >\r\n          <!-- 轮播图 -->\r\n          <div\r\n            v-if=\"item.type == 'banner'\"\r\n            class=\"diy-banner\"\r\n            :style=\"{ padding: `${item.style.paddingTop}px ${item.style.paddingLeft}px`, background: item.style.background }\"\r\n          >\r\n            <div\r\n              class=\"swiper-item\"\r\n              v-show=\"dataIdx <= 1\"\r\n              v-for=\"(dataItem, dataIdx) in item.data\"\r\n              :key=\"`${index}_${dataIdx}_img`\"\r\n              :style=\"{ borderRadius: `${item.style.borderRadius}px` }\"\r\n            >\r\n              <img class=\"image\" :src=\"dataItem.imgUrl\" />\r\n            </div>\r\n            <div\r\n              class=\"indicator-dots\"\r\n              :class=\"item.style.btnShape\"\r\n              :style=\"{ '--padding-top': `${item.style.paddingTop}px` }\"\r\n            >\r\n              <div\r\n                v-for=\"(dataItem, dataIdx) in item.data\"\r\n                :key=\"`${index}_${dataIdx}_dots`\"\r\n                :style=\"{ background: item.style.btnColor }\"\r\n                class=\"dots-item\"\r\n              ></div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 图片组 -->\r\n          <div\r\n            v-else-if=\"item.type == 'image'\"\r\n            class=\"diy-image\"\r\n            :style=\"{ padding: `${item.style.paddingTop}px ${item.style.paddingLeft}px`, background: item.style.background }\"\r\n          >\r\n            <div\r\n              class=\"item-image\"\r\n              v-for=\"(dataItm, dataIdx) in item.data\"\r\n              :key=\"`${index}_${dataIdx}`\"\r\n              :style=\"{ marginBottom: `${item.style.itemMargin}px`, borderRadius: `${item.style.borderRadius}px` }\"\r\n            >\r\n              <img class=\"image\" :src=\"dataItm.imgUrl\" />\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 图片橱窗 -->\r\n          <div\r\n            v-else-if=\"item.type == 'window'\"\r\n            class=\"diy-window\"\r\n            :style=\"{ background: item.style.background, padding: `${item.style.paddingTop}px ${item.style.paddingLeft}px` }\"\r\n          >\r\n            <ul\r\n              v-if=\"item.style.layout > -1\"\r\n              class=\"data-list clearfix\"\r\n              :class=\"`avg-sm-${item.style.layout}`\"\r\n            >\r\n              <li\r\n                v-for=\"(window, dataIdx) in item.data\"\r\n                :key=\"`${index}_${dataIdx}`\"\r\n                class=\"data-item\"\r\n                :style=\"{ padding: `${item.style.paddingTop}px ${item.style.paddingLeft}px` }\"\r\n              >\r\n                <div class=\"item-image\">\r\n                  <img class=\"image\" :src=\"window.imgUrl\" />\r\n                </div>\r\n              </li>\r\n            </ul>\r\n            <div class=\"display\" v-else>\r\n              <div\r\n                class=\"display-left\"\r\n                :style=\"{ padding: `${item.style.paddingTop}px ${item.style.paddingLeft}px` }\"\r\n              >\r\n                <img class=\"image\" :src=\"item.data[0].imgUrl\" />\r\n              </div>\r\n              <div class=\"display-right\">\r\n                <div\r\n                  v-if=\"item.data.length >= 2\"\r\n                  class=\"display-right1\"\r\n                  :style=\"{ padding: `${item.style.paddingTop}px ${item.style.paddingLeft}px` }\"\r\n                >\r\n                  <img class=\"image\" :src=\"item.data[1].imgUrl\" />\r\n                </div>\r\n                <div class=\"display-right2\">\r\n                  <div\r\n                    v-if=\"item.data.length >= 3\"\r\n                    class=\"left\"\r\n                    :style=\"{ padding: `${item.style.paddingTop}px ${item.style.paddingLeft}px` }\"\r\n                  >\r\n                    <img class=\"image\" :src=\"item.data[2].imgUrl\" />\r\n                  </div>\r\n                  <div\r\n                    v-if=\"item.data.length >= 4\"\r\n                    class=\"right\"\r\n                    :style=\"{ padding: `${item.style.paddingTop}px ${item.style.paddingLeft}px` }\"\r\n                  >\r\n                    <img class=\"image\" :src=\"item.data[3].imgUrl\" />\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 热区组 -->\r\n          <div\r\n            v-else-if=\"item.type == 'hotZone'\"\r\n            class=\"diy-hotZone\"\r\n            :style=\"{ padding: `${item.style.paddingTop}px ${item.style.paddingLeft}px`, background: item.style.background }\"\r\n          >\r\n            <div class=\"bg-image\" :style=\"{ borderRadius: `${item.style.borderRadius}px` }\">\r\n              <img class=\"image\" :src=\"item.data.imgUrl\" />\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 视频组 -->\r\n          <div\r\n            v-else-if=\"item.type == 'video'\"\r\n            class=\"diy-video\"\r\n            :style=\"{ padding: `${item.style.paddingTop}px ${item.style.paddingLeft}px`, background: item.style.background }\"\r\n          >\r\n            <video\r\n              :style=\"{ height: `${item.style.height}px` }\"\r\n              :src=\"item.params.videoUrl\"\r\n              :poster=\"item.params.poster\"\r\n              controls\r\n            >您的浏览器不支持 video 标签</video>\r\n          </div>\r\n\r\n          <!-- 文章组 -->\r\n          <div v-else-if=\"item.type == 'article'\" class=\"diy-article\">\r\n            <div\r\n              class=\"article-item\"\r\n              v-for=\"(dataItm, dataIdx) in (item.params.source == 'choice' && item.data.length ? item.data : item.defaultData)\"\r\n              :key=\"`${index}_${dataIdx}`\"\r\n              :class=\"`show-type__${dataItm.show_type}`\"\r\n            >\r\n              <!-- 小图模式 -->\r\n              <template v-if=\"dataItm.show_type == 10\">\r\n                <div class=\"article-item__left flex-box\">\r\n                  <div class=\"article-item__title twoline-hide\">\r\n                    <span class=\"article-title\">{{ dataItm.title }}</span>\r\n                  </div>\r\n                  <div class=\"article-item__footer\">\r\n                    <span class=\"article-views\">{{ dataItm.views_num }}次浏览</span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"article-item__image\">\r\n                  <img class=\"image\" :src=\"dataItm.image\" alt />\r\n                </div>\r\n              </template>\r\n              <!-- 大图模式 -->\r\n              <template v-if=\"dataItm.show_type == 20\">\r\n                <div class=\"article-item__title\">\r\n                  <span class=\"article-title\">{{ dataItm.title }}</span>\r\n                </div>\r\n                <div class=\"article-item__image\">\r\n                  <img class=\"image\" :src=\"dataItm.image\" />\r\n                </div>\r\n                <div class=\"article-item__footer\">\r\n                  <span class=\"article-views\">{{ dataItm.views_num }}次浏览</span>\r\n                </div>\r\n              </template>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 搜索栏 -->\r\n          <div\r\n            v-else-if=\"item.type == 'search'\"\r\n            class=\"diy-search\"\r\n            :class=\"{ sticky: item.params.sticky }\"\r\n            :style=\"{ background: item.style.background, padding: `${item.style.paddingY}px ${item.style.paddingX}px` }\"\r\n          >\r\n            <div class=\"inner\" :class=\"item.style.searchStyle\">\r\n              <div\r\n                class=\"search-input\"\r\n                :style=\"{ textAlign: item.style.textAlign, background: item.style.searchBg, color: item.style.searchFontColor }\"\r\n              >\r\n                <a-icon class=\"search-icon\" :component=\"PageIcon.search\" />\r\n                <span>{{ item.params.placeholder }}</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 店铺公告 -->\r\n          <div\r\n            v-else-if=\"item.type == 'notice'\"\r\n            class=\"diy-notice\"\r\n            :style=\"{ padding: `${item.style.paddingTop}px 0` }\"\r\n          >\r\n            <div\r\n              class=\"notice-body\"\r\n              :style=\"{ background: item.style.background, color: item.style.textColor }\"\r\n            >\r\n              <div class=\"notice__icon\">\r\n                <a-icon class=\"notice-icon\" :component=\"PageIcon.volumeFill\" />\r\n              </div>\r\n              <div\r\n                class=\"notice__text flex-box oneline-hide\"\r\n                :style=\"{ fontSize: `${item.style.fontSize}px` }\"\r\n              >\r\n                <span>{{ item.params.text }}</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 导航组 -->\r\n          <div\r\n            v-else-if=\"item.type == 'navBar'\"\r\n            class=\"diy-navBar\"\r\n            :style=\"{ padding: `${item.style.paddingTop}px 0`, background: item.style.background, color: item.style.textColor }\"\r\n          >\r\n            <div class=\"data-list clearfix\" :class=\"`avg-sm-${item.style.rowsNum}`\">\r\n              <div\r\n                class=\"item-nav\"\r\n                v-for=\"(dataItm, dataIdx) in item.data\"\r\n                :key=\"`${index}_${dataIdx}`\"\r\n              >\r\n                <div class=\"item-image\">\r\n                  <img\r\n                    class=\"image\"\r\n                    :style=\"{ width: `${item.style.imageSize}px`, height: `${item.style.imageSize}px` }\"\r\n                    :src=\"dataItm.imgUrl\"\r\n                  />\r\n                </div>\r\n                <p class=\"item-text oneline-hide\">{{ dataItm.text }}</p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 商品组 -->\r\n          <div\r\n            v-else-if=\"item.type == 'goods'\"\r\n            class=\"diy-goods\"\r\n            :style=\"{ background: item.style.background, padding: `${item.style.paddingY}px ${item.style.paddingX}px` }\"\r\n          >\r\n            <div\r\n              class=\"goods-list\"\r\n              :class=\"[`display-${item.style.display}`, `column-${item.style.column}` ]\"\r\n              :style=\"{ marginBottom: `-${item.style.itemMargin}px` }\"\r\n            >\r\n              <div\r\n                class=\"goods-item\"\r\n                v-for=\"(dataItm, dataIdx) in (item.params.source == 'choice' && item.data.length ? item.data : item.defaultData)\"\r\n                :key=\"`${index}_${dataIdx}`\"\r\n                :class=\"[`display-${item.style.cardType}`]\"\r\n                :style=\"{ marginBottom: `${item.style.itemMargin}px`, borderRadius: `${item.style.borderRadius}px` }\"\r\n              >\r\n                <!-- 单列商品 -->\r\n                <template v-if=\"item.style.column == 1\">\r\n                  <div class=\"flex\">\r\n                    <!-- 商品图片 -->\r\n                    <div class=\"goods-item-left\">\r\n                      <img class=\"image\" :src=\"dataItm.goods_image\" />\r\n                    </div>\r\n                    <div class=\"goods-item-right\">\r\n                      <div class=\"goods-info\">\r\n                        <div\r\n                          v-if=\"inArray('goodsName', item.style.show)\"\r\n                          class=\"goods-name\"\r\n                          :class=\"[ item.style.goodsNameRows == 'two' ? 'twoline-hide' : 'oneline-hide', `row-${item.style.goodsNameRows}` ]\"\r\n                        >{{ dataItm.goods_name }}</div>\r\n                        <div v-if=\"inArray('sellingPoint', item.style.show)\" class=\"goods-selling\">\r\n                          <span\r\n                            class=\"selling oneline-hide\"\r\n                            :style=\"{ color: item.style.sellingColor }\"\r\n                          >{{ dataItm.selling_point }}</span>\r\n                        </div>\r\n                        <div\r\n                          v-if=\"inArray('goodsSales', item.style.show)\"\r\n                          class=\"goods-sales oneline-hide\"\r\n                        >\r\n                          <span class=\"sales\">已售{{ dataItm.goods_sales }}</span>\r\n                        </div>\r\n                        <div class=\"footer\">\r\n                          <div\r\n                            class=\"goods-price oneline-hide\"\r\n                            :style=\"{ color: item.style.priceColor }\"\r\n                          >\r\n                            <template v-if=\"inArray('goodsPrice', item.style.show)\">\r\n                              <span class=\"unit\">￥</span>\r\n                              <span class=\"value\">{{ dataItm.goods_price_min }}</span>\r\n                            </template>\r\n                            <span v-if=\"inArray('linePrice', item.style.show)\" class=\"line-price\">\r\n                              <span class=\"unit\">￥</span>\r\n                              <span class=\"value\">{{ dataItm.line_price_min }}</span>\r\n                            </span>\r\n                          </div>\r\n                          <div\r\n                            v-show=\"inArray('cartBtn', item.style.show) && item.style.column < 3\"\r\n                            class=\"action\"\r\n                          >\r\n                            <div class=\"btn-cart\" :style=\"{ color: item.style.btnCartColor }\">\r\n                              <a-icon\r\n                                class=\"cart-icon\"\r\n                                :component=\"PageIcon[`jiagou${item.style.btnCartStyle}`]\"\r\n                              />\r\n                            </div>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </template>\r\n                <!-- 两列/三列 -->\r\n                <template v-else>\r\n                  <div class=\"goods-image\">\r\n                    <img class=\"image\" :src=\"dataItm.goods_image\" />\r\n                  </div>\r\n                  <div class=\"goods-info\">\r\n                    <div\r\n                      v-if=\"inArray('goodsName', item.style.show)\"\r\n                      class=\"goods-name\"\r\n                      :class=\"[ item.style.goodsNameRows == 'two' ? 'twoline-hide' : 'oneline-hide', `row-${item.style.goodsNameRows}` ]\"\r\n                    >{{ dataItm.goods_name }}</div>\r\n                    <div v-if=\"inArray('sellingPoint', item.style.show)\" class=\"goods-selling\">\r\n                      <span\r\n                        class=\"selling oneline-hide\"\r\n                        :style=\"{ color: item.style.sellingColor }\"\r\n                      >{{ dataItm.selling_point }}</span>\r\n                    </div>\r\n                    <div\r\n                      v-if=\"inArray('goodsSales', item.style.show)\"\r\n                      class=\"goods-sales oneline-hide\"\r\n                    >\r\n                      <span class=\"sales\">已售{{ dataItm.goods_sales }}</span>\r\n                    </div>\r\n                    <div class=\"footer\">\r\n                      <div\r\n                        v-if=\"inArray('goodsPrice', item.style.show)\"\r\n                        class=\"goods-price oneline-hide\"\r\n                        :style=\"{ color: item.style.priceColor }\"\r\n                      >\r\n                        <span class=\"unit\">￥</span>\r\n                        <span class=\"value\">{{ dataItm.goods_price_min }}</span>\r\n                        <span v-if=\"inArray('linePrice', item.style.show)\" class=\"line-price\">\r\n                          <span class=\"unit\">￥</span>\r\n                          <span class=\"value\">{{ dataItm.line_price_min }}</span>\r\n                        </span>\r\n                      </div>\r\n                      <div\r\n                        v-show=\"inArray('cartBtn', item.style.show) && item.style.column < 3\"\r\n                        class=\"action\"\r\n                      >\r\n                        <div class=\"btn-cart\" :style=\"{ color: item.style.btnCartColor }\">\r\n                          <a-icon\r\n                            class=\"cart-icon\"\r\n                            :component=\"PageIcon[`jiagou${item.style.btnCartStyle}`]\"\r\n                          />\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </template>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 辅助空白 -->\r\n          <div\r\n            v-else-if=\"item.type == 'blank'\"\r\n            class=\"diy-blank\"\r\n            :style=\"{ height: `${item.style.height}px` , background: item.style.background }\"\r\n          ></div>\r\n\r\n          <!-- 辅助线 -->\r\n          <div\r\n            v-else-if=\"item.type == 'guide'\"\r\n            class=\"diy-guide\"\r\n            :style=\"{ padding: `${item.style.paddingTop}px 0`, background: item.style.background }\"\r\n          >\r\n            <p\r\n              class=\"line\"\r\n              :style=\"{\r\n                borderTopWidth: item.style.lineHeight + 'px',\r\n                borderTopColor: item.style.lineColor,\r\n                borderTopStyle: item.style.lineStyle\r\n              }\"\r\n            ></p>\r\n          </div>\r\n\r\n          <!-- 在线客服 -->\r\n          <div\r\n            v-else-if=\"item.type == 'service'\"\r\n            class=\"diy-service\"\r\n            :style=\"{ opacity: item.style.opacity / 100 }\"\r\n          >\r\n            <div class=\"service-icon\">\r\n              <img class=\"image\" :src=\"item.params.image\" alt />\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 富文本 -->\r\n          <div\r\n            v-else-if=\"item.type == 'richText'\"\r\n            class=\"diy-richText\"\r\n            :style=\"{ background: item.style.background, padding: `${item.style.paddingTop}px ${item.style.paddingLeft}px` }\"\r\n            v-html=\"item.params.content\"\r\n          ></div>\r\n\r\n          <!-- 关注公众号 -->\r\n          <div v-else-if=\"item.type == 'officialAccount'\" class=\"diy-officialAccount\">\r\n            <div class=\"item-top\">\r\n              <span>关联的公众号</span>\r\n            </div>\r\n            <div class=\"item-content\">\r\n              <div class=\"item-cont-avatar\">\r\n                <img class=\"image\" src=\"~@/assets/img/circular.png\" alt />\r\n              </div>\r\n              <div class=\"item-cont-public\">\r\n                <div class=\"public-name\">\r\n                  <span>公众号名称</span>\r\n                </div>\r\n                <div class=\"public-describe\">\r\n                  <span>公众号简介公众号简介公众号简介</span>\r\n                </div>\r\n              </div>\r\n              <div class=\"item-cont-active\">\r\n                <div class=\"active-btn\">\r\n                  <span>关注</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 线下门店 -->\r\n          <div\r\n            v-else-if=\"item.type == 'shop'\"\r\n            class=\"diy-shop\"\r\n            :style=\"{ background: item.style.background }\"\r\n          >\r\n            <div\r\n              class=\"shop-item\"\r\n              v-for=\"(shop, idx) in (item.params.source == 'choice' && item.data.length ? item.data : item.defaultData)\"\r\n              :key=\"idx\"\r\n            >\r\n              <div v-if=\"inArray('logo', item.style.show)\" class=\"shop-item__logo\">\r\n                <img class=\"image\" :src=\"shop.logo_url\" alt=\"门店logo\" />\r\n              </div>\r\n              <div class=\"shop-item__content\">\r\n                <div class=\"shop-item__title\">\r\n                  <span>{{ shop.shop_name }}</span>\r\n                </div>\r\n                <div\r\n                  v-if=\"inArray('address', item.style.show)\"\r\n                  class=\"shop-item__address oneline-hide\"\r\n                >\r\n                  <span>门店地址：{{ shop.region.province }}{{ shop.region.city }}{{ shop.region.region }}{{ shop.address }}</span>\r\n                </div>\r\n                <div v-if=\"inArray('phone', item.style.show)\" class=\"shop-item__phone\">\r\n                  <span>联系电话：{{ shop.phone }}</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 砍价商品 -->\r\n          <div\r\n            v-else-if=\"item.type == 'bargain'\"\r\n            class=\"diy-bargain\"\r\n            :style=\"{ background: item.style.background, padding: `${item.style.paddingY}px ${item.style.paddingX}px` }\"\r\n          >\r\n            <div class=\"container\" :style=\"{ borderRadius: `${item.style.borderRadius}px` }\">\r\n              <!-- 标题栏 -->\r\n              <div\r\n                v-if=\"item.params.title.enable\"\r\n                class=\"title-bar\"\r\n                :style=\"{ backgroundImage: `url(${item.params.title.bgImage})` }\"\r\n              >\r\n                <div class=\"title-bar--left\">\r\n                  <div class=\"title-image\">\r\n                    <img class=\"image\" :src=\"item.params.title.image\" alt />\r\n                  </div>\r\n                  <div class=\"title-desc\" :style=\"{ color: item.style.title.descTextColor }\">\r\n                    <span>{{ item.params.title.descText }}</span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"title-bar--right\">\r\n                  <div class=\"title-more\" :style=\"{ color: item.style.title.rightTextColor }\">\r\n                    <span class=\"more-text\">{{ item.params.title.rightText }}</span>\r\n                    <span class=\"more-arrow\">\r\n                      <a-icon :component=\"Icon.arrowRight\" />\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <!-- 商品列表 -->\r\n              <div\r\n                class=\"goods-list\"\r\n                :class=\"[`display-${item.params.display}`, `column-${item.params.column}`]\"\r\n                :style=\"{ marginBottom: `-${item.style.content.itemMargin}px`, padding: `${item.style.content.itemPaddingY}px 10px` }\"\r\n              >\r\n                <div\r\n                  class=\"goods-item\"\r\n                  v-for=\"(dataItm, dataIdx) in (item.params.source == 'choice' && item.data.length ? item.data : item.defaultData)\"\r\n                  :key=\"`${index}_${dataIdx}`\"\r\n                  :style=\"{ marginBottom: `${item.style.content.itemMargin}px` }\"\r\n                >\r\n                  <!-- 单列商品 -->\r\n                  <template v-if=\"item.params.column == 1\">\r\n                    <div class=\"flex\">\r\n                      <!-- 商品图片 -->\r\n                      <div class=\"goods-item-left\">\r\n                        <img\r\n                          :style=\"{ borderRadius: `${item.style.content.borderRadius}px` }\"\r\n                          class=\"image\"\r\n                          :src=\"dataItm.goods_image\"\r\n                        />\r\n                      </div>\r\n                      <div class=\"goods-item-right\">\r\n                        <div class=\"goods-info\">\r\n                          <div\r\n                            v-if=\"inArray('goodsName', item.params.show)\"\r\n                            class=\"goods-name\"\r\n                            :class=\"[ item.style.content.goodsNameRows == 'two' ? 'twoline-hide' : 'oneline-hide', `row-${item.style.goodsNameRows}` ]\"\r\n                          >{{ dataItm.goods_name }}</div>\r\n                          <!-- 参与的用户 -->\r\n                          <div v-if=\"inArray('peoples', item.params.show)\" class=\"peoples\">\r\n                            <div class=\"user-list\">\r\n                              <div\r\n                                v-for=\"(help, hidx) in item.demo.helpList\"\r\n                                :key=\"hidx\"\r\n                                class=\"user-item-avatar\"\r\n                              >\r\n                                <img class=\"image\" :src=\"help.user.avatar_url\" />\r\n                              </div>\r\n                            </div>\r\n                            <div class=\"people__text\">\r\n                              <span>{{ item.demo.helpsCount }}人正在砍价</span>\r\n                            </div>\r\n                          </div>\r\n                          <div class=\"footer\">\r\n                            <div\r\n                              class=\"goods-price\"\r\n                              :style=\"{ color: item.style.content.priceColor }\"\r\n                            >\r\n                              <div class=\"goods-price--row oneline-hide\">\r\n                                <template v-if=\"inArray('floorPrice', item.params.show)\">\r\n                                  <span class=\"small\">底价</span>\r\n                                  <span class=\"unit\">￥</span>\r\n                                  <span class=\"value\">{{ dataItm.floor_price }}</span>\r\n                                </template>\r\n                              </div>\r\n                              <div class=\"goods-price--row oneline-hide\">\r\n                                <span\r\n                                  v-if=\"inArray('originalPrice', item.params.show)\"\r\n                                  class=\"line-price\"\r\n                                >\r\n                                  <span class=\"unit\">￥</span>\r\n                                  <span class=\"value\">{{ dataItm.original_price }}</span>\r\n                                </span>\r\n                              </div>\r\n                            </div>\r\n                            <div\r\n                              v-show=\"inArray('mainBtn', item.params.show) && item.params.column < 3\"\r\n                              class=\"action\"\r\n                            >\r\n                              <div\r\n                                class=\"btn-main\"\r\n                                :style=\"{ color: item.style.content.mainBtnTextColor,\r\n                                  background: `linear-gradient(to right, ${item.style.content.mainBtnBgColor})` }\"\r\n                              >\r\n                                <span>{{ item.params.mainBtnText }}</span>\r\n                              </div>\r\n                            </div>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </template>\r\n                  <!-- 两列/三列 -->\r\n                  <template v-else>\r\n                    <div\r\n                      class=\"goods-image\"\r\n                      :style=\"{ borderRadius: `${item.style.content.borderRadius}px` }\"\r\n                    >\r\n                      <img class=\"image\" :src=\"dataItm.goods_image\" />\r\n                    </div>\r\n                    <div class=\"goods-info\">\r\n                      <div\r\n                        v-if=\"inArray('goodsName', item.params.show)\"\r\n                        class=\"goods-name\"\r\n                        :class=\"[ item.style.content.goodsNameRows == 'two' ? 'twoline-hide' : 'oneline-hide', `row-${item.style.goodsNameRows}` ]\"\r\n                      >{{ dataItm.goods_name }}</div>\r\n                      <!-- <div v-if=\"inArray('sellingPoint', item.params.show)\" class=\"goods-selling\">\r\n                        <span\r\n                          class=\"selling oneline-hide\"\r\n                          :style=\"{ color: item.style.sellingColor }\"\r\n                        >{{ dataItm.selling_point }}</span>\r\n                      </div>\r\n                      <div\r\n                        v-if=\"inArray('goodsSales', item.params.show)\"\r\n                        class=\"goods-sales oneline-hide\"\r\n                      >\r\n                        <span class=\"sales\">已售{{ dataItm.goods_sales }}</span>\r\n                      </div>-->\r\n                      <div class=\"footer\">\r\n                        <div\r\n                          v-if=\"inArray('floorPrice', item.params.show)\"\r\n                          class=\"goods-price oneline-hide\"\r\n                          :style=\"{ color: item.style.content.priceColor }\"\r\n                        >\r\n                          <span class=\"unit\">￥</span>\r\n                          <span class=\"value\">{{ dataItm.floor_price }}</span>\r\n                          <span\r\n                            v-if=\"inArray('originalPrice', item.params.show)\"\r\n                            class=\"line-price\"\r\n                          >\r\n                            <span class=\"unit\">￥</span>\r\n                            <span class=\"value\">{{ dataItm.original_price }}</span>\r\n                          </span>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </template>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 拼团商品 -->\r\n          <div\r\n            v-else-if=\"item.type == 'groupon'\"\r\n            class=\"diy-groupon\"\r\n            :style=\"{ background: item.style.background, padding: `${item.style.paddingY}px ${item.style.paddingX}px` }\"\r\n          >\r\n            <div class=\"container\" :style=\"{ borderRadius: `${item.style.borderRadius}px` }\">\r\n              <!-- 标题栏 -->\r\n              <div\r\n                v-if=\"item.params.title.enable\"\r\n                class=\"title-bar\"\r\n                :style=\"{ backgroundImage: `url(${item.params.title.bgImage})` }\"\r\n              >\r\n                <div class=\"title-bar--left\">\r\n                  <div class=\"title-image\">\r\n                    <img class=\"image\" :src=\"item.params.title.image\" alt />\r\n                  </div>\r\n                  <div class=\"title-desc\" :style=\"{ color: item.style.title.descTextColor }\">\r\n                    <span>{{ item.params.title.descText }}</span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"title-bar--right\">\r\n                  <div class=\"title-more\" :style=\"{ color: item.style.title.rightTextColor }\">\r\n                    <span class=\"more-text\">{{ item.params.title.rightText }}</span>\r\n                    <span class=\"more-arrow\">\r\n                      <a-icon :component=\"Icon.arrowRight\" />\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <!-- 商品列表 -->\r\n              <div\r\n                class=\"goods-list\"\r\n                :class=\"[`display-${item.params.display}`, `column-${item.params.column}`]\"\r\n                :style=\"{ marginBottom: `-${item.style.content.itemMargin}px`, padding: `${item.style.content.itemPaddingY}px 10px` }\"\r\n              >\r\n                <div\r\n                  class=\"goods-item\"\r\n                  v-for=\"(dataItm, dataIdx) in (item.params.source == 'choice' && item.data.length ? item.data : item.defaultData)\"\r\n                  :key=\"`${index}_${dataIdx}`\"\r\n                  :style=\"{ marginBottom: `${item.style.content.itemMargin}px` }\"\r\n                >\r\n                  <!-- 单列商品 -->\r\n                  <template v-if=\"item.params.column == 1\">\r\n                    <div class=\"flex\">\r\n                      <!-- 商品图片 -->\r\n                      <div class=\"goods-item-left\">\r\n                        <img\r\n                          :style=\"{ borderRadius: `${item.style.content.borderRadius}px` }\"\r\n                          class=\"image\"\r\n                          :src=\"dataItm.goods_image\"\r\n                        />\r\n                      </div>\r\n                      <div class=\"goods-item-right\">\r\n                        <div class=\"goods-info\">\r\n                          <div\r\n                            v-if=\"inArray('goodsName', item.params.show)\"\r\n                            class=\"goods-name\"\r\n                            :class=\"[ item.style.content.goodsNameRows == 'two' ? 'twoline-hide' : 'oneline-hide', `row-${item.style.goodsNameRows}` ]\"\r\n                          >{{ dataItm.goods_name }}</div>\r\n                          <!-- 成团人数和销量 -->\r\n                          <div class=\"people-sales\">\r\n                            <div\r\n                              v-if=\"inArray('people', item.params.show)\"\r\n                              class=\"tag-item item-1\"\r\n                              :style=\"{ backgroundColor: item.style.content.tagColor }\"\r\n                            >\r\n                              <div class=\"item-bg\">\r\n                                <span>{{ dataItm.show_people }}人团</span>\r\n                              </div>\r\n                            </div>\r\n                            <div\r\n                              v-if=\"inArray('activeSales', item.params.show)\"\r\n                              class=\"tag-item item-2\"\r\n                              :style=\"{ backgroundColor: item.style.content.tagColor, color: item.style.content.tagColor }\"\r\n                            >\r\n                              <div class=\"item-bg\">\r\n                                <span>已拼{{ dataItm.active_sales }}件</span>\r\n                              </div>\r\n                            </div>\r\n                          </div>\r\n                          <div class=\"footer\">\r\n                            <div\r\n                              class=\"goods-price\"\r\n                              :style=\"{ color: item.style.content.priceColor }\"\r\n                            >\r\n                              <div class=\"goods-price--row oneline-hide\">\r\n                                <template v-if=\"inArray('grouponPrice', item.params.show)\">\r\n                                  <span class=\"small\">拼团价</span>\r\n                                  <span class=\"unit\">￥</span>\r\n                                  <span class=\"value\">{{ dataItm.groupon_price }}</span>\r\n                                </template>\r\n                              </div>\r\n                              <div class=\"goods-price--row oneline-hide\">\r\n                                <span\r\n                                  v-if=\"inArray('originalPrice', item.params.show)\"\r\n                                  class=\"line-price\"\r\n                                >\r\n                                  <span class=\"unit\">￥</span>\r\n                                  <span class=\"value\">{{ dataItm.original_price }}</span>\r\n                                </span>\r\n                              </div>\r\n                            </div>\r\n                            <div\r\n                              v-show=\"inArray('mainBtn', item.params.show) && item.params.column < 3\"\r\n                              class=\"action\"\r\n                            >\r\n                              <div\r\n                                class=\"btn-main\"\r\n                                :style=\"{ color: item.style.content.mainBtnTextColor,\r\n                                  background: `linear-gradient(to right, ${item.style.content.mainBtnBgColor})` }\"\r\n                              >\r\n                                <span>{{ item.params.mainBtnText }}</span>\r\n                              </div>\r\n                            </div>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </template>\r\n                  <!-- 两列/三列 -->\r\n                  <template v-else>\r\n                    <div\r\n                      class=\"goods-image\"\r\n                      :style=\"{ borderRadius: `${item.style.content.borderRadius}px` }\"\r\n                    >\r\n                      <img class=\"image\" :src=\"dataItm.goods_image\" />\r\n                    </div>\r\n                    <div class=\"goods-info\">\r\n                      <div\r\n                        v-if=\"inArray('goodsName', item.params.show)\"\r\n                        class=\"goods-name\"\r\n                        :class=\"[ item.style.content.goodsNameRows == 'two' ? 'twoline-hide' : 'oneline-hide', `row-${item.style.goodsNameRows}` ]\"\r\n                      >{{ dataItm.goods_name }}</div>\r\n                      <!-- <div v-if=\"inArray('sellingPoint', item.params.show)\" class=\"goods-selling\">\r\n                        <span\r\n                          class=\"selling oneline-hide\"\r\n                          :style=\"{ color: item.style.sellingColor }\"\r\n                        >{{ dataItm.selling_point }}</span>\r\n                      </div>\r\n                      <div\r\n                        v-if=\"inArray('goodsSales', item.params.show)\"\r\n                        class=\"goods-sales oneline-hide\"\r\n                      >\r\n                        <span class=\"sales\">已售{{ dataItm.goods_sales }}</span>\r\n                      </div>-->\r\n                      <div class=\"footer\">\r\n                        <div\r\n                          v-if=\"inArray('grouponPrice', item.params.show)\"\r\n                          class=\"goods-price oneline-hide\"\r\n                          :style=\"{ color: item.style.content.priceColor }\"\r\n                        >\r\n                          <span class=\"unit\">￥</span>\r\n                          <span class=\"value\">{{ dataItm.groupon_price }}</span>\r\n                          <span\r\n                            v-if=\"inArray('originalPrice', item.params.show)\"\r\n                            class=\"line-price\"\r\n                          >\r\n                            <span class=\"unit\">￥</span>\r\n                            <span class=\"value\">{{ dataItm.original_price }}</span>\r\n                          </span>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </template>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 整点秒杀 -->\r\n          <div\r\n            v-else-if=\"item.type == 'sharp'\"\r\n            class=\"diy-sharp\"\r\n            :style=\"{ background: item.style.background, padding: `${item.style.paddingY}px ${item.style.paddingX}px` }\"\r\n          >\r\n            <div class=\"container\" :style=\"{ borderRadius: `${item.style.borderRadius}px` }\">\r\n              <!-- 标题栏 -->\r\n              <div\r\n                v-if=\"item.params.title.enable\"\r\n                class=\"title-bar\"\r\n                :style=\"{ backgroundImage: `url(${item.params.title.bgImage})` }\"\r\n              >\r\n                <div class=\"title-bar--left\">\r\n                  <div class=\"title-image\">\r\n                    <img class=\"image\" :src=\"item.params.title.image\" alt />\r\n                  </div>\r\n                  <template v-if=\"item.params.title.showCountdown\">\r\n                    <div\r\n                      class=\"sharp-active-status\"\r\n                      :style=\"{ color: item.style.title.cdTextColor }\"\r\n                    >\r\n                      <span>{{ item.params.title.countdownText }}</span>\r\n                    </div>\r\n                    <div class=\"active-count-down\">\r\n                      <div class=\"clock flex\">\r\n                        <div\r\n                          class=\"clock-time\"\r\n                          :style=\"{ backgroundColor: item.style.title.cdNumBgColor, color: item.style.title.cdNumColor }\"\r\n                        >\r\n                          <span>00</span>\r\n                        </div>\r\n                        <div class=\"clock-symbol\" :style=\"{ color: item.style.title.cdTextColor }\">\r\n                          <span>:</span>\r\n                        </div>\r\n                        <div\r\n                          class=\"clock-time\"\r\n                          :style=\"{ backgroundColor: item.style.title.cdNumBgColor, color: item.style.title.cdNumColor }\"\r\n                        >\r\n                          <span>58</span>\r\n                        </div>\r\n                        <div class=\"clock-symbol\" :style=\"{ color: item.style.title.cdTextColor }\">\r\n                          <span>:</span>\r\n                        </div>\r\n                        <div\r\n                          class=\"clock-time\"\r\n                          :style=\"{ backgroundColor: item.style.title.cdNumBgColor, color: item.style.title.cdNumColor }\"\r\n                        >\r\n                          <span>04</span>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </template>\r\n                </div>\r\n                <div class=\"title-bar--right\">\r\n                  <div class=\"title-more\" :style=\"{ color: item.style.title.rightTextColor }\">\r\n                    <span class=\"more-text\">{{ item.params.title.rightText }}</span>\r\n                    <span class=\"more-arrow\">\r\n                      <a-icon :component=\"Icon.arrowRight\" />\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <!-- 商品列表 -->\r\n              <div\r\n                class=\"goods-list\"\r\n                :class=\"[`display-${item.params.display}`, `column-${item.params.column}`]\"\r\n                :style=\"{ marginBottom: `-${item.style.content.itemMargin}px`, padding: `${item.style.content.itemPaddingY}px 10px` }\"\r\n              >\r\n                <div\r\n                  class=\"goods-item\"\r\n                  v-for=\"(dataItm, dataIdx) in item.data\"\r\n                  :key=\"`${index}_${dataIdx}`\"\r\n                  :style=\"{ marginBottom: `${item.style.content.itemMargin}px` }\"\r\n                >\r\n                  <!-- 单列商品 -->\r\n                  <template v-if=\"item.params.column == 1\">\r\n                    <div class=\"flex\">\r\n                      <!-- 商品图片 -->\r\n                      <div class=\"goods-item-left\">\r\n                        <img\r\n                          :style=\"{ borderRadius: `${item.style.content.borderRadius}px` }\"\r\n                          class=\"image\"\r\n                          :src=\"dataItm.goods_image\"\r\n                        />\r\n                      </div>\r\n                      <div class=\"goods-item-right\">\r\n                        <div class=\"goods-info\">\r\n                          <div\r\n                            v-if=\"inArray('goodsName', item.params.show)\"\r\n                            class=\"goods-name\"\r\n                            :class=\"[ item.style.content.goodsNameRows == 'two' ? 'twoline-hide' : 'oneline-hide', `row-${item.style.goodsNameRows}` ]\"\r\n                          >{{ dataItm.goods_name }}</div>\r\n                          <!-- 抢购进度 -->\r\n                          <div v-if=\"inArray('progress', item.params.show)\" class=\"sharp-progress\">\r\n                            <div\r\n                              class=\"yoo-progress\"\r\n                              :style=\"{ background: `linear-gradient(to right, ${item.style.content.progressColor})` }\"\r\n                            >\r\n                              <div class=\"yoo-progress--filter\">\r\n                                <div\r\n                                  class=\"yoo-progress--portion\"\r\n                                  :style=\"{ width: `${dataItm.progress}%`,\r\n                                    background: `linear-gradient(to right, ${item.style.content.progressColor})` }\"\r\n                                ></div>\r\n                                <span class=\"yoo-progress--text\">{{ dataItm.progress }}%</span>\r\n                              </div>\r\n                            </div>\r\n                            <div\r\n                              class=\"sharp-sales oneline-hide\"\r\n                              :style=\"{ color: item.style.content.salesColor }\"\r\n                            >已抢{{ dataItm.sales_actual }}件</div>\r\n                          </div>\r\n                          <div class=\"footer\">\r\n                            <div\r\n                              class=\"goods-price\"\r\n                              :style=\"{ color: item.style.content.priceColor }\"\r\n                            >\r\n                              <div class=\"goods-price--row oneline-hide\">\r\n                                <template v-if=\"inArray('seckillPrice', item.params.show)\">\r\n                                  <span class=\"small\">秒杀价</span>\r\n                                  <span class=\"unit\">￥</span>\r\n                                  <span class=\"value\">{{ dataItm.seckill_price_min }}</span>\r\n                                </template>\r\n                              </div>\r\n                              <div class=\"goods-price--row oneline-hide\">\r\n                                <span\r\n                                  v-if=\"inArray('originalPrice', item.params.show)\"\r\n                                  class=\"line-price\"\r\n                                >\r\n                                  <span class=\"unit\">￥</span>\r\n                                  <span class=\"value\">{{ dataItm.original_price }}</span>\r\n                                </span>\r\n                              </div>\r\n                            </div>\r\n                            <div\r\n                              v-show=\"inArray('mainBtn', item.params.show) && item.params.column < 3\"\r\n                              class=\"action\"\r\n                            >\r\n                              <div\r\n                                class=\"btn-main\"\r\n                                :style=\"{ color: item.style.content.mainBtnTextColor,\r\n                                  background: `linear-gradient(to right, ${item.style.content.mainBtnBgColor})` }\"\r\n                              >\r\n                                <span>{{ item.params.mainBtnText }}</span>\r\n                              </div>\r\n                            </div>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </template>\r\n                  <!-- 两列/三列 -->\r\n                  <template v-else>\r\n                    <div\r\n                      class=\"goods-image\"\r\n                      :style=\"{ borderRadius: `${item.style.content.borderRadius}px` }\"\r\n                    >\r\n                      <img class=\"image\" :src=\"dataItm.goods_image\" />\r\n                    </div>\r\n                    <div class=\"goods-info\">\r\n                      <div\r\n                        v-if=\"inArray('goodsName', item.params.show)\"\r\n                        class=\"goods-name\"\r\n                        :class=\"[ item.style.content.goodsNameRows == 'two' ? 'twoline-hide' : 'oneline-hide', `row-${item.style.goodsNameRows}` ]\"\r\n                      >{{ dataItm.goods_name }}</div>\r\n                      <!-- <div v-if=\"inArray('sellingPoint', item.params.show)\" class=\"goods-selling\">\r\n                        <span\r\n                          class=\"selling oneline-hide\"\r\n                          :style=\"{ color: item.style.sellingColor }\"\r\n                        >{{ dataItm.selling_point }}</span>\r\n                      </div>\r\n                      <div\r\n                        v-if=\"inArray('goodsSales', item.params.show)\"\r\n                        class=\"goods-sales oneline-hide\"\r\n                      >\r\n                        <span class=\"sales\">已售{{ dataItm.goods_sales }}</span>\r\n                      </div>-->\r\n                      <div class=\"footer\">\r\n                        <div\r\n                          v-if=\"inArray('seckillPrice', item.params.show)\"\r\n                          class=\"goods-price oneline-hide\"\r\n                          :style=\"{ color: item.style.content.priceColor }\"\r\n                        >\r\n                          <span class=\"unit\">￥</span>\r\n                          <span class=\"value\">{{ dataItm.seckill_price_min }}</span>\r\n                          <span\r\n                            v-if=\"inArray('originalPrice', item.params.show)\"\r\n                            class=\"line-price\"\r\n                          >\r\n                            <span class=\"unit\">￥</span>\r\n                            <span class=\"value\">{{ dataItm.original_price }}</span>\r\n                          </span>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </template>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 优惠券 -->\r\n          <div\r\n            v-else-if=\"item.type == 'coupon'\"\r\n            class=\"diy-coupon\"\r\n            :style=\"{ padding: `${item.style.paddingTop}px 0`, background: item.style.background }\"\r\n          >\r\n            <div class=\"coupon-wrapper\">\r\n              <div\r\n                class=\"coupon-item\"\r\n                v-for=\"(coupon, idx) in (item.params.source == 'choice' && item.data.length ? item.data : item.defaultData)\"\r\n                :key=\"idx\"\r\n                :style=\"{ marginRight: `${item.style.marginRight}px` }\"\r\n              >\r\n                <i class=\"before\" :style=\"{ background: item.style.background }\"></i>\r\n                <div\r\n                  class=\"left-content\"\r\n                  :style=\"{ background: item.style.couponBgColor, color: item.style.couponTextColor }\"\r\n                >\r\n                  <div class=\"content-top\">\r\n                    <template v-if=\"coupon.coupon_type == 10\">\r\n                      <span class=\"unit\">￥</span>\r\n                      <span class=\"price\">{{ coupon.reduce_price }}</span>\r\n                    </template>\r\n                    <template v-if=\"coupon.coupon_type == 20\">\r\n                      <span class=\"price\">{{ coupon.discount }}折</span>\r\n                    </template>\r\n                  </div>\r\n                  <div class=\"content-bottom\">\r\n                    <span>满{{ coupon.min_price }}元可用</span>\r\n                  </div>\r\n                </div>\r\n                <div\r\n                  class=\"right-receive\"\r\n                  :style=\"{ background: item.style.receiveBgColor, color: item.style.receiveTextColor }\"\r\n                >\r\n                  <span>立即</span>\r\n                  <span>领取</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 头条快报 -->\r\n          <div\r\n            v-else-if=\"item.type == 'special'\"\r\n            class=\"diy-special\"\r\n            :style=\"{ padding: `${item.style.paddingTop}px 0`, background: item.style.background }\"\r\n          >\r\n            <div class=\"special-left\">\r\n              <img class=\"image\" :src=\"item.params.image\" alt />\r\n            </div>\r\n            <div class=\"special-content\" :class=\"[`display_${item.params.display}`]\">\r\n              <ul class=\"special-content-list\">\r\n                <li\r\n                  class=\"content-item oneline-hide\"\r\n                  v-for=\"(dataItm, idx) in (item.params.source == 'choice' && item.data.length ? item.data : item.defaultData)\"\r\n                  :key=\"idx\"\r\n                >\r\n                  <span :style=\"{ color: item.style.textColor }\">{{ dataItm.title }}</span>\r\n                </li>\r\n              </ul>\r\n            </div>\r\n            <div class=\"special-more\">\r\n              <a-icon :component=\"Icon.arrowRight\" />\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 备案号 -->\r\n          <div\r\n            v-else-if=\"item.type == 'ICPLicense'\"\r\n            class=\"diy-ICPLicense\"\r\n            :style=\"{ padding: `${item.style.paddingTop}px ${item.style.paddingLeft}px`, background: item.style.background }\"\r\n          >\r\n            <p class=\"line\" :style=\"{ textAlign: item.style.textAlign }\">\r\n              <a\r\n                :style=\"{ fontSize: item.style.fontSize + 'px', color: item.style.textColor }\"\r\n                :href=\"item.params.link\"\r\n                target=\"_blank\"\r\n              >{{ item.params.text }}</a>\r\n            </p>\r\n          </div>\r\n\r\n          <!-- 商品分组 -->\r\n          <div\r\n            v-else-if=\"item.type == 'goodsGroup'\"\r\n            class=\"diy-goodsGroup\"\r\n            :style=\"{ background: item.style.background, padding: `${item.style.paddingY}px ${item.style.paddingX}px` }\"\r\n          >\r\n            <div\r\n              class=\"tabs\"\r\n              :style=\"{ '--tab-active-font-color': item.style.tabActiveFontColor, '--tab-active-bg-color': item.style.tabActiveBgColor, background: item.style.background }\"\r\n            >\r\n              <div\r\n                class=\"tab-item\"\r\n                v-for=\"(tabItem, dataIdx) in item.params.tabs\"\r\n                :key=\"`${index}_${dataIdx}`\"\r\n                :class=\"{ active: dataIdx == 0 }\"\r\n              >\r\n                <div class=\"tab-name\" :style=\"{ color: item.style.tabTextColor }\">{{ tabItem.name }}</div>\r\n                <div class=\"sub-name\">{{ tabItem.subName }}</div>\r\n              </div>\r\n            </div>\r\n            <div class=\"goods-list\" :style=\"{ marginBottom: `-${item.style.itemMargin}px` }\">\r\n              <div\r\n                class=\"goods-item\"\r\n                v-for=\"(dataItm, dataIdx) in item.defaultData\"\r\n                :key=\"`${index}_${dataIdx}`\"\r\n                :class=\"[`display-${item.style.cardType}`]\"\r\n                :style=\"{ marginBottom: `${item.style.itemMargin}px`, borderRadius: `${item.style.borderRadius}px` }\"\r\n              >\r\n                <div class=\"goods-image\">\r\n                  <img class=\"image\" :src=\"dataItm.goods_image\" alt />\r\n                </div>\r\n                <div class=\"goods-info\">\r\n                  <div\r\n                    v-if=\"inArray('goodsName', item.style.show)\"\r\n                    class=\"goods-name\"\r\n                    :class=\"[ item.style.goodsNameRows == 'two' ? 'twoline-hide' : 'oneline-hide', `row-${item.style.goodsNameRows}` ]\"\r\n                  >{{ dataItm.goods_name }}</div>\r\n                  <div v-if=\"inArray('sellingPoint', item.style.show)\" class=\"goods-selling\">\r\n                    <span\r\n                      class=\"selling oneline-hide\"\r\n                      :style=\"{ color: item.style.sellingColor }\"\r\n                    >{{ dataItm.selling_point }}</span>\r\n                  </div>\r\n                  <div\r\n                    v-if=\"inArray('goodsSales', item.style.show)\"\r\n                    class=\"goods-sales oneline-hide\"\r\n                  >\r\n                    <span class=\"sales\">已售{{ dataItm.goods_sales }}</span>\r\n                  </div>\r\n                  <div class=\"footer\">\r\n                    <div class=\"goods-price oneline-hide\" :style=\"{ color: item.style.priceColor }\">\r\n                      <template v-if=\"inArray('goodsPrice', item.style.show)\">\r\n                        <span class=\"unit\">￥</span>\r\n                        <span class=\"value\">{{ dataItm.goods_price_min }}</span>\r\n                      </template>\r\n                      <span v-if=\"inArray('linePrice', item.style.show)\" class=\"line-price\">\r\n                        <span class=\"unit\">￥</span>\r\n                        <span class=\"value\">{{ dataItm.line_price_min }}</span>\r\n                      </span>\r\n                    </div>\r\n                    <div v-show=\"inArray('cartBtn', item.style.show)\" class=\"action\">\r\n                      <div class=\"btn-cart\" :style=\"{ color: item.style.btnCartColor }\">\r\n                        <a-icon\r\n                          class=\"cart-icon\"\r\n                          :component=\"PageIcon[`jiagou${item.style.btnCartStyle}`]\"\r\n                        />\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 标题文本 -->\r\n          <div\r\n            v-else-if=\"item.type == 'title'\"\r\n            class=\"diy-title\"\r\n            :style=\"{ padding: `${item.style.paddingY}px 15px`, background: item.style.background }\"\r\n          >\r\n            <div class=\"title-content\">\r\n              <!-- 标题文字 -->\r\n              <div class=\"title\">\r\n                <span\r\n                  :style=\"{ color: item.style.titleTextColor, fontSize: `${item.params.titleFontSize}px`, fontWeight: item.params.titleFontWeight }\"\r\n                >{{ item.params.title }}</span>\r\n              </div>\r\n              <!-- 查看更多 -->\r\n              <div\r\n                v-if=\"item.params.more.enable\"\r\n                class=\"more-content\"\r\n                :style=\"{ color: item.style.moreTextColor }\"\r\n              >\r\n                <span class=\"more-text\">{{ item.params.more.text }}</span>\r\n                <span v-if=\"item.params.more.enableIcon\" class=\"more-icon\">\r\n                  <a-icon :component=\"Icon.arrowRight\" />\r\n                </span>\r\n              </div>\r\n            </div>\r\n            <!-- 描述文字 -->\r\n            <div class=\"desc-content\">\r\n              <span\r\n                :style=\"{ color: item.style.descTextColor, fontSize: `${item.params.descFontSize}px`, fontWeight: item.params.descFontWeight }\"\r\n              >{{ item.params.desc }}</span>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 积分商品 -->\r\n          <div\r\n            v-else-if=\"item.type == 'pointsMall'\"\r\n            class=\"diy-pointsMall\"\r\n            :style=\"{ background: item.style.background, padding: `${item.style.paddingY}px ${item.style.paddingX}px` }\"\r\n          >\r\n            <div\r\n              class=\"goods-list\"\r\n              :class=\"[`display-${item.style.display}`, `column-${item.style.column}` ]\"\r\n              :style=\"{ marginBottom: `-${item.style.itemMargin}px` }\"\r\n            >\r\n              <div\r\n                class=\"goods-item\"\r\n                v-for=\"(dataItm, dataIdx) in (item.params.source == 'choice' && item.data.length ? item.data : item.defaultData)\"\r\n                :key=\"`${index}_${dataIdx}`\"\r\n                :class=\"[`display-${item.style.cardType}`]\"\r\n                :style=\"{ marginBottom: `${item.style.itemMargin}px`, borderRadius: `${item.style.borderRadius}px` }\"\r\n              >\r\n                <!-- 两列/三列 -->\r\n                <div class=\"goods-image\">\r\n                  <img class=\"image\" :src=\"dataItm.goods_image\" />\r\n                </div>\r\n                <div class=\"goods-info\">\r\n                  <div\r\n                    v-if=\"inArray('goodsName', item.style.show)\"\r\n                    class=\"goods-name\"\r\n                    :class=\"[ item.style.goodsNameRows == 'two' ? 'twoline-hide' : 'oneline-hide', `row-${item.style.goodsNameRows}` ]\"\r\n                  >{{ dataItm.goods_name }}</div>\r\n                  <div v-if=\"inArray('sellingPoint', item.style.show)\" class=\"goods-selling\">\r\n                    <span\r\n                      class=\"selling oneline-hide\"\r\n                      :style=\"{ color: item.style.sellingColor }\"\r\n                    >{{ dataItm.selling_point }}</span>\r\n                  </div>\r\n                  <div\r\n                    v-if=\"inArray('goodsSales', item.style.show)\"\r\n                    class=\"goods-sales oneline-hide\"\r\n                  >\r\n                    <span class=\"sales\">已兑换{{ dataItm.goods_sales }}</span>\r\n                  </div>\r\n                  <div class=\"footer\">\r\n                    <div\r\n                      v-if=\"inArray('goodsPrice', item.style.show)\"\r\n                      class=\"goods-price oneline-hide\"\r\n                    >\r\n                      <div class=\"points-icon\">\r\n                        <img class=\"image\" src=\"~@/assets/img/points_01.png\" alt />\r\n                      </div>\r\n                      <span class=\"value\">{{ dataItm.points_price_min }}</span>\r\n                      <template v-if=\"dataItm.buy_type == 20\">\r\n                        <span class=\"plus\">+</span>\r\n                        <span class=\"value\">{{ dataItm.paid_price_min }}元</span>\r\n                      </template>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 操作工具栏 -->\r\n          <div class=\"action-tools\">\r\n            <div class=\"tools-content\">\r\n              <div class=\"tools-item\" v-for=\"(itm, idx) in actionTools(item, index)\" :key=\"idx\">\r\n                <a-tooltip placement=\"right\" :getPopupContainer=\"() => $refs['phone-content']\">\r\n                  <span class=\"tooltip-text\" slot=\"title\">{{ itm.title }}</span>\r\n                  <div\r\n                    class=\"item-btn\"\r\n                    :class=\"{ disabled: itm.disabled }\"\r\n                    @click.stop=\"handleActionToolItem(itm, index)\"\r\n                  >\r\n                    <a-icon class=\"tools-icon\" :type=\"itm.type\" />\r\n                  </div>\r\n                </a-tooltip>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </draggable>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport PropTypes from 'ant-design-vue/es/_util/vue-types'\r\nimport draggable from 'vuedraggable'\r\nimport { inArray } from '@/utils/util'\r\nimport * as PageIcon from '@/assets/icons/page'\r\nimport * as Icon from '@/core/icons'\r\nimport { UTag } from './modules'\r\n\r\n// 禁止拖拽的组件\r\nconst undragList = ['service']\r\n\r\nexport default {\r\n  props: {\r\n    // 页面数据\r\n    data: PropTypes.object.def({}),\r\n    // 当前选中的元素索引\r\n    selectedIndex: PropTypes.oneOfType([PropTypes.number, PropTypes.string]).def(0)\r\n  },\r\n  components: {\r\n    draggable,\r\n    UTag\r\n  },\r\n  data () {\r\n    return {\r\n      // 禁止拖拽的组件\r\n      undragList\r\n    }\r\n  },\r\n  computed: {\r\n\r\n  },\r\n  beforeCreate () {\r\n    this.Icon = Icon\r\n    this.PageIcon = PageIcon\r\n    this.inArray = inArray\r\n  },\r\n  methods: {\r\n\r\n    /**\r\n     * 操作工具栏\r\n     * @param item\r\n     * @param index\r\n     */\r\n    actionTools (item, index) {\r\n      const { data: { items } } = this\r\n      const isUndrag = inArray(item.type, undragList)\r\n      return [\r\n        {\r\n          title: '上移',\r\n          type: 'up',\r\n          disabled: isUndrag || index == 0 || inArray(items[index - 1].type, undragList)\r\n        },\r\n        {\r\n          title: '下移',\r\n          type: 'down',\r\n          disabled: isUndrag || items.length <= index + 1 || inArray(items[index + 1].type, undragList)\r\n        },\r\n        {\r\n          title: '复制',\r\n          type: 'copy',\r\n          disabled: false\r\n        },\r\n        {\r\n          title: '删除',\r\n          type: 'delete',\r\n          disabled: false\r\n        }\r\n      ]\r\n    },\r\n\r\n    /**\r\n     * 拖动diy元素更新当前索引\r\n     * @param e\r\n     */\r\n    handelDragItem (e) {\r\n      this.$emit('onEditer', e.newIndex)\r\n    },\r\n\r\n    /**\r\n     * 点击当前选中的Diy元素\r\n     * @param index\r\n     */\r\n    handelClickItem (index) {\r\n      this.$emit('onEditer', index)\r\n    },\r\n\r\n    // 操作工具栏点击事件\r\n    handleActionToolItem (acItem, index) {\r\n      !acItem.disabled && this.$emit('onActionItem', acItem.type, index)\r\n    },\r\n\r\n    // 渲染组件外层容器的样式\r\n    renderItemStyle (item) {\r\n      if (item.type === 'service') {\r\n        return {\r\n          position: 'absolute',\r\n          right: item.style.right + 'px',\r\n          bottom: item.style.bottom + 'px',\r\n          zIndex: 999\r\n        }\r\n      }\r\n      return {}\r\n    }\r\n\r\n  }\r\n}\r\n</script>\r\n<style lang=\"less\" scoped>\r\n@import './style.less';\r\n</style>\r\n"]}]}