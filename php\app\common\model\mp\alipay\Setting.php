<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2023 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\common\model\mp\alipay;

use cores\BaseModel;
use app\common\library\helper;

/**
 * 支付宝小程序设置模型
 * Class Setting
 * @package app\common\model\mp\alipay
 */
class Setting extends BaseModel
{
    // 定义表名
    protected $name = 'mp_alipay_setting';

    protected $createTime = false;

    /**
     * 获取器: 转义数组格式
     * @param $value
     * @return array
     */
    public function getValuesAttr($value): array
    {
        return helper::jsonDecode($value);
    }

    /**
     * 修改器: 转义成json格式
     * @param $value
     * @return string
     */
    public function setValuesAttr($value): string
    {
        return helper::jsonEncode($value);
    }

    /**
     * 获取指定项设置
     * @param string $key
     * @param int|null $storeId
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function getItem(string $key, int $storeId = null): array
    {
        $data = static::getAll($storeId);
        return isset($data[$key]) ? $data[$key]['values'] : [];
    }

    /**
     * 获取支付宝小程序基础配置
     * @param int|null $storeId
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function getConfigBasic(?int $storeId = null): array
    {
        return static::getItem('basic', $storeId);
    }

    /**
     * 获取全部设置
     * @param int|null $storeId
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function getAll(?int $storeId = null): array
    {
        $model = new static;
        if (!is_null($storeId)) {
            $model = $model->where('store_id', '=', $storeId);
        }
        // 获取设置项列表
        $data = $model->select();
        // 整理格式
        $result = [];
        foreach ($data as $item) {
            $result[$item['key']] = $item;
        }
        // 合并默认数据
        return array_merge_recursive($model->defaultData(), $result);
    }

    /**
     * 获取设置项信息
     * @param string $key
     * @return static|array|null
     */
    public static function detail(string $key)
    {
        return static::get(compact('key'));
    }

    /**
     * 默认配置
     * @return array
     */
    public function defaultData(): array
    {
        return [
            'basic' => [
                'key' => 'basic',
                'describe' => '基础设置',
                'values' => [
                    // 是否开启支付宝小程序端访问
                    'enabled' => false,
                    // 小程序AppID
                    'appId' => '',
                    // 签名类型
                    'signType' => 'RSA2',
                    // 签名模式 (10普通公钥模式 20公钥证书模式)
                    'signMode' => 10,
                    // 支付宝公钥
                    'alipayPublicKey' => '',
                    // 应用公钥证书
                    'appCertPublicKey' => '',
                    // 支付宝公钥证书
                    'alipayCertPublicKey' => '',
                    // 支付宝根证书
                    'alipayRootCert' => '',
                    // 商户私钥
                    'merchantPrivateKey' => '',
                ]
            ],
            'customer' => [
                'key' => 'customer',
                'describe' => '客服设置',
                'values' => [
                    // 是否开启客服
                    'enabled' => false,
                    // 客服提供商
                    'provider' => 'myznkf',
                    // 客服配置
                    'config' => [
                        'myznkf' => [
                            'tntInstId' => '',
                            'scene' => ''
                        ]
                    ]
                ]
            ]
        ];
    }
}
