<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2023 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\common\model\groupon;

use cores\BaseModel;
use app\common\library\helper;
use think\model\relation\BelongsTo;

/**
 * 拼团商品SKU模型
 * Class GoodsSku
 * @package app\common\model\groupon
 */
class GoodsSku extends BaseModel
{
    // 定义表名
    protected $name = 'groupon_goods_sku';

    // 定义主键
    protected $pk = 'id';

    /**
     * 关联商品表
     * @return BelongsTo
     */
    public function goods(): BelongsTo
    {
        $module = self::getCalledModule();
        return $this->belongsTo("app\\{$module}\\model\\groupon\\Goods", 'goods_id');
    }

    /**
     * 获取器：规格值ID集
     * @param string|null $value
     * @return array|mixed
     */
    public function getSpecValueIdsAttr(?string $value)
    {
        return !empty($value) ? helper::jsonDecode($value) :[];
    }

    /**
     * 获取器：规格属性
     * @param string|null $value
     * @return array|mixed
     */
    public function getGoodsPropsAttr(?string $value)
    {
        return !empty($value) ? helper::jsonDecode($value) :[];
    }

    /**
     * 获取器：拼团价格 (阶梯团)
     * @param string|null $value
     * @return array|mixed
     */
    public function getStepsPriceConfigAttr(?string $value)
    {
        return !empty($value) ? helper::jsonDecode($value) :[];
    }

    /**
     * 设置器：规格值ID集
     * @param $value
     * @return false|string
     */
    public function setSpecValueIdsAttr($value)
    {
        return helper::jsonEncode($value);
    }

    /**
     * 设置器：规格属性
     * @param $value
     * @return false|string
     */
    public function setGoodsPropsAttr($value)
    {
        return helper::jsonEncode($value);
    }

    /**
     * 设置器：拼团价格 (阶梯团)
     * @param $value
     * @return false|string
     */
    public function setStepsPriceConfigAttr($value)
    {
        return helper::jsonEncode($value);
    }

    /**
     * 获取sku信息详情
     * @param int $grouponGoodsId
     * @param string $goodsSkuId
     * @return static|array|null
     */
    public static function detail(int $grouponGoodsId, string $goodsSkuId)
    {
        return static::get(['groupon_goods_id' => $grouponGoodsId, 'goods_sku_id' => $goodsSkuId]);
    }
}