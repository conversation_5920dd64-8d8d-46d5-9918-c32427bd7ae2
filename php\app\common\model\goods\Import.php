<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2025 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\common\model\goods;

use cores\BaseModel;
use app\common\library\helper;

/**
 * 商品批量导入记录模型
 * Class Import
 * @package app\common\model\goods
 */
class Import extends BaseModel
{
    // 定义表名
    protected $name = 'goods_import';

    // 定义主键
    protected $pk = 'id';

    /**
     * 获取器：开始时间
     * @param $value
     * @return string
     */
    public function getStartTimeAttr($value): string
    {
        return \format_time($value);
    }

    /**
     * 获取器：结束时间
     * @param $value
     * @return string
     */
    public function getEndTimeAttr($value): string
    {
        return \format_time($value);
    }

    /**
     * 获取器：导入失败日志
     * @param $value
     * @return array
     */
    public function getFailLogAttr($value): array
    {
        return $value ? helper::jsonDecode($value) : [];
    }

    /**
     * 修改器：导入失败日志
     * @param $value
     * @return string
     */
    public function setFailLogAttr($value): string
    {
        return helper::jsonEncode($value);
    }

    /**
     * 导出记录详情
     * @param int $id
     * @return static|array|null
     */
    public static function detail(int $id)
    {
        return self::get($id);
    }
}