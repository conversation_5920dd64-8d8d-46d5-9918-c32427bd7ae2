<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2023 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\api\controller\groupon;

use think\response\Json;
use app\api\controller\Controller;
use app\api\service\User as UserService;
use app\api\model\groupon\Goods as GoodsModel;
use app\api\model\groupon\Setting as SettingModel;
use app\common\service\qrcode\groupon\Goods as GoodsPoster;

/**
 * 拼团商品管理
 * Class Goods
 * @package app\api\controller\groupon
 */
class Goods extends Controller
{
    /**
     * 拼团活动商品列表
     * @return Json
     * @throws \think\Exception
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function list(): Json
    {
        // 获取拼团活动会场首页数据
        $service = new GoodsModel;
        $list = $service->getList($this->request->param());
        return $this->renderSuccess(compact('list'));
    }

    /**
     * 获取活动商品详情
     * @param int $grouponGoodsId 拼团商品ID
     * @return Json
     * @throws \cores\exception\BaseException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function detail(int $grouponGoodsId): Json
    {
        // 获取拼团活动商品详情
        $model = new GoodsModel;
        $detail = $model->getGoodsDetail($grouponGoodsId);
        // 获取拼团设置
        $setting = SettingModel::getBasic();
        return $this->renderSuccess(compact('detail', 'setting'));
    }

    /**
     * 生成商品海报
     * @param int $grouponGoodsId 拼团商品ID
     * @param string $channel 二维码渠道(小程序码、h5码)
     * @return Json
     * @throws \cores\exception\BaseException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function poster(int $grouponGoodsId, string $channel = 'H5'): Json
    {
        // 获取拼团活动商品详情
        $service = new GoodsModel;
        $goods = $service->getGoodsBasic($grouponGoodsId);
        // 生成商品海报图
        $userId = UserService::getCurrentLoginUserId();
        $Qrcode = new GoodsPoster($goods, $userId, $channel);
        return $this->renderSuccess(['imageUrl' => $Qrcode->getImage()]);
    }
}