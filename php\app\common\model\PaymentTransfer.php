<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\common\model;

use cores\BaseModel;
use cores\exception\BaseException;
use app\common\enum\payment\transfer\BillState as BillStateEnum;

/**
 * 模型类：第三方转账记录
 * Class PaymentTransfer
 * @package app\common\model
 */
class PaymentTransfer extends BaseModel
{
    // 定义表名
    protected $name = 'payment_transfer';

    // 定义主键
    protected $pk = 'transfer_id';

    /**
     * 转账记录详情
     * @param $where
     * @return static|array|null
     */
    public static function detail($where)
    {
        return static::get($where);
    }

    /**
     * 查询第三方转账记录详情
     * @param string $outBillNo 转账订单号
     * @return static|array|null
     * @throws BaseException
     */
    public static function detailByOutBillNo(string $outBillNo)
    {
        $detail = static::detail(['out_bill_no' => $outBillNo]);
        if (empty($detail)) {
            throwError("第三方支付交易记录不存在 {$outBillNo}");
        }
        return $detail;
    }

    /**
     * 将第三方转账记录更新为转账成功状态
     * @param int $transferId 交易记录ID
     * @param int $state 转账状态
     * @param bool $isReceived 用户是否已领取
     * @return bool
     */
    public static function updateBillState(int $transferId, int $state, bool $isReceived): bool
    {
        return static::updateOne(['bill_state' => $state, 'is_received' => $isReceived], $transferId);
    }
}