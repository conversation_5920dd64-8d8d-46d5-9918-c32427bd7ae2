<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2023 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\store\controller\live;

use think\response\Json;
use app\store\controller\Controller;
use app\store\model\wxapp\LiveRoom as LiveRoomModel;
use cores\exception\BaseException;

/**
 * 小程序直播间管理
 * Class Room
 * @package app\store\controller\apps\live
 */
class Room extends Controller
{
    /**
     * 直播间列表页
     * @param string $search 检索词
     * @return Json
     * @throws \think\db\exception\DbException
     */
    public function list(string $search = ''): Json
    {
        $model = new LiveRoomModel;
        $list = $model->getList($search);
        return $this->renderSuccess(compact('list'));
    }

    /**
     * 同步刷新直播间列表
     * @return Json
     * @throws BaseException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function sync(): Json
    {
        $model = new LiveRoomModel;
        if ($model->refreshLiveList()) {
            return $this->renderSuccess('直播间同步成功');
        }
        return $this->renderError($model->getError() ?: '同步失败');
    }

    /**
     * 修改直播间置顶状态
     * @param int $id 直播间ID
     * @param int $isTop 是否置顶(1置顶 0取消)
     * @return Json
     */
    public function setTop(int $id, int $isTop): Json
    {
        // 直播间详情
        $model = LiveRoomModel::detail($id);
        if (!$model->setIsTop($isTop)) {
            return $this->renderError('操作失败');
        }
        return $this->renderSuccess('操作成功');
    }
}