<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\api\model;

use app\common\enum\payment\transfer\OrderType as TransferOrderTypeEnum;
use app\common\model\PaymentTransfer as PaymentTransferModel;
use app\common\enum\payment\transfer\BillState as BillStateEnum;
use cores\exception\BaseException;

/**
 * 模型类：第三方转账记录
 * Class PaymentTransfer
 * @package app\api\model
 */
class PaymentTransfer extends PaymentTransferModel
{
    /**
     * 隐藏字段
     * @var array
     */
    protected $hidden = [
        'store_id',
        'create_time',
        'update_time'
    ];

    /**
     * 记录第三方转账单
     * @param array $orderInfo 订单信息
     * @param int $orderType 转账订单类型
     * @param string $method 第三方转账方式
     * @param array $payment 支付信息
     * @return bool
     */
    public static function record(array $orderInfo, int $orderType, string $method, array $payment): bool
    {
        return (new static)->save([
            'out_bill_no' => $payment['out_bill_no'],
            'order_type' => $orderType,
            'order_id' => $orderInfo['order_id'],
            'transfer_time' => \time(),
            'user_id' => $orderInfo['user_id'],
            'pay_method' => $method,
            'is_received' => 0,
            'package_info' => $payment['package_info'],
            'transfer_bill_no' => $payment['transfer_bill_no'],
            'bill_status' => BillStateEnum::UNPAID,
            'store_id' => self::$storeId,
        ]);
    }

    /**
     * 获取最近的转账记录
     * @param int $orderId 转账订单ID
     * @param int $orderType 转账订单类型
     * @param string $method 第三方转账方式
     * @return static|array|mixed|\think\Model|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function getLastTransferInfo(int $orderId, int $orderType, string $method)
    {
        $where = [
            'order_type' => $orderType,
            'order_id' => $orderId,
            'is_received' => 0,
            'pay_method' => $method,
            'bill_state' => 10
        ];
        $model = new static;
        return $model->where($where)->order([$model->getPk() => 'desc'])->find();
    }
}
