{"remainingRequest": "D:\\2025\\gaodux\\gaodux5\\vue\\store\\node_modules\\babel-loader\\lib\\index.js!D:\\2025\\gaodux\\gaodux5\\vue\\store\\src\\views\\page\\modules\\phone\\modules\\index.js", "dependencies": [{"path": "D:\\2025\\gaodux\\gaodux5\\vue\\store\\src\\views\\page\\modules\\phone\\modules\\index.js", "mtime": 1656432000000}, {"path": "D:\\2025\\gaodux\\gaodux5\\vue\\store\\babel.config.js", "mtime": 1656432000000}, {"path": "D:\\2025\\gaodux\\gaodux5\\vue\\store\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751804606056}, {"path": "D:\\2025\\gaodux\\gaodux5\\vue\\store\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751804605661}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IFVUYWcgZnJvbSAnLi9VVGFnJzsKZXhwb3J0IHsgVVRhZyB9Ow=="}, {"version": 3, "names": ["UTag"], "sources": ["D:/2025/gaodux/gaodux5/vue/store/src/views/page/modules/phone/modules/index.js"], "sourcesContent": ["import UTag from './UTag'\r\n\r\nexport { UTag }\r\n"], "mappings": "AAAA,OAAOA,IAAI,MAAM,QAAQ;AAEzB,SAASA,IAAI"}]}