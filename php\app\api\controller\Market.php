<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\api\controller;

use think\response\Json;
use app\api\service\Market as MarketService;

/**
 * 营销管理
 * Class Market
 * @package app\store\controller
 */
class Market extends Controller
{
    /**
     * 获取促销活动详情
     * @param int|null $goodsId 商品ID
     * @param int|null $source 商品来源
     * @return Json
     * @throws \cores\exception\BaseException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function detail(?int $goodsId = null, ?int $source = null): Json
    {
        $model = new MarketService;
        return $this->renderSuccess($model->getDetail($goodsId, $source));
    }
}