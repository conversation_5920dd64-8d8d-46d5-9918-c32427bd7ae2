<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\api\controller\dealer;

use think\response\Json;
use app\api\controller\Controller;
use app\api\model\dealer\User as DealerUserModel;
use app\api\model\dealer\Withdraw as WithdrawModel;
use app\api\service\User as UserService;
use app\api\service\dealer\TransferBills as TransferBillsService;
use cores\exception\BaseException;

/**
 * 分销商提现
 * Class Withdraw
 * @package app\api\controller\user\dealer
 */
class Withdraw extends Controller
{
    /**
     * 分销商提现明细
     * @param int $applyStatus 申请状态
     * @return Json
     * @throws BaseException
     * @throws \think\db\exception\DbException
     */
    public function list(int $applyStatus = -1): Json
    {
        $model = new WithdrawModel;
        $list = $model->getList($applyStatus);
        return $this->renderSuccess(compact('list'));
    }

    /**
     * 提交提现申请
     * @return Json
     * @throws BaseException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function submit(): Json
    {
        // 当前用户ID
        $userId = UserService::getCurrentLoginUserId();
        // 分销商用户详情
        $dealer = DealerUserModel::detail($userId);
        // 提交提现申请
        $model = new WithdrawModel;
        if ($model->submit($dealer, $this->postForm())) {
            return $this->renderSuccess([], '提现申请已提交成功，请等待审核');
        }
        return $this->renderError($model->getError() ?: '提交失败');
    }

    /**
     * 分销商提现：微信商家转账 (2025年新接口)
     * @param int $id 提现记录ID
     * @return Json
     * @throws \cores\exception\BaseException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function mchTransfer(int $id): Json
    {
        $service = new TransferBillsService;
        if ($service->mchTransfer($id)) {
            return $this->renderSuccess($service->getResult(), '操作成功');
        }
        return $this->renderError($service->getError() ?: '操作失败');
    }

    /**
     * 分销商提现：微信商家转账同步查询并更新状态
     * @param string $outBillNo 转账订单号
     * @return Json
     * @throws BaseException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function queryBillsNotice(string $outBillNo): Json
    {
        $service = new TransferBillsService;
        if ($service->queryBillsNotice($outBillNo)) {
            return $this->renderSuccess('操作成功');
        }
        return $this->renderError($service->getError() ?: '操作失败');
    }

}