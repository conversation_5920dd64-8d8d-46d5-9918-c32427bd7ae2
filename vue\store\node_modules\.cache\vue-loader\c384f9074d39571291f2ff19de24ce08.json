{"remainingRequest": "D:\\2025\\gaodux\\gaodux5\\vue\\store\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025\\gaodux\\gaodux5\\vue\\store\\src\\views\\page\\modules\\phone\\modules\\UTag.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\2025\\gaodux\\gaodux5\\vue\\store\\src\\views\\page\\modules\\phone\\modules\\UTag.vue", "mtime": 1656432000000}, {"path": "D:\\2025\\gaodux\\gaodux5\\vue\\store\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751804606056}, {"path": "D:\\2025\\gaodux\\gaodux5\\vue\\store\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751804605661}, {"path": "D:\\2025\\gaodux\\gaodux5\\vue\\store\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751804606056}, {"path": "D:\\2025\\gaodux\\gaodux5\\vue\\store\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751804675994}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQppbXBvcnQgUHJvcFR5cGVzIGZyb20gJ2FudC1kZXNpZ24tdnVlL2VzL191dGlsL3Z1ZS10eXBlcycNCg0KLy8gdS10YWfnu4Tku7YNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogJ1VUYWcnLA0KICBjb21wb25lbnRzOiB7DQogIH0sDQogIHByb3BzOiB7DQogICAgLy8g5qCH562+5paH5a2XDQogICAgdGV4dDogUHJvcFR5cGVzLnN0cmluZy5kZWYoJycpLA0KICAgIC8vIOagh+etvuWwuuWvuCAgZGVmYXVsdCBtaW5pDQogICAgc2l6ZTogUHJvcFR5cGVzLnN0cmluZy5kZWYoJ2RlZmF1bHQnKSwNCiAgICAvLyDkuLvpopjnsbvlnosgIHByaW1hcnkgLyBzdWNjZXNzIC8gaW5mbyAvIHdhcm5pbmcgLyBlcnJvcg0KICAgIHR5cGU6IFByb3BUeXBlcy5zdHJpbmcuZGVmKCdwcmltYXJ5JyksDQogICAgLy8g5qih5byP6YCJ5oupICBkYXJrKOa3seiJsuiDjOaZrynjgIFsaWdodCjmtYXoibLog4zmma8p44CBcGxhaW4o55m96Imy6IOM5pmvKQ0KICAgIG1vZGU6IFByb3BUeXBlcy5zdHJpbmcuZGVmKCdsaWdodCcpLA0KICB9LA0KICBkYXRhICgpIHsNCiAgICByZXR1cm4ge30NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["UTag.vue"], "names": [], "mappings": ";AAOA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "UTag.vue", "sourceRoot": "src/views/page/modules/phone/modules", "sourcesContent": ["<template>\r\n  <div class=\"u-tag\" :class=\"[`u-size-${size}`, `u-mode-${mode}-error`]\">\r\n    <span>{{ text }}</span>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport PropTypes from 'ant-design-vue/es/_util/vue-types'\r\n\r\n// u-tag组件\r\nexport default {\r\n  name: 'UTag',\r\n  components: {\r\n  },\r\n  props: {\r\n    // 标签文字\r\n    text: PropTypes.string.def(''),\r\n    // 标签尺寸  default mini\r\n    size: PropTypes.string.def('default'),\r\n    // 主题类型  primary / success / info / warning / error\r\n    type: PropTypes.string.def('primary'),\r\n    // 模式选择  dark(深色背景)、light(浅色背景)、plain(白色背景)\r\n    mode: PropTypes.string.def('light'),\r\n  },\r\n  data () {\r\n    return {}\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.u-tag {\r\n  box-sizing: border-box;\r\n  align-items: center;\r\n  border-radius: 3px;\r\n  display: inline-block;\r\n  line-height: 1;\r\n}\r\n.u-size-mini {\r\n  font-size: 10px;\r\n  padding: 3px 6px;\r\n}\r\n.u-mode-plain-error {\r\n  background-color: #ffffff;\r\n  color: #fa3534;\r\n  border: 1px solid #fa3534;\r\n}\r\n.u-mode-light-error {\r\n  background-color: #fef0f0;\r\n  color: #fa3534;\r\n  border: 1px solid #fab6b6;\r\n}\r\n</style>\r\n"]}]}