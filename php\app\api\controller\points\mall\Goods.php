<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2025 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\api\controller\points\mall;

use think\response\Json;
use app\api\controller\Controller;
use app\api\model\points\mall\Goods as PMGoodsModel;
use app\api\service\User as UserService;
use app\common\service\qrcode\points\mall\Goods as PMGoodsPoster;
use cores\exception\BaseException;

/**
 * 控制器: 积分商城-积分商品
 * Class Goods
 * @package app\api\controller\points\mall
 */
class Goods extends Controller
{
    /**
     * 积分商品列表
     * @return Json
     * @throws \think\Exception
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function list(): Json
    {
        $service = new PMGoodsModel;
        $list = $service->getList($this->request->param());
        return $this->renderSuccess(compact('list'));
    }

    /**
     * 获取积分商品详情
     * @param int $pmGoodsId 积分商品ID
     * @return Json
     * @throws BaseException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function detail(int $pmGoodsId): Json
    {
        $model = new PMGoodsModel;
        return $this->renderSuccess([
            // 积分商品详情
            'detail' => $model->getGoodsDetail($pmGoodsId),
            // 积分设置: 名称
            'pointsSetting' => $model->getPointsSetting(),
            // 用户信息: 可用积分
            'personal' => $model->getPersonal()
        ]);
    }

    /**
     * 生成商品海报
     * @param int $pmGoodsId 积分商品ID
     * @param string $channel 二维码渠道(小程序码、h5码)
     * @return Json
     * @throws \cores\exception\BaseException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function poster(int $pmGoodsId, string $channel = 'H5'): Json
    {
        // 获取积分商品详情
        $service = new PMGoodsModel;
        $pmGoodsInfo = $service->getGoodsBasic($pmGoodsId);
        // 生成商品海报图
        $userId = UserService::getCurrentLoginUserId();
        $Qrcode = new PMGoodsPoster($pmGoodsInfo, $userId, $channel);
        return $this->renderSuccess(['imageUrl' => $Qrcode->getImage()]);
    }
}