<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\common\model\signin;

use cores\BaseModel;
use app\common\library\helper;

/**
 * 模型类: 每日签到日志记录
 * Class Log
 * @package app\common\model\signin
 */
class Log extends BaseModel
{
    // 定义表名
    protected $name = 'signin_log';

    // 定义主键
    protected $pk = 'log_id';

    protected $updateTime = false;

    /**
     * 关联会员记录表
     * @return \think\model\relation\BelongsTo
     */
    public function user(): \think\model\relation\BelongsTo
    {
        $module = self::getCalledModule();
        return $this->belongsTo("app\\{$module}\\model\\User");
    }

    /**
     * 获取器: 奖励配置
     * @param $value
     * @return array
     */
    public function getOptionsAttr($value): array
    {
        return helper::jsonDecode($value);
    }

    /**
     * 修改器: 奖励配置
     * @param $value
     * @return string
     */
    public function setOptionsAttr($value): string
    {
        return helper::jsonEncode($value);
    }

    /**
     * 新增记录
     * @param array $data
     * @return bool
     */
    public static function add(array $data): bool
    {
        $static = new static;
        return $static->save(\array_merge([
            'options' => [],
            'store_id' => $static::$storeId,
        ], $data));
    }
}
