<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2023 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\store\controller\dealer;

use think\response\Json;
use app\store\controller\Controller;
use app\store\model\dealer\Apply as ApplyModel;

/**
 * 分销商申请
 * Class Setting
 * @package app\store\controller\apps\dealer
 */
class Apply extends Controller
{
    /**
     * 分销商申请列表
     * @param string $search
     * @return Json
     */
    public function list(string $search = ''): Json
    {
        $model = new ApplyModel;
        $list = $model->getList($search);
        return $this->renderSuccess(compact('list'));
    }

    /**
     * 分销商审核
     * @param int $applyId
     * @return Json
     */
    public function audit(int $applyId): Json
    {
        $model = ApplyModel::detail($applyId);
        if ($model->submit($this->postForm())) {
            return $this->renderSuccess('操作成功');
        }
        return $this->renderError($model->getError() ?: '操作失败');
    }
}