<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2025 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\api\service\dealer;

use app\common\library\Lock;
use app\common\library\Log;
use app\common\service\BaseService;
use app\api\model\dealer\Withdraw as WithdrawModel;
use app\api\model\PaymentTransfer as PaymentTransferModel;
use app\common\enum\payment\transfer\BillState as BillStateEnum;

use cores\exception\BaseException;

/**
 * 服务类: 分销商提现 - 微信商家转账异步通知
 * Class TransferNotice
 * @package app\api\service\dealer
 */
class TransferNotice extends BaseService
{
    /**
     * 微信商家转账异步通知事件
     * @param array $notifyParams 异步回调的请求参数 (outBillNo、transferBillNo、state)
     * @param string $method 来源方法 (同步queryBillsNotice 异步transfer)
     * @return void
     * @throws BaseException
     */
    public static function notify(array $notifyParams, string $method)
    {
        // 设置并发锁
        Lock::lockUp("TransferNotice-{$notifyParams['outBillNo']}");
        // 查找第三方转账记录
        $transferInfo = PaymentTransferModel::detailByOutBillNo($notifyParams['outBillNo']);
        // 验证转账单状态是否允许更新
        if ($transferInfo['bill_state'] != BillStateEnum::UNPAID) {
            Log::append("TransferNotice-{$method}", ['message' => '第三方转账记录不允许更新']);
            return;
        }
        // 更新第三方转账记录状态
        $state = $notifyParams['state'] === 'SUCCESS' ? BillStateEnum::SUCCESS : BillStateEnum::FAIL;
        PaymentTransferModel::updateBillState($transferInfo['transfer_id'], $state,
            $state === BillStateEnum::SUCCESS);
        // 更新分销商提现记录状态为已打款
        if ($notifyParams['state'] === 'SUCCESS') {
            $model = WithdrawModel::detail($transferInfo['order_id']);
            $model->setPayed();
        }
        // 记录日志
        Log::append("TransferNotice-{$method}", ['message' => '第三方转账记录状态已更新',
            'notifyParams' => $notifyParams]);
        // 解除并发锁
        Lock::unLock("TransferNotice-{$notifyParams['outBillNo']}");
    }
}