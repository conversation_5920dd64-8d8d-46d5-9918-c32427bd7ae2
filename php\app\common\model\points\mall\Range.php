<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\common\model\points\mall;

use cores\BaseModel;

/**
 * 模型类: 积分商城-积分范围
 * Class Range
 * @package app\common\model\points\mall;
 */
class Range extends BaseModel
{
    // 定义表名
    protected $name = 'points_mall_range';

    // 定义主键
    protected $pk = 'range_id';

    /**
     * 积分范围详情
     * @param int $rangeId
     * @return static|array|null
     */
    public static function detail(int $rangeId)
    {
        return static::get($rangeId);
    }

    /**
     * 获取列表记录
     * @param array $where
     * @return \think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getList(array $where = []): \think\Collection
    {
        return $this->where($where)
            ->order(['sort', $this->getPk()])
            ->select();
    }
}
