<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2023 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\api\service;

use app\api\model\Goods as GoodsModel;
use app\api\model\Setting as SettingModel;
use app\api\model\GoodsSku as GoodsSkuModel;
use app\common\enum\Setting as SettingEnum;
use app\common\service\Goods as GoodsService;

/**
 * 商品服务类
 * Class Goods
 * @package app\api\service
 */
class Goods extends GoodsService
{
    /**
     * 获取商品的指定的某个SKU信息
     * @param int $goodsId
     * @param string $goodsSkuId
     * @return GoodsSkuModel|array|null
     */
    public static function getSkuInfo(int $goodsId, string $goodsSkuId)
    {
        $detail = GoodsSkuModel::detail($goodsId, $goodsSkuId);
        if (!empty($detail['image'])) {
            $detail['goods_image'] = $detail['image']['preview_url'];
        }
        return $detail;
    }

    /**
     * 推荐的商品列表
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function recommended(): array
    {
        // 获取商品推荐数据
        $setting = $this->getRecommendedSetting();
        // 获取商品数据
        $model = new GoodsModel;
        if ($setting['params']['source'] === 'choice') {
            // 数据来源：手动
            if (empty($setting['params']['goodsIds'])) return [];
            $goodsList = $model->getListByIdsFromApi($setting['params']['goodsIds']);
        } else {
            // 数据来源：自动
            $goodsList = $model->getList([
                'status' => 10,
                'categoryId' => $setting['params']['auto']['category'],
                'sortType' => $setting['params']['auto']['goodsSort'],
            ], $setting['params']['auto']['showNum']);
        }
        if (empty($goodsList) && $goodsList->isEmpty()) {
            return [];
        }
        return $this->formatGoodsList($goodsList);
    }

    /**
     * 格式化商品列表
     * @param $goodsList
     * @return array
     */
    private function formatGoodsList($goodsList): array
    {
        $data = [];
        foreach ($goodsList as $goods) {
            $data[] = [
                'goods_id' => $goods['goods_id'],
                'goods_name' => $goods['goods_name'],
                'selling_point' => $goods['selling_point'],
                'goods_image' => $goods['goods_image'],
                'goods_price_min' => $goods['goods_price_min'],
                'goods_price_max' => $goods['goods_price_max'],
                'line_price_min' => $goods['line_price_min'],
                'line_price_max' => $goods['line_price_max'],
                'goods_sales' => $goods['goods_sales'],
            ];
        }
        return $data;
    }

    /**
     * 获取商品推荐设置
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    private function getRecommendedSetting(): array
    {
        return SettingModel::getItem(SettingEnum::RECOMMENDED);
    }
}