<?php

namespace app\common\validate\goods;

use think\Validate;

/**
 * 验证类：商品导入
 * Class Import
 * @package app\common\service\goods
 */
class Import extends Validate
{
    // 验证场景
    protected $scene = [
        'goods' => ['goods_name', 'delivery_id', 'imagesIds', 'categoryIds', 'serviceIds'],
        'skuInfo' => ['goods_price', 'stock_num', 'goods_weight'],
    ];

    /**
     * 验证规则
     * @var array
     */
    protected $rule = [
        'goods_name|商品名称' => ['require'],
        'delivery_id|运费模板ID' => ['require', 'number'],
        'imagesIds|商品图片ID集' => ['require', 'array', 'validateIds'],
        'categoryIds|商品分类ID集' => ['require', 'array', 'validateIds'],
        'serviceIds|服务与承诺' => ['array', 'validateIds'],

        'goods_price|商品价格' => ['require', 'float', '>=:0.01'],
        'stock_num|库存数量' => ['require', 'integer', '>=:0'],
        'goods_weight|商品重量' => ['require', 'float', '>=:0'],
    ];

    /**
     * 错误信息
     * @var string[]
     */
    protected $message = [
        // 'delivery_id.number' => '运费模板ID必须是数字',
    ];

    /**
     * 验证ID集格式是否正确
     * @param $value
     * @return bool
     */
    protected function validateIds($value): bool
    {
        foreach ($value as $val) {
            if (!is_numeric($val)) {
                return false;
            }
        }
        return true;
    }
}