-- =============================================
-- 支付宝小程序设置表结构和数据
-- =============================================

-- 创建支付宝小程序设置表
DROP TABLE IF EXISTS `yoshop_mp_alipay_setting`;
CREATE TABLE `yoshop_mp_alipay_setting` (
  `key` varchar(30) NOT NULL DEFAULT '' COMMENT '设置项标示',
  `describe` varchar(255) NOT NULL DEFAULT '' COMMENT '设置项描述',
  `values` mediumtext NOT NULL COMMENT '设置内容(json格式)',
  `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
  `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  UNIQUE KEY `unique_key` (`key`,`store_id`),
  KEY `store_id` (`store_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='支付宝小程序设置表';

-- 插入默认数据 (store_id=10001为示例，实际使用时请根据实际商城ID调整)
INSERT INTO `yoshop_mp_alipay_setting` (`key`, `describe`, `values`, `store_id`, `update_time`) VALUES
('basic', '基础设置', '{\"enabled\":false,\"appId\":\"\",\"signType\":\"RSA2\",\"signMode\":10,\"alipayPublicKey\":\"\",\"appCertPublicKey\":\"\",\"alipayCertPublicKey\":\"\",\"alipayRootCert\":\"\",\"merchantPrivateKey\":\"\"}', 10001, UNIX_TIMESTAMP()),
('customer', '客服设置', '{\"enabled\":false,\"provider\":\"myznkf\",\"config\":{\"myznkf\":{\"tntInstId\":\"\",\"scene\":\"\"}}}', 10001, UNIX_TIMESTAMP());

-- =============================================
-- 说明：
-- 1. 请根据实际的商城ID替换上面的10001
-- 2. 如果有多个商城，需要为每个商城插入对应的数据
-- 3. 表前缀 yoshop_ 请根据实际安装时的表前缀调整
-- =============================================
