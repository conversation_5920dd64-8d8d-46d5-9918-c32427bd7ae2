<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2025 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\api\controller\sharp;

use app\api\controller\Controller;
use app\api\model\sharp\Setting as SettingModel;
use app\api\service\sharp\Active as ActiveService;
use app\api\service\order\Checkout as CheckoutService;
use app\common\enum\order\OrderSource as OrderSourceEnum;
use app\common\library\Lock;
use think\response\Json;

/**
 * 整点秒杀-订单结算控制器
 * Class Checkout
 * @package app\api\controller\sharp
 */
class Checkout extends Controller
{
    /**
     * 秒杀订单结算
     * @return Json
     * @throws \cores\exception\BaseException
     * @throws \cores\exception\BaseException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function order(): Json
    {
        // 实例化结算台服务
        $Checkout = new CheckoutService;
        // 订单结算api参数
        $params = $Checkout->setParam($this->getParam([
            'activeTimeId' => 0,
            'sharpGoodsId' => 0,
            'goodsSkuId' => '',
            'goodsNum' => 0,
        ]));
        // 获取秒杀商品信息
        $service = new ActiveService;
        $goodsList = $service->getCheckoutGoodsList(
            (int)$params['activeTimeId'],
            (int)$params['sharpGoodsId'],
            (string)$params['goodsSkuId'],
            (int)$params['goodsNum']
        );
        // 设置并发锁
        $lockId = "sharp_order_{$params['activeTimeId']}_{$params['sharpGoodsId']}";
        Lock::lockUp($lockId);
        // 设置订单来源
        $Checkout->setOrderSource([
            'source' => OrderSourceEnum::SHARP,
            'sourceId' => $params['activeTimeId'],
        ]);
        // 秒杀商品不参与 等级折扣、积分抵扣和优惠券折扣
        $Checkout->setCheckoutRule([
            'isUserGrade' => false,
            'isCoupon' => false,
            'isUsePoints' => false,
            'isUseFullDiscount' => false,
            'isUseFullFree' => false,
            'isDealer' => SettingModel::getIsDealer(),
        ]);
        // 获取订单结算信息
        $orderInfo = $Checkout->onCheckout($goodsList);
        // 解除并发锁
        Lock::unLock($lockId);
        if ($this->request->isGet()) {
            return $this->renderSuccess([
                'order' => $orderInfo,
                'personal' => $Checkout->getPersonal(),
                'setting' => $Checkout->getSetting(),
            ]);
        }
        // 订单结算提交
        if ($Checkout->hasError()) {
            return $this->renderError($Checkout->getError());
        }
        // 创建订单
        if (!$Checkout->createOrder($orderInfo)) {
            return $this->renderError($Checkout->getError() ?: '订单创建失败');
        }
        // 返回结算信息
        return $this->renderSuccess(['orderId' => $Checkout->model['order_id']], '订单创建成功');
    }

    /**
     * 订单提交
     * @return Json
     * @throws \cores\exception\BaseException
     * @throws \cores\exception\BaseException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function submit(): Json
    {
        return $this->order();
    }

    /**
     * 订单结算提交的参数
     * @param array $define
     * @return array
     */
    private function getParam(array $define = []): array
    {
        return \array_merge($define, $this->request->param());
    }
}