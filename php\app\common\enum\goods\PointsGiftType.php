<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\common\enum\goods;

use app\common\enum\EnumBasics;

/**
 * 枚举类：积分赠送类型
 * Class PointsGiftType
 * @package app\common\enum\goods
 */
class PointsGiftType extends EnumBasics
{
    // 百分比
    const RATIO = 10;

    // 固定数量
    const QUANTITY = 20;

    /**
     * 获取枚举数据
     * @return array
     */
    public static function data(): array
    {
        return [
            self::RATIO => [
                'name' => '百分比',
                'value' => self::RATIO,
            ],
            self::QUANTITY => [
                'name' => '固定数量',
                'value' => self::QUANTITY,
            ]
        ];
    }
}