<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\api\model;

use app\common\model\FullDiscount as FullDiscountModel;
use app\common\enum\fulldiscount\UsableType as UsableTypeEnum;
use app\common\enum\fulldiscount\ThresholdType as ThresholdTypeEnum;

/**
 * 满额立减活动模型
 * Class FullDiscount
 * @package app\api\model
 */
class FullDiscount extends FullDiscountModel
{
    /**
     * 隐藏字段
     * @var array
     */
    protected $hidden = [
        'sort',
        'store_id',
        'is_delete',
        'create_time',
        'update_time',
    ];

    /**
     * 获取开启的活动列表
     * @return FullDiscount[]|array|\think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getActiveList()
    {
        return $this->where('start_time', '<=', \time())
            ->where('end_time', '>=', \time())
            ->where('status', '=', 1)
            ->where('is_delete', '=', 0)
            ->order(['sort', $this->getPk()])
            ->select();
    }

    /**
     * 获取开启的活动列表 (根据指定的商品ID集)
     * @param array $goodsIds 商品ID集
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getActiveListByGoodsIds(array $goodsIds = []): array
    {
        // 获取开启的活动列表
        $list = $this->getActiveList();
        // 根据指定商品ID筛选支持的活动
        $data = [];
        foreach ($list as $item) {
            // 判断商品适用范围
            if ($this->filterUsableType($item, $goodsIds)) {
                $data[] = $item;
            }
        }
        return $data;
    }

    /**
     * 根据订单获取可用的满额立减活动列表 (用于订单结算台)
     * @param array $goodsIds 商品ID集
     * @param int $orderTotalNum 订单商品总数量
     * @param float $orderPayPrice 订单商品总金额
     * @param array $enabledStacking 订单中启用的叠加优惠
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getListByOrder(array $goodsIds, int $orderTotalNum, float $orderPayPrice, array $enabledStacking): array
    {
        // 判断订单商品总金额不能为1分
        if ($orderPayPrice <= 0.01) {
            return [];
        }
        // 获取开启的活动列表
        $list = $this->getActiveListByGoodsIds($goodsIds);
        $data = [];
        foreach ($list as $item) {
            // 判断是否支持订单结算的叠加优惠
            if (!self::checkOrderStacking($item['stacking'], $enabledStacking)) {
                continue;
            }
            // 判断订单是否满件
            if ($item['threshold_type'] == ThresholdTypeEnum::QUANTITY
                && $orderTotalNum >= $item['threshold_quantity']
            ) {
                $data[] = $item;
            }
            // 判断订单是否满额
            if ($item['threshold_type'] == ThresholdTypeEnum::AMOUNT
                && $orderPayPrice >= $item['threshold_money']
            ) {
                $data[] = $item;
            }
        }
        return $data;
    }

    /**
     * 判断商品适用范围
     * @param mixed $item 活动信息
     * @param array $goodsIds 商品ID集
     * @return bool
     */
    private function filterUsableType($item, array $goodsIds = []): bool
    {
        // 全部商品
        if ($item['usable_type'] == UsableTypeEnum::ALL) {
            return true;
        }
        if (empty($goodsIds) || empty($item['usable_config']['goodsIds'])) {
            return false;
        }
        // 指定商品
        if ($item['usable_type'] == UsableTypeEnum::APPOINT
            && !empty(\array_intersect($goodsIds, $item['usable_config']['goodsIds']))
        ) {
            return true;
        }
        // 排除商品
        if ($item['usable_type'] == UsableTypeEnum::EXCLUDE
            && !empty(\array_diff($goodsIds, $item['usable_config']['goodsIds']))
        ) {
            return true;
        }
        return false;
    }
}
