<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2023 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\api\service;

use app\api\model\Store as StoreModel;
use app\api\service\Client as ClientService;
use app\api\service\Setting as SettingService;
use app\api\model\store\Module as StoreModuleModel;
use app\api\model\dealer\Setting as DealerSettingModel;
use app\common\service\BaseService;

/**
 * 商城基础信息
 * Class Store
 * @package app\api\service
 */
class Store extends BaseService
{
    /**
     * 获取商城基础信息
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function data(): array
    {
        return [
            // 店铺基本信息
            'storeInfo' => $this->storeInfo(),
            // 当前客户端名称
            'client' => \getPlatform(),
            // 商城设置
            'setting' => $this->setting(),
            // 分销设置 (仅基础设置)
            'dealer' => $this->dealerData(),
            // 客户端设置
            'clientData' => $this->getClientData(),
            // 开启的功能模块
            'modules' => $this->getModules(),
        ];
    }

    /**
     * 开启的功能模块
     * @return array|mixed
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    private function getModules() {
        return StoreModuleModel::getModules($this->storeId);
    }

    /**
     * 客户端公共数据
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    private function getClientData(): array
    {
        $service = new ClientService;
        return $service->getPublic();
    }

    /**
     * 商城设置
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    private function setting(): array
    {
        $service = new SettingService;
        return $service->getPublic();
    }

    /**
     * 分销设置 (仅基础设置)
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    private function dealerData(): array
    {
        return ['setting' => DealerSettingModel::getPublic()['basic']];
    }

    /**
     * 店铺基本信息（名称、简介、logo）
     * @return StoreModel|array|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    private function storeInfo()
    {
        return StoreModel::getInfo();
    }
}