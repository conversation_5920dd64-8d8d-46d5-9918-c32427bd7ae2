<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2023 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\store\controller\client\wxofficial;

use think\response\Json;
use app\store\controller\Controller;
use app\store\model\wxofficial\Setting as SettingModel;
use app\store\service\client\wxofficial\Setting as WxofficialSettingService;

/**
 * 微信公众号设置
 * Class Setting
 * @package app\store\controller\apps\wxofficial
 */
class Setting extends Controller
{
    /**
     * 获取微信公众号设置 (基础设置)
     * @return Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function basic(): Json
    {
        $service = new WxofficialSettingService;
        return $this->renderSuccess($service->basic());
    }

    /**
     * 获取微信公众号设置 (指定)
     * @param string $key
     * @return Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function detail(string $key): Json
    {
        // 获取微信小程序设置
        $detail = SettingModel::getItem($key);
        return $this->renderSuccess(compact('detail'));
    }

    /**
     * 更新设置项
     * @param string $key
     * @return Json
     */
    public function update(string $key): Json
    {
        $model = new SettingModel;
        if ($model->edit($key, $this->postForm())) {
            return $this->renderSuccess('更新成功');
        }
        return $this->renderError($model->getError() ?: '更新失败');
    }
}