<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2023 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\api\model\store\shop;

use app\api\model\Order as OrderModel;
use app\api\service\User as UserService;
use app\api\model\store\shop\Clerk as ClerkModel;
use app\common\model\store\shop\Order as ShopOrderModel;

/**
 * 商家门店核销订单记录模型
 * Class Order
 * @package app\api\model\store\shop
 */
class Order extends ShopOrderModel
{
    /**
     * 获取用户订单详情页数据 [需验证核销员身份]
     * @param int $orderId 订单ID
     * @return \app\api\model\Order|array|false|null
     * @throws \cores\exception\BaseException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function getUserOrderDetail(int $orderId)
    {
        // 获取当前店员信息
        $userId = UserService::getCurrentLoginUserId();
        if (!ClerkModel::getClerkInfo($userId)) {
            return false;
        }
        // 查询当前的订单信息
        return OrderModel::getUserOrderDetail($orderId, false);
    }

    /**
     * 确认订单核销
     * @param int $orderId 订单ID
     * @return bool
     * @throws \cores\exception\BaseException
     */
    public function extract(int $orderId): bool
    {
        // 获取当前店员信息
        $userId = UserService::getCurrentLoginUserId();
        $clerkInfo = ClerkModel::getClerkInfo($userId);
        // 查询当前的订单信息
        $orderInfo = OrderModel::getDetail($orderId, [], false);
        // 验证店员信息是否为指定门店的核销员
        ClerkModel::checkAuthority($clerkInfo, (int)$orderInfo['extract_shop_id']);
        // 确认并核销
        if (!$orderInfo->confirmExtract((int)$clerkInfo['clerk_id'], true)) {
            $this->error = $orderInfo->getError();
            return false;
        }
        return true;
    }
}