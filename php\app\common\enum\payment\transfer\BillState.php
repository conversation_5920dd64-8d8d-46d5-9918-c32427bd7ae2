<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\common\enum\payment\transfer;

use app\common\enum\EnumBasics;

/**
 * 枚举类：第三方转账记录 - 打款状态
 * Class BillState
 * @package app\common\enum\payment\transfer
 */
class BillState extends EnumBasics
{
    // 未转账
    const UNPAID = 10;

    // 转账成功
    const SUCCESS = 20;

    // 转入撤销
    const REFUND = 30;

    // 转账失败
    const FAIL = 40;

    /**
     * 获取类型值
     * @return array
     */
    public static function data(): array
    {
        return [
            self::UNPAID => [
                'name' => '未转账',
                'value' => self::UNPAID
            ],
            self::SUCCESS => [
                'name' => '转账成功',
                'value' => self::SUCCESS
            ],
            self::REFUND => [
                'name' => '转入撤销',
                'value' => self::REFUND
            ],
            self::FAIL => [
                'name' => '转账失败',
                'value' => self::FAIL
            ]
        ];
    }
}