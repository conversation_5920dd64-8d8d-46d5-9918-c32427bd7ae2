import { axios } from '@/utils/request'

// api接口列表
const api = {
  detail: '/client.mp.alipay.customer/detail',
  update: '/client.mp.alipay.customer/update'
}

/**
 * 获取支付宝小程序客服设置
 */
export function detail () {
  return axios({
    url: api.detail,
    method: 'get'
  })
}

/**
 * 更新客服设置
 * @param {*} data
 */
export function update (data) {
  return axios({
    url: api.update,
    method: 'post',
    data
  })
}
