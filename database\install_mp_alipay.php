<?php
/**
 * 支付宝小程序数据库安装脚本
 * 使用方法：在浏览器中访问此文件，或在命令行中执行 php install_mp_alipay.php
 */

// 数据库配置 - 请根据实际情况修改
$config = [
    'host' => 'localhost',
    'port' => 3306,
    'database' => 'your_database_name',  // 请修改为实际数据库名
    'username' => 'your_username',       // 请修改为实际用户名
    'password' => 'your_password',       // 请修改为实际密码
    'charset' => 'utf8mb4',
    'prefix' => 'yoshop_'               // 请修改为实际表前缀
];

// 商城ID - 请根据实际情况修改
$storeIds = [10001]; // 可以添加多个商城ID，如：[10001, 10002, 10003]

try {
    // 连接数据库
    $dsn = "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset={$config['charset']}";
    $pdo = new PDO($dsn, $config['username'], $config['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "数据库连接成功！\n";
    
    // 创建表结构
    $createTableSql = "
    DROP TABLE IF EXISTS `{$config['prefix']}mp_alipay_setting`;
    CREATE TABLE `{$config['prefix']}mp_alipay_setting` (
      `key` varchar(30) NOT NULL DEFAULT '' COMMENT '设置项标示',
      `describe` varchar(255) NOT NULL DEFAULT '' COMMENT '设置项描述',
      `values` mediumtext NOT NULL COMMENT '设置内容(json格式)',
      `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商城ID',
      `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
      UNIQUE KEY `unique_key` (`key`,`store_id`),
      KEY `store_id` (`store_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='支付宝小程序设置表';
    ";
    
    $pdo->exec($createTableSql);
    echo "支付宝小程序设置表创建成功！\n";
    
    // 插入默认数据
    $insertSql = "INSERT INTO `{$config['prefix']}mp_alipay_setting` (`key`, `describe`, `values`, `store_id`, `update_time`) VALUES (?, ?, ?, ?, ?)";
    $stmt = $pdo->prepare($insertSql);
    
    $currentTime = time();
    
    // 基础设置数据
    $basicValues = json_encode([
        'enabled' => false,
        'appId' => '',
        'signType' => 'RSA2',
        'signMode' => 10,
        'alipayPublicKey' => '',
        'appCertPublicKey' => '',
        'alipayCertPublicKey' => '',
        'alipayRootCert' => '',
        'merchantPrivateKey' => ''
    ], JSON_UNESCAPED_UNICODE);
    
    // 客服设置数据
    $customerValues = json_encode([
        'enabled' => false,
        'provider' => 'myznkf',
        'config' => [
            'myznkf' => [
                'tntInstId' => '',
                'scene' => ''
            ]
        ]
    ], JSON_UNESCAPED_UNICODE);
    
    // 为每个商城插入数据
    foreach ($storeIds as $storeId) {
        // 插入基础设置
        $stmt->execute(['basic', '基础设置', $basicValues, $storeId, $currentTime]);
        // 插入客服设置
        $stmt->execute(['customer', '客服设置', $customerValues, $storeId, $currentTime]);
        
        echo "商城ID {$storeId} 的支付宝小程序设置数据插入成功！\n";
    }
    
    echo "\n=== 安装完成 ===\n";
    echo "支付宝小程序功能已成功安装！\n";
    echo "现在可以在商户后台的 [客户端] -> [支付宝小程序] 中进行配置。\n";
    
} catch (PDOException $e) {
    echo "数据库错误: " . $e->getMessage() . "\n";
    echo "请检查数据库配置是否正确。\n";
} catch (Exception $e) {
    echo "安装错误: " . $e->getMessage() . "\n";
}
?>
