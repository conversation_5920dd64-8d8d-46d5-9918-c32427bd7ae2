<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2025 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
namespace app\common\model\groupon;

use cores\BaseModel;
use think\model\relation\BelongsTo;

/**
 * 拼团拼单成员模型
 * Class TaskUsers
 * @package app\common\model\groupon
 */
class TaskUsers extends BaseModel
{
    // 定义表名
    protected $name = 'groupon_task_users';

    // 定义主键
    protected $pk = 'id';

    /**
     * 关联模型：用户信息
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        $module = self::getCalledModule();
        return $this->BelongsTo("app\\{$module}\\model\\User")->field(['user_id', 'nick_name', 'avatar_id']);
    }

    /**
     * 关联模型：机器人信息
     * @return BelongsTo
     */
    public function robot(): BelongsTo
    {
        return $this->BelongsTo('Robot', 'robot_id');
    }

    /**
     * 获取器：拼单开始时间
     * @param $value
     * @return false|string
     */
    public function getStartTimeAttr($value)
    {
        return \format_time($value);
    }

    /**
     * 判断用户是否已经参与了拼单
     * @param int $taskId
     * @param int $userId
     * @return bool
     */
    public static function isJoin(int $taskId, int $userId): bool
    {
        return (bool)(new static)->where('task_id', '=', $taskId)
            ->where('user_id', '=', $userId)
            ->where('is_robot', '=', 0)
            ->value('id');
    }
}