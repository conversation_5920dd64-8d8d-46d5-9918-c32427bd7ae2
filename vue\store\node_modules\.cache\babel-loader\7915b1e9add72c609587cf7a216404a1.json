{"remainingRequest": "D:\\2025\\gaodux\\gaodux5\\vue\\store\\node_modules\\babel-loader\\lib\\index.js!D:\\2025\\gaodux\\gaodux5\\vue\\store\\src\\views\\page\\modules\\editor\\modules\\HotZoneModal\\index.js", "dependencies": [{"path": "D:\\2025\\gaodux\\gaodux5\\vue\\store\\src\\views\\page\\modules\\editor\\modules\\HotZoneModal\\index.js", "mtime": 1657468800000}, {"path": "D:\\2025\\gaodux\\gaodux5\\vue\\store\\babel.config.js", "mtime": 1656432000000}, {"path": "D:\\2025\\gaodux\\gaodux5\\vue\\store\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751804606056}, {"path": "D:\\2025\\gaodux\\gaodux5\\vue\\store\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751804605661}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IEhvdFpvbmVNb2RhbCBmcm9tICcuL0hvdFpvbmVNb2RhbCc7CmV4cG9ydCBkZWZhdWx0IEhvdFpvbmVNb2RhbDs="}, {"version": 3, "names": ["HotZoneModal"], "sources": ["D:/2025/gaodux/gaodux5/vue/store/src/views/page/modules/editor/modules/HotZoneModal/index.js"], "sourcesContent": ["import HotZoneModal from './HotZoneModal'\r\n\r\nexport default HotZoneModal\r\n"], "mappings": "AAAA,OAAOA,YAAY,MAAM,gBAAgB;AAEzC,eAAeA,YAAY"}]}