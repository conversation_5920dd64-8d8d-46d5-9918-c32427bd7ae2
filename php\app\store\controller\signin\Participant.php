<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\store\controller\signin;

use think\response\Json;
use app\store\controller\Controller;
use app\store\model\signin\Participant as ParticipantModel;

/**
 * 控制器: 每日签到记录
 * Class Participant
 * @package app\store\controller\signin
 */
class Participant extends Controller
{
    /**
     * 获取参与用户列表
     * @return Json
     */
    public function list(): Json
    {
        $model = new ParticipantModel;
        $list = $model->getList($this->request->param());
        return $this->renderSuccess(compact('list'));
    }
}
