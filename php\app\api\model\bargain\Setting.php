<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2023 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\api\model\bargain;

use app\common\model\bargain\Setting as SettingModel;

/**
 * 砍价活动设置模型
 * Class Setting
 * @package app\api\model\bargain
 */
class Setting extends SettingModel
{
    /**
     * 获取砍价基本配置
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function getBasic(): array
    {
        return [
            // 砍价规则
            'rulesDesc' => static::getItem('basic')['rulesDesc'],
        ];
    }
}