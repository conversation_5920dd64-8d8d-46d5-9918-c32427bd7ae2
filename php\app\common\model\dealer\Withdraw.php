<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\common\model\dealer;

use cores\BaseModel;
use cores\exception\BaseException;
use think\model\relation\BelongsTo;
use app\store\model\dealer\Capital as CapitalModel;
use app\store\model\dealer\User as DealerUserModel;
use app\common\model\Payment as PaymentModel;
use app\common\model\UserOauth as UserOauthModel;
use app\common\enum\Client as ClientEnum;
use app\common\enum\payment\Method as PaymentMethodEnum;
use app\common\enum\dealer\withdraw\ApplyStatus as ApplyStatusEnum;

/**
 * 分销商提现明细模型
 * Class Withdraw
 * @package app\common\model\dealer
 */
class Withdraw extends BaseModel
{
    // 定义表名
    protected $name = 'dealer_withdraw';

    // 定义主键
    protected $pk = 'id';

    /**
     * 关联分销商用户表
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        $module = self::getCalledModule();
        return $this->belongsTo("app\\{$module}\\model\\User");
    }

    /**
     * 提现详情
     * @param int $id
     * @return static|array|null
     * @throws BaseException
     */
    public static function detail(int $id)
    {
        $detail = static::get($id);
        if (empty($detail)) {
            throwError("该分销商提现记录不存在 {$id}");
        }
        return $detail;
    }

    /**
     * 验证已冻结佣金是否合法
     * @param int $userId 分销商用户ID
     * @param string $money 提现金额
     * @return bool
     */
    public function verifyUserFreezeMoney(int $userId, string $money): bool
    {
        $dealerUserInfo = DealerUserModel::detail($userId);
        if ($dealerUserInfo['freeze_money'] < $money) {
            $this->error = '数据错误：已冻结的佣金不能小于提现的金额';
            return false;
        }
        return true;
    }

    /**
     * 确认已打款
     * @return bool
     */
    public function setPayed(): bool
    {
        $this->transaction(function () {
            // 更新申请状态
            $this->save(['apply_status' => ApplyStatusEnum::PAID, 'audit_time' => \time()]);
            // 更新分销商累积提现佣金
            DealerUserModel::totalMoney((int)$this['user_id'], $this['money']);
            // 记录分销商资金明细
            CapitalModel::add([
                'user_id' => $this['user_id'],
                'flow_type' => 20,
                'money' => -$this['money'],
                'describe' => '申请提现'
            ]);
        });
        return true;
    }

    /**
     * 获取支付方式的配置信息
     * @param string $client 客户端
     * @return mixed
     * @throws BaseException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function getPaymentConfig(string $client)
    {
        $PaymentModel = new PaymentModel;
        $templateInfo = $PaymentModel->getPaymentInfo(PaymentMethodEnum::WECHAT, $client, static::$storeId);
        return $templateInfo['template']['config'][PaymentMethodEnum::WECHAT];
    }

    /**
     * 获取分销商用户微信小程序OpenID
     * @param int $userId 分销商用户ID
     * @param string $platform 提现申请所在的客户端 (只能是微信小程序或者微信公众号)
     * @return string
     * @throws BaseException
     */
    public function getWeiXinOpenId(int $userId, string $platform): string
    {
        if (!\in_array($platform, [ClientEnum::MP_WEIXIN])) {
            throwError('很抱歉，提现申请来源客户端必须是微信小程序');
        }
        $openid = UserOauthModel::getOauthIdByUserId($userId, $platform);
        if (empty($openid)) {
            throwError('很抱歉，未找到当前分销商用户的openid');
        }
        return $openid;
    }
}