<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2023 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\store\controller\client\mp\alipay;

use app\store\controller\Controller;
use app\store\model\mp\alipay\Setting as SettingModel;
use think\response\Json;

/**
 * 支付宝小程序客服设置
 * Class Customer
 * @package app\store\controller\client\mp\alipay
 */
class Customer extends Controller
{
    /**
     * 获取客服设置
     * @return Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function detail(): Json
    {
        $detail = SettingModel::getItem('customer');
        return $this->renderSuccess(compact('detail'));
    }

    /**
     * 更新客服设置
     * @return Json
     */
    public function update(): Json
    {
        $model = new SettingModel;
        if ($model->edit('customer', $this->postForm())) {
            return $this->renderSuccess('更新成功');
        }
        return $this->renderError($model->getError() ?: '更新失败');
    }
}
