# 支付宝小程序数据库安装说明

## 文件说明

### 1. mp_alipay_setting.sql
纯SQL文件，包含支付宝小程序设置表的创建语句和默认数据。

### 2. install_mp_alipay.php
PHP安装脚本，可以自动创建表结构并插入默认数据。

## 安装方法

### 方法一：使用SQL文件（推荐）

1. 打开 `mp_alipay_setting.sql` 文件
2. 根据实际情况修改以下内容：
   - 表前缀：将 `yoshop_` 替换为实际的表前缀
   - 商城ID：将 `10001` 替换为实际的商城ID
3. 在数据库管理工具（如phpMyAdmin、Navicat等）中执行SQL语句

### 方法二：使用PHP安装脚本

1. 打开 `install_mp_alipay.php` 文件
2. 修改数据库配置：
   ```php
   $config = [
       'host' => 'localhost',
       'port' => 3306,
       'database' => 'your_database_name',  // 修改为实际数据库名
       'username' => 'your_username',       // 修改为实际用户名
       'password' => 'your_password',       // 修改为实际密码
       'charset' => 'utf8mb4',
       'prefix' => 'yoshop_'               // 修改为实际表前缀
   ];
   ```
3. 修改商城ID：
   ```php
   $storeIds = [10001]; // 可以添加多个商城ID
   ```
4. 执行安装脚本：
   - 方式1：在浏览器中访问 `http://your-domain/database/install_mp_alipay.php`
   - 方式2：在命令行中执行 `php install_mp_alipay.php`

## 表结构说明

### yoshop_mp_alipay_setting 表

| 字段名 | 类型 | 说明 |
|--------|------|------|
| key | varchar(30) | 设置项标识 |
| describe | varchar(255) | 设置项描述 |
| values | mediumtext | 设置内容(JSON格式) |
| store_id | int(11) | 商城ID |
| update_time | int(11) | 更新时间 |

### 默认设置项

#### basic（基础设置）
```json
{
  "enabled": false,
  "appId": "",
  "signType": "RSA2",
  "signMode": 10,
  "alipayPublicKey": "",
  "appCertPublicKey": "",
  "alipayCertPublicKey": "",
  "alipayRootCert": "",
  "merchantPrivateKey": ""
}
```

#### customer（客服设置）
```json
{
  "enabled": false,
  "provider": "myznkf",
  "config": {
    "myznkf": {
      "tntInstId": "",
      "scene": ""
    }
  }
}
```

## 注意事项

1. **表前缀**：请确保表前缀与系统实际使用的前缀一致
2. **商城ID**：请使用实际的商城ID，可以在 `yoshop_store` 表中查看
3. **权限**：确保数据库用户有创建表和插入数据的权限
4. **备份**：建议在执行前备份数据库

## 验证安装

安装完成后，可以通过以下方式验证：

1. 检查数据库中是否存在 `yoshop_mp_alipay_setting` 表
2. 查看表中是否有对应商城ID的 `basic` 和 `customer` 两条记录
3. 登录商户后台，查看 [客户端] -> [支付宝小程序] 菜单是否可以正常访问

## 故障排除

### 常见问题

1. **表已存在错误**：SQL中包含 `DROP TABLE IF EXISTS`，会自动删除已存在的表
2. **权限不足**：确保数据库用户有足够的权限
3. **字符编码问题**：确保数据库和表都使用 utf8mb4 编码
4. **商城ID不存在**：确保使用的商城ID在系统中确实存在

### 手动清理

如果需要重新安装，可以执行：
```sql
DROP TABLE IF EXISTS `yoshop_mp_alipay_setting`;
```

然后重新执行安装步骤。
