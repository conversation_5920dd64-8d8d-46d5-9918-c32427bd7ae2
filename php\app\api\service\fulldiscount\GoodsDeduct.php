<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\api\service\fulldiscount;

use app\common\service\BaseService;
use app\common\library\helper;
use app\common\enum\fulldiscount\Methods as MethodsEnum;
use app\common\enum\fulldiscount\UsableType as UsableTypeEnum;
use app\common\enum\fulldiscount\DiscountType as DiscountTypeEnum;
use cores\exception\BaseException;

/**
 * 订单满额立减服务类
 * Class GoodsDeduct
 * @package app\api\service\fulldiscount
 */
class GoodsDeduct extends BaseService
{
    // 实际抵扣金额
    private int $actualReducedMoney;

    // 订单商品列表
    private iterable $goodsList = [];

    // 满额立减活动信息
    private $activeInfo = null;

    // 实际参与满额立减的商品记录
    private array $rangeGoodsList = [];

    // 设置订单商品列表
    public function setGoodsList(iterable $goodsList): GoodsDeduct
    {
        $this->goodsList = $goodsList;
        return $this;
    }

    // 设置满额立减活动信息
    public function setActiveInfo($activeInfo): GoodsDeduct
    {
        $this->activeInfo = $activeInfo;
        return $this;
    }

    /**
     * 计算满额立减金额
     * @return GoodsDeduct
     * @throws BaseException
     */
    public function setGoodsFullDiscountMoney(): GoodsDeduct
    {
        // 验证当前类属性
        $this->checkAttribute();
        // 设置实际参与满额立减的商品记录
        $this->setRangeGoodsList();
        // 计算实际抵扣的金额
        $this->setActualReducedMoney();
        // 实际抵扣金额为0
        if ($this->actualReducedMoney > 0) {
            // 计算商品的价格权重
            $this->setGoodsListWeight();
            // 计算商品满额立减金额
            $this->setGoodsListFullDiscountMoney();
            // 总抵扣金额 (已分配的)
            $assignedFullDiscountMoney = (int)helper::getArrayColumnSum($this->rangeGoodsList, 'full_discount_money');
            // 分配剩余的抵扣金额
            $this->setGoodsListFullDiscountMoneyFill($assignedFullDiscountMoney);
            $this->setGoodsListFullDiscountMoneyDiff($assignedFullDiscountMoney);
        }
        return $this;
    }

    // 获取实际参与满额立减的商品记录
    public function getRangeGoodsList(): array
    {
        return $this->rangeGoodsList;
    }

    /**
     * 获取实际抵扣的金额
     * @return int
     */
    public function getActualReducedMoney(): int
    {
        return $this->actualReducedMoney;
    }

    /**
     * 设置实际参与满额立减的商品记录
     * @return void
     */
    private function setRangeGoodsList()
    {
        $this->rangeGoodsList = [];
        foreach ($this->goodsList as $goods) {
            $goods['total_price'] *= 100;
            $goodsKey = "{$goods['goods_id']}-{$goods['goods_sku_id']}";
            switch ($this->activeInfo['usable_type']) {
                // 全部商品
                case UsableTypeEnum::ALL:
                    $this->rangeGoodsList[$goodsKey] = $goods;
                    break;
                // 指定商品
                case UsableTypeEnum::APPOINT:
                    if (\in_array($goods['goods_id'], $this->activeInfo['usable_config']['goodsIds'])) {
                        $this->rangeGoodsList[$goodsKey] = $goods;
                    }
                    break;
                // 排除商品
                case UsableTypeEnum::EXCLUDE:
                    if (!\in_array($goods['goods_id'], $this->activeInfo['usable_config']['goodsIds'])) {
                        $this->rangeGoodsList[$goodsKey] = $goods;
                    }
                    break;
                default:
                    break;
            }
        }
    }

    /**
     * 验证当前类属性
     * @throws BaseException
     */
    private function checkAttribute()
    {
        if (empty($this->goodsList) || empty($this->activeInfo)) {
            throwError('goodsList or activeInfo not found.');
        }
    }

    /**
     * 计算实际抵扣的金额
     */
    private function setActualReducedMoney()
    {
        // 抵扣的金额
        $reducePrice = 0;
        // 获取当前订单商品总额
        $orderTotalPrice = $this->getOrderTotalPrice();
        // 优惠一次
        if ($this->activeInfo['methods'] == MethodsEnum::ONCE) {
            // 减额
            if ($this->activeInfo['discount_type'] == DiscountTypeEnum::REDUCE) {
                $reducePrice = $this->activeInfo['discount_money'] * 100;
            }
            // 打折
            if ($this->activeInfo['discount_type'] == DiscountTypeEnum::DISCOUNT) {
                $reducePrice = $orderTotalPrice - helper::bcmul($orderTotalPrice, $this->activeInfo['discount_rate'] / 10);
            }
        }
        // 循环优惠
        if ($this->activeInfo['methods'] == MethodsEnum::REPEAT) {
            // 循环优惠的次数
            $times = \floor($orderTotalPrice / ($this->activeInfo['threshold_money'] * 100));
            $reducePrice = $times * $this->activeInfo['discount_money'] * 100;
            // 不能超出每单最多减免金额
            if ($this->activeInfo['discount_max_money'] > 0) {
                $reducePrice = \min($reducePrice, $this->activeInfo['discount_max_money'] * 100);
            }
        }
        // 最大允许抵扣到一分钱，所以此处判断抵扣金额大于等于订单金额时，减去一分钱
        $this->actualReducedMoney = ($reducePrice >= $orderTotalPrice) ? $orderTotalPrice - 1 : (int)$reducePrice;
    }

    /**
     * 获取当前订单商品总额
     * @return int
     */
    private function getOrderTotalPrice(): int
    {
        $orderTotalPrice = 0;
        foreach ($this->goodsList as $goods) {
            $orderTotalPrice += (int)($goods['total_price'] * 100);
        }
        return $orderTotalPrice;
    }

    /**
     * 计算商品抵扣的权重(占比)
     */
    private function setGoodsListWeight()
    {
        $orderTotalPrice = helper::getArrayColumnSum($this->rangeGoodsList, 'total_price');
        foreach ($this->rangeGoodsList as &$goods) {
            $weight = \round($goods['total_price'] / $orderTotalPrice, 6);
            $goods['weight'] = helper::scToStr((string)$weight, 6);
        }
        \array_sort($this->rangeGoodsList, 'weight', true);
    }

    /**
     * 计算商品抵扣的金额
     */
    private function setGoodsListFullDiscountMoney(): void
    {
        foreach ($this->rangeGoodsList as &$goods) {
            $goods['full_discount_money'] = helper::bcmul($this->actualReducedMoney, $goods['weight'], 0);
        }
    }

    private function setGoodsListFullDiscountMoneyFill(int $assignedFullDiscountMoney): void
    {
        if ($assignedFullDiscountMoney === 0) {
            $temReducedMoney = $this->actualReducedMoney;
            foreach ($this->rangeGoodsList as &$goods) {
                if ($temReducedMoney == 0) break;
                $goods['full_discount_money'] = 1;
                $temReducedMoney--;
            }
        }
    }

    private function setGoodsListFullDiscountMoneyDiff(int $assignedFullDiscountMoney): void
    {
        // 剩余未抵扣金额 = 实际抵扣金额 - 总抵扣金额 (已分配的)
        $tempDiff = $this->actualReducedMoney - $assignedFullDiscountMoney;
        foreach ($this->rangeGoodsList as &$goods) {
            if ($tempDiff < 1) break;
            // 当抵扣金额大于商品总价时不再累积抵扣
            if ($goods['full_discount_money'] >= $goods['total_price']) continue;
            $goods['full_discount_money']++;
            $tempDiff--;
        }
    }
}