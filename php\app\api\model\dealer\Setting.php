<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2023 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\api\model\dealer;

use app\common\model\dealer\Setting as SettingModel;

/**
 * 分销商设置模型
 * Class Setting
 * @package app\api\model\dealer
 */
class Setting extends SettingModel
{
    /**
     * 隐藏字段
     * @var array
     */
    protected $hidden = ['update_time'];

    /**
     * 获取分销设置 (仅输出可公开的配置)
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function getPublic(): array
    {
        $data = static::getAll();
        return [
            'basic' => ['is_open' => $data['basic']['is_open'], 'level' => $data['basic']['level']],
            'settlement' => [
                'pay_type' => $data['settlement']['pay_type'],
                'min_money' => $data['settlement']['min_money'],
            ],
            'words' => $data['words'],
            'license' => $data['license'],
            'background' => $data['background'],
        ];
    }
}