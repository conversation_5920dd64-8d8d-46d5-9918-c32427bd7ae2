<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2025 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\store\controller\sharp;

use think\response\Json;
use app\store\controller\Controller;
use app\store\model\sharp\Goods as SharpGoodsModel;
use cores\exception\BaseException;

/**
 * 秒杀商品管理
 * Class Goods
 * @package app\store\controller\apps\sharp
 */
class Goods extends Controller
{
    /**
     * 秒杀商品列表
     * @param string $search 搜索内容
     * @return Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function list(string $search = ''): Json
    {
        $model = new SharpGoodsModel;
        $list = $model->getList($search);
        return $this->renderSuccess(compact('list'));
    }

    /**
     * 秒杀商品详情
     * @param int $sharpGoodsId 秒杀商品ID
     * @return Json
     * @throws BaseException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function detail(int $sharpGoodsId): Json
    {
        $model = new SharpGoodsModel;
        $detail = $model->getDetail($sharpGoodsId);
        return $this->renderSuccess(compact('detail'));
    }

    /**
     * 一键添加秒杀商品
     * @return Json
     * @throws BaseException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function add(): Json
    {
        $model = new SharpGoodsModel;
        if (!$model->add($this->postForm())) {
            return $this->renderError($model->getError());
        }
        return $this->renderSuccess('添加成功，请在编辑页设置秒杀价格');
    }

    /**
     * 编辑秒杀商品
     * @param int $sharpGoodsId 秒杀商品ID
     * @return Json
     * @throws BaseException
     */
    public function edit(int $sharpGoodsId): Json
    {
        $model = SharpGoodsModel::getFl($sharpGoodsId, 'sharp-goods1');
        if ($model->edit($this->postForm())) {
            return $this->renderSuccess('更新成功');
        }
        return $this->renderError($model->getError() ?: '更新失败');
    }

    /**
     * 删除秒杀商品
     * @param int $sharpGoodsId 秒杀商品ID
     * @return Json
     * @throws BaseException
     */
    public function delete(int $sharpGoodsId): Json
    {
        $model = SharpGoodsModel::getFl($sharpGoodsId, 'sharp-goods1');
        if (!$model->setDelete()) {
            return $this->renderError($model->getError() ?: '删除失败');
        }
        return $this->renderSuccess('删除成功');
    }
}