<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2023 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\store\model\mp\alipay;

use think\facade\Cache;
use app\common\library\helper;
use app\common\exception\BaseException;
use app\common\model\mp\alipay\Setting as SettingModel;

/**
 * 支付宝小程序设置模型
 * Class Setting
 * @package app\store\model\mp\alipay
 */
class Setting extends SettingModel
{
    /**
     * 设置项描述
     * @var array
     */
    private array $describe = [
        'basic' => '基础设置',
        'customer' => '客服设置'
    ];

    /**
     * 更新系统设置
     * @param string $key
     * @param array $values
     * @return bool
     * @throws BaseException
     */
    public function edit(string $key, array $values): bool
    {
        $model = self::detail($key) ?: $this;
        // 删除支付宝小程序设置缓存
        Cache::delete('mp_alipay_setting_' . self::$storeId);
        
        // 根据不同的设置项处理不同的字段
        $allowedFields = $this->getAllowedFields($key);
        
        // 保存设置
        return $model->save([
            'key' => $key,
            'describe' => $this->describe[$key],
            'values' => helper::pick($values, $allowedFields),
            'update_time' => time(),
            'store_id' => self::$storeId,
        ]);
    }

    /**
     * 获取允许的字段列表
     * @param string $key
     * @return array
     */
    private function getAllowedFields(string $key): array
    {
        $fieldsMap = [
            'basic' => [
                'enabled', 'appId', 'signType', 'signMode', 
                'alipayPublicKey', 'appCertPublicKey', 'alipayCertPublicKey', 
                'alipayRootCert', 'merchantPrivateKey'
            ],
            'customer' => [
                'enabled'
            ]
        ];
        
        return $fieldsMap[$key] ?? [];
    }

    /**
     * 获取支付宝小程序基础设置
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function getBasic(): array
    {
        $data = static::getItem('basic');
        return [
            'enabled' => $data['enabled'] ?? 0,
            'appId' => $data['appId'] ?? '',
            'signType' => $data['signType'] ?? 'RSA2',
            'signMode' => $data['signMode'] ?? 10,
            'alipayPublicKey' => $data['alipayPublicKey'] ?? '',
            'appCertPublicKey' => $data['appCertPublicKey'] ?? '',
            'alipayCertPublicKey' => $data['alipayCertPublicKey'] ?? '',
            'alipayRootCert' => $data['alipayRootCert'] ?? '',
            'merchantPrivateKey' => $data['merchantPrivateKey'] ?? '',
        ];
    }
}
