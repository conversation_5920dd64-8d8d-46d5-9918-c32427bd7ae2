<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
namespace app\api\service\points;

use app\common\service\BaseService;
use app\api\model\Setting as SettingModel;
use app\api\model\points\mall\Range as PMRangeModel;
use app\api\model\points\mall\Setting as PMSettingModel;
use app\api\model\points\mall\Category as PMCategoryModel;
use app\api\service\User as UserService;

/**
 * 服务类: 积分商城
 * Class Mall
 * @package app\api\service\points
 */
class Mall extends BaseService
{
    /**
     * 获取积分商城首页数据
     * @return array
     * @throws \cores\exception\BaseException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getPageData(): array
    {
        return [
            // 当前用户是否登录
            'isLogin' => UserService::isLogin(false),
            // 当前用户信息
            'personal' => $this->getPersonal(),
            // 积分商城设置
            'setting' => PMSettingModel::getBasic(),
            // 积分设置: 名称
            'pointsSetting' => ['points_name' => SettingModel::getPointsName()],
            // 积分商品分类
            'categoryList' => $this->getCategoryList(),
            // 积分范围列表
            'rangeList' => $this->getRangeList(),
        ];
    }

    /**
     * 获取积分商品分类
     * @return \think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    private function getCategoryList(): \think\Collection
    {
        $model = new PMCategoryModel;
        return $model->getShowList();
    }

    /**
     * 获取积分范围列表
     * @return \think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    private function getRangeList(): \think\Collection
    {
        $model = new PMRangeModel;
        return $model->getShowList();
    }

    /**
     * 获取当前登录用户个人信息 (可用积分)
     * @return array|null
     * @throws \cores\exception\BaseException
     */
    public function getPersonal(): ?array
    {
        $userInfo = UserService::getCurrentLoginUser(false);
        return $userInfo ? [
            'user_id' => $userInfo['user_id'],
            'nick_name' => $userInfo['nick_name'],
            'balance' => $userInfo['balance'],
            'points' => $userInfo['points']
        ] : null;
    }
}