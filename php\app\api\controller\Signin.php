<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2025 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\api\controller;

use think\response\Json;
use app\api\service\Signin as SigninService;
use app\api\service\signin\Page as SigninPageService;

/**
 * 控制器：每日签到页面
 * Class Signin
 * @package app\api\controller
 */
class Signin extends Controller
{
    /**
     * 每日签到首页数据
     * @return Json
     * @throws \cores\exception\BaseException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function home(): Json
    {
        $service = new SigninPageService;
        $data = $service->getPageData();
        return $this->renderSuccess($data);
    }

    /**
     * 每日签到事件
     * @return Json
     * @throws \cores\exception\BaseException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function report(): Json
    {
        $service = new SigninService;
        if ($service->report()) {
            return $this->renderSuccess(['reward' => $service->getReward()], '签到成功');
        }
        return $this->renderError($service->getError() ?: '签到失败');
    }
}