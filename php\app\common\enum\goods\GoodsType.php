<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2023 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\common\enum\goods;

use app\common\enum\EnumBasics;

/**
 * 枚举类：商品类型
 * Class GoodsType
 * @package app\common\enum\goods
 */
class GoodsType extends EnumBasics
{
    // 实物商品
    const PHYSICAL = 10;

    // 虚拟商品
    const VIRTUAL = 20;

    /**
     * 获取枚举数据
     * @return array
     */
    public static function data(): array
    {
        return [
            self::PHYSICAL => [
                'name' => '实物商品',
                'value' => self::PHYSICAL,
            ],
            self::VIRTUAL => [
                'name' => '虚拟商品',
                'value' => self::VIRTUAL,
            ]
        ];
    }
}