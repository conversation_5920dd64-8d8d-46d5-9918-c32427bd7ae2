<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2025 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\common\model\store;

use cores\BaseModel;
use app\common\library\helper;
use think\db\exception\DbException;
use think\db\exception\DataNotFoundException;
use think\db\exception\ModelNotFoundException;

/**
 * 商家功能模块模型
 * Class Module
 * @package app\common\model\admin
 */
class Module extends BaseModel
{
    // 定义表名
    protected $name = 'store_module';

    // 定义表主键
    protected $pk = 'store_id';

    // 当前商城开启的功能模块
    private static array $modules = [];

    /**
     * 获取器：模块内容
     * @param ?string $value
     * @return array
     */
    public function getConfigAttr(?string $value = null): array
    {
        return !empty($value) ? helper::jsonDecode($value) : [];
    }

    /**
     * 设置器：模块内容
     * @param array $value
     * @return false|string
     */
    public function setConfigAttr(array $value)
    {
        return helper::jsonEncode($value);
    }

    /**
     * 功能模块详情
     * @param int $storeId
     * @return static|array|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function detail(int $storeId)
    {
        return static::withoutGlobalScope()->where('store_id', '=', $storeId)->find();
    }

    /**
     * 获取功能模块内容
     * @param int|null $storeId
     * @return array|mixed
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public static function getModules(?int $storeId = null)
    {
        if (is_null($storeId)) {
            $static = new static;
            $storeId = $static::$storeId;
        }
        $detail = self::detail($storeId);
        $config = !empty($detail) ? $detail['config'] : [];
        if (empty($config)) {
            $default = self::getDefaultConfig();
            return self::getDefaultConfigKeys($default);
        }
        return $config;
    }

    /**
     * 获取系统默认的功能模块
     * @return array
     */
    public static function getDefaultConfig(): array
    {
        // 从配置文件中读取默认的功能模块  路径: config/module.php
        $config = \config('module');
        // 将系统默认功能模块设置key
        $modules = self::setDefaultConfigKey($config);
        // 过滤已开启的插件
        return self::filterDefaultConfigPlugin($modules);
    }

    /**
     * 过滤已开启的插件
     * @param array $modules
     * @return array
     */
    private static function filterDefaultConfigPlugin(array $modules): array
    {
        // 从配置文件中读取插件  路径: config/plugin.php
        $plugins = \config('plugin');
        foreach ($modules as $key => &$item) {
            // 过滤已开启的插件
            if (
                isset($item['isPlugin']) && $item['isPlugin']
                && (!isset($plugins[$item['pluginName']]) || !$plugins[$item['pluginName']]['enable'])
            ) {
                unset($modules[$key]);
                continue;
            }
            // 递归处理下级
            if (!empty($item['children'])) {
                $item['children'] = self::filterDefaultConfigPlugin($item['children']);
            }
        }
        return $modules;
    }

    /**
     * 将系统默认功能模块设置key
     * @param array $config
     * @param string $keyPrefix
     * @return array
     */
    private static function setDefaultConfigKey(array $config, string $keyPrefix = ''): array
    {
        foreach ($config as $key => &$item) {
            $item['key'] = $keyPrefix ? ($keyPrefix . '-' . $key) : $key;
            if (!empty($item['children'])) {
                $item['children'] = self::setDefaultConfigKey($item['children'], $item['key']);
            }
        }
        return $config;
    }

    /**
     * 递归获取系统默认功能模块key集
     * @param array $config
     * @return array
     */
    public static function getDefaultConfigKeys(array $config): array
    {
        $keys = [];
        foreach ($config as $item) {
            $keys[] = $item['key'];
            if (!empty($item['children'])) {
                $keys = \array_merge($keys, self::getDefaultConfigKeys($item['children']));
            }
        }
        return $keys;
    }

    /**
     * 获取当前商城开启的功能模块
     * @param int|null $storeId
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public static function getCurrentModules(?int $storeId = null): array
    {
        empty(self::$modules) && self::$modules = self::getModules($storeId);
        return self::$modules;
    }

    /**
     * 验证是否开启功能模块权限
     * @param string $moduleKey
     * @param int|null $storeId
     * @return bool
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public static function checkModuleKey(string $moduleKey, ?int $storeId = null): bool
    {
        return \in_array($moduleKey, self::getCurrentModules());
    }

    /**
     * 验证是否开启功能模块权限（批量）
     * @param array $moduleKeys
     * @param int|null $storeId
     * @return bool
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public static function checkModules(array $moduleKeys, ?int $storeId = null): bool
    {
        if (empty($moduleKeys)) {
            return true;
        }
        $filter = \array_filter($moduleKeys, fn(string $moduleKey) => self::checkModuleKey($moduleKey, $storeId));
        return \count($filter) > 0;
    }

    /**
     * 过滤开启的功能模块（批量）
     * @param array $array
     * @param int|null $storeId
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public static function filterModules(array $array, ?int $storeId = null): array
    {
        return \array_filter($array,
            fn($item) => empty($item['moduleKey']) || self::checkModuleKey($item['moduleKey'], $storeId));
    }
}
