<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2025 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\common\model\sharp;

use cores\BaseModel;
use app\common\library\helper;
use app\common\model\sharp\Goods as SharpGoodsModel;
use think\model\Collection;
use think\model\relation\BelongsTo;

/**
 * 整点秒杀-活动会场与商品关联模型
 * Class ActiveGoods
 * @package app\common\model\sharp
 */
class ActiveGoods extends BaseModel
{
    // 定义表名
    protected $name = 'sharp_active_goods';

    // 定义主键
    protected $pk = 'id';

    /**
     * 关联活动会场表
     * @return BelongsTo
     */
    public function active(): BelongsTo
    {
        return $this->belongsTo('Active', 'active_id');
    }

    /**
     * 关联活动会场场次表
     * @return BelongsTo
     */
    public function activeTime(): BelongsTo
    {
        return $this->belongsTo('ActiveTime', 'active_time_id');
    }

    /**
     * 根据活动场次ID获取商品列表
     * @param int $activeTimeId 场次ID
     * @param array $goodsParam 商品参数
     * @return Collection|\think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function getGoodsListByActiveTimeId(int $activeTimeId, array $goodsParam = [])
    {
        // 根据活动场次ID获取商品集
        $model = new static;
        $activeGoodsList = $model->getSharpGoodsByActiveTimeId($activeTimeId);
        if ($activeGoodsList->isEmpty()) {
            return $activeGoodsList;
        }
        // 将列表的索引值设置为商品ID
        $activeGoodsList = helper::arrayColumn2Key($activeGoodsList, 'sharp_goods_id');
        // 获取秒杀商品列表
        $sharpGoodsIds = \array_keys($activeGoodsList);
        $sharpGoodsList = $model->getGoodsListByIds($sharpGoodsIds, $goodsParam);
        // 整理活动商品信息
        foreach ($sharpGoodsList as &$item) {
            // 活动商品的销量
            $item['sales_actual'] = $activeGoodsList[$item['sharp_goods_id']]['sales_actual'];
            // 商品销售进度
            $item['progress'] = $model->getProgress($item['sales_actual'], $item['seckill_stock']);
        }
        // 返回秒杀商品列表
        /* @var $sharpGoodsList Collection */
        return $sharpGoodsList->hidden(['goods']);
    }

    /**
     * 计算商品销售进度
     * @param $value1
     * @param $value2
     * @return mixed
     */
    protected function getProgress($value1, $value2)
    {
        if ($value2 <= 0) return 100;
        $progress = helper::bcdiv($value1, $value2);
        return min(100, (int)helper::bcmul($progress, 100, 0));
    }

    /**
     * 根据活动场次ID获取商品集
     * @param int $activeTimeId 场次ID
     * @return \think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    private function getSharpGoodsByActiveTimeId(int $activeTimeId): \think\Collection
    {
        return $this->where('active_time_id', '=', $activeTimeId)->select();
    }

    /**
     * 根据商品ID集获取商品列表
     * @param array $sharpGoodsIds 秒杀商品ID集
     * @param array $param 查询参数
     * @return mixed
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    protected function getGoodsListByIds(array $sharpGoodsIds, array $param = [])
    {
        return (new SharpGoodsModel)->getListByIds($sharpGoodsIds, $param);
    }
}