<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2023 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
namespace app\common\model\groupon;

use cores\BaseModel;
use think\model\relation\HasOne;

/**
 * 拼团机器人模型
 * Class Robot
 * @package app\common\model\groupon
 */
class Robot extends BaseModel
{
    // 定义表名
    protected $name = 'groupon_robot';

    // 定义主键
    protected $pk = 'robot_id';

    /**
     * 关联模型：用户头像
     * @return HasOne
     */
    public function avatar(): HasOne
    {
        $module = self::getCalledModule();
        return $this->hasOne("app\\{$module}\\model\\UploadFile", 'file_id', 'avatar_id');
    }

    /**
     * 详情信息
     * @param int $robotId
     * @return static|array|null
     */
    public static function detail(int $robotId)
    {
        return self::get($robotId);
    }
}