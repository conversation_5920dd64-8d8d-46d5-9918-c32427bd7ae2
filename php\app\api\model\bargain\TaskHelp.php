<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2023 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\api\model\bargain;

use app\api\model\bargain\Task as TaskModel;
use app\api\model\bargain\Setting as SettingModel;
use app\api\service\User as UserService;
use app\common\model\bargain\TaskHelp as TaskHelpModel;
use cores\exception\BaseException;

/**
 * 砍价任务助力记录模型
 * Class TaskHelp
 * @package app\api\model\bargain
 */
class TaskHelp extends TaskHelpModel
{
    /**
     * 隐藏的字段
     * @var array
     */
    protected $hidden = [
        'store_id',
        'create_time',
    ];

    /**
     * 获取助力列表记录
     * @param int $taskId 任务ID
     * @return \think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function getListByTaskId(int $taskId): \think\Collection
    {
        // 获取列表数据
        $list = (new static)->with(['user.avatar'])
            ->where('task_id', '=', $taskId)
            ->order(['create_time' => 'desc'])
            ->select();
        // 隐藏会员昵称
        foreach ($list as &$item) {
            $item['user']['nick_name'] = substr_cut($item['user']['nick_name']);
        }
        return $list;
    }

    /**
     * 判断当前用户是否已砍价
     * @param int $taskId
     * @return bool
     * @throws BaseException
     */
    public static function isCut(int $taskId): bool
    {
        // 当前用户ID
        if (!$userId = UserService::getCurrentLoginUserId(false)) {
            return false;
        }
        // 判断砍价任务是否存在当前用户的砍价记录
        return (bool)(new static)->where('task_id', '=', $taskId)
            ->where('user_id', '=', $userId)
            ->value('help_id');
    }

    /**
     * 验证每日帮砍的次数限制
     * @param int $userId 用户ID
     * @return bool
     * @throws BaseException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function checkHelpCutLimit(int $userId): bool
    {
        // 获取每人每日帮砍次数限制
        $limitNum =  SettingModel::getHelpCutLimitNum();
        // 获取今天总共帮砍次数
        $todayTimeStamp = strtotime(date('Y-m-d'));
        $count = (new static)->where('user_id', '=', $userId)
            ->where('create_time', '>=', $todayTimeStamp)
            ->where('create_time', '<', $todayTimeStamp + 86400)
            ->where('is_creater', '=', 0)
            ->count();
        // 判断是否超限
        if ($limitNum > 0 && $count >= $limitNum) {
            throwError("很抱歉，每人每日最多允许帮砍{$limitNum}次");
        }
        return true;
    }

    /**
     * 新增砍价任务助力记录
     * @param TaskModel $task 砍价任务详情
     * @param int $userId 用户ID
     * @param string $cutMoney 已砍金额
     * @param bool $isCreater 是否为发起人
     * @return bool|false
     */
    public function add(TaskModel $task, int $userId, string $cutMoney, bool $isCreater = false): bool
    {
        return $this->save([
            'task_id' => $task['task_id'],
            'active_id' => $task['active_id'],
            'user_id' => $userId,
            'cut_money' => $cutMoney,
            'is_creater' => $isCreater,
            'store_id' => static::$storeId,
        ]);
    }

    /**
     * 根据砍价活动ID获取正在砍价的助力信息列表
     * @param int $activeId
     * @return mixed
     */
    public function getHelpListByActiveId(int $activeId)
    {
        return $this->with(['user.avatar'])
            ->alias('help')
            ->field(['help.user_id'])
            ->join('user', 'user.user_id = help.user_id')
            ->join('bargain_task task', 'task.task_id = help.task_id')
            ->where('help.active_id', '=', $activeId)
            // is_creater 只统计发起人
            // ->where('help.is_creater', '=', 1)
            ->where('task.status', '=', 1)
            ->where('task.is_delete', '=', 0)
            ->group('help.user_id')
            ->limit(5)
            ->select();
    }

    /**
     * 根据砍价活动ID获取正在砍价的助力人数
     * @param int $activeId
     * @return mixed
     */
    public function getHelpCountByActiveId(int $activeId)
    {
        return $this->alias('help')
            ->join('user', 'user.user_id = help.user_id')
            ->join('bargain_task task', 'task.task_id = help.task_id')
            ->where('help.active_id', '=', $activeId)
            // is_creater 只统计发起人
//            ->where('help.is_creater', '=', 1)
            ->where('task.status', '=', 1)
            ->where('task.is_delete', '=', 0)
            ->group('help.user_id')
            ->count();
    }
}