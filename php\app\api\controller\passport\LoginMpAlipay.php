<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2023 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\api\controller\passport;

use app\api\controller\Controller;
use app\api\service\passport\Login as LoginService;
use app\api\validate\passport\Login as LoginValidate;
use think\response\Json;

/**
 * 支付宝小程序登录注册
 * Class LoginMpAlipay
 * @package app\api\controller\passport
 */
class LoginMpAlipay extends Controller
{
    /**
     * 支付宝小程序快捷登录 (一键登录)
     * @return Json
     * @throws \cores\exception\BaseException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\Exception
     */
    public function login(): Json
    {
        // 表单验证
        $validate = new LoginValidate;
        if (!$validate->scene('mpAlipay')->check($this->postForm())) {
            return $this->renderError($validate->getError());
        }
        // 支付宝小程序登录注册
        $LoginService = new LoginService;
        if (!$LoginService->loginMpAlipay($this->postForm())) {
            return $this->renderError($LoginService->getError());
        }
        // 返回用户信息
        return $this->renderSuccess([
            'userInfo' => $LoginService->getUserInfo(),
            'token' => $LoginService->getToken()
        ], '登录成功');
    }

    /**
     * 支付宝小程序注册用户 (绑定手机号)
     * @return Json
     * @throws \cores\exception\BaseException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\Exception
     */
    public function register(): Json
    {
        // 表单验证
        $validate = new LoginValidate;
        if (!$validate->scene('mpAlipayMobile')->check($this->postForm())) {
            return $this->renderError($validate->getError());
        }
        // 支付宝小程序注册
        $LoginService = new LoginService;
        if (!$LoginService->registerMpAlipay($this->postForm())) {
            return $this->renderError($LoginService->getError());
        }
        // 返回用户信息
        return $this->renderSuccess([
            'userInfo' => $LoginService->getUserInfo(),
            'token' => $LoginService->getToken()
        ], '注册成功');
    }
}
