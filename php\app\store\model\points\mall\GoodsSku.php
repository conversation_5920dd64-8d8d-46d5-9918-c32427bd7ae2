<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\store\model\points\mall;

use think\model\Collection;
use app\common\library\helper;
use app\store\model\GoodsSku as GoodsSkuModel;
use app\common\model\points\mall\GoodsSku as PMGoodsSkuModel;
use app\common\enum\goods\SpecType as SpecTypeEnum;

/**
 * 模型类: 积分商城-积分商品SKU
 * Class Goods
 * @package app\store\model\points\mall
 */
class GoodsSku extends PMGoodsSkuModel
{
    /**
     * 获取积分商品SKU列表(包含主商品sku的一些数据)
     * @param Collection $pmSkuList 积分商品SKU列表
     * @param int $goodsId 主商品ID
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function getCommonSkuList(Collection $pmSkuList, int $goodsId): array
    {
        // 获取主商品SKU列表
        $mainSkuData = static::getMainSkuList($goodsId);
        // 合并整理成新的数据 (商品价格、库存数量、商品sku编码)
        $data = [];
        foreach ($pmSkuList as &$item) {
            $mainItem = $mainSkuData[$item['goods_sku_id']];
            $item['goods_sku_no'] = $mainItem['goods_sku_no'];
            $item['goods_price'] = $mainItem['goods_price'];
            $item['stock_num'] = $mainItem['stock_num'];
        }
        return $data;
    }

    /**
     * 获取主商品SKU列表
     * @param int $goodsId 主商品ID
     * @return array|\think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    private static function getMainSkuList(int $goodsId)
    {
        // 获取主商品SKU列表
        $mainSkuList = GoodsSkuModel::getSkuList($goodsId);
        if ($mainSkuList->isEmpty()) {
            return [];
        }
        // 将列表数据主键设置为goods_sku_id
        return helper::arrayColumn2Key($mainSkuList->toArray(), 'goods_sku_id');
    }

    /**
     * 获取积分价格高低区间 (根据sku列表数据)
     * @param array $skuList
     * @return array
     */
    public static function getPointsPrices(array $skuList): array
    {
        $goodsPriceArr = helper::getArrayColumn($skuList, 'points_price');
        return [\min($goodsPriceArr), \max($goodsPriceArr)];
    }

    /**
     * 获取积分价格高低区间 (根据sku列表数据)
     * @param array $skuList
     * @return array
     */
    public static function getMinMaxSkuItem(array $skuList): array
    {
        $pointsPriceArr = helper::getArrayColumn($skuList, 'points_price');
        $minKeys = \array_keys($pointsPriceArr, \min($pointsPriceArr));
        $maxKeys = \array_keys($pointsPriceArr, \max($pointsPriceArr));
        $data = \array_values($skuList);
        return [$data[$minKeys[0]], $data[$maxKeys[0]]];
    }

    /**
     * 生成skuList数据(用于编辑积分商品)
     * @param int $goodsId 主商品ID
     * @param array $skuList 用户提交的SKU数据
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function getNewSkuList(int $goodsId, array $skuList): array
    {
        // 获取主商品SKU列表
        $mainSkuData = static::getMainSkuList($goodsId);
        foreach ($skuList as &$skuItem) {
            $mainItem = $mainSkuData[$skuItem['goods_sku_id']];
            $skuItem['spec_value_ids'] = $mainItem['spec_value_ids'];
            $skuItem['goods_props'] = $mainItem['goods_props'];
        }
        return $skuList;
    }

    /**
     * 生成默认skuList数据(用于新增积分商品)
     * @param int $goodsId 主商品ID
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function getDefaultSkuList(int $goodsId)
    {
        // 获取主商品SKU列表
        $data = static::getMainSkuList($goodsId);
        foreach ($data as &$skuItem) {
            $skuItem['points_price'] = 0;
            $skuItem['paid_price'] = 0;
            $skuItem['create_time'] = \time();
            $skuItem['update_time'] = \time();
        }
        return $data;
    }

    /**
     * 新增商品sku记录
     * @param int $pmGoodsId 积分商品ID
     * @param array $newSkuList
     * @param int $specType
     * @return array|bool|false
     */
    public static function add(int $pmGoodsId, int $specType = SpecTypeEnum::SINGLE, array $newSkuList = [])
    {
        // 单规格模式
        if ($specType === SpecTypeEnum::SINGLE) {
            return (new static)->save(\array_merge($newSkuList, [
                'pm_goods_id' => $pmGoodsId,
                'goods_sku_id' => 0,
                'store_id' => self::$storeId
            ]));
        } // 多规格模式
        elseif ($specType === SpecTypeEnum::MULTI) {
            // 批量写入商品sku记录
            return static::increasedFroMulti($pmGoodsId, $newSkuList);
        }
        return false;
    }

    /**
     * 更新商品sku记录
     * @param int $pmGoodsId 积分商品ID
     * @param int $specType
     * @param array $skuList
     * @return array|bool|false
     */
    public static function edit(int $pmGoodsId, int $specType = SpecTypeEnum::SINGLE, array $skuList = [])
    {
        // 删除所有的sku记录
        static::deleteAll(['pm_goods_id' => $pmGoodsId]);
        // 新增商品sku记录
        return static::add($pmGoodsId, $specType, $skuList);
    }

    /**
     * 批量写入商品sku记录
     * @param int $pmGoodsId 积分商品ID
     * @param array $skuList
     * @return array|false
     */
    private static function increasedFroMulti(int $pmGoodsId, array $skuList)
    {
        $dataset = [];
        foreach ($skuList as $skuItem) {
            $dataset[] = \array_merge($skuItem, [
                'id' => null,   // 此处的id必须是数据库自增
                'goods_sku_id' => $skuItem['goods_sku_id'],
                'points_price' => $skuItem['points_price'],
                'paid_price' => $skuItem['paid_price'] ?: 0,
                'goods_props' => $skuItem['goods_props'],
                'spec_value_ids' => $skuItem['spec_value_ids'],
                'pm_goods_id' => $pmGoodsId,
                'store_id' => self::$storeId
            ]);
        }
        return (new static)->addAll($dataset);
    }
}
