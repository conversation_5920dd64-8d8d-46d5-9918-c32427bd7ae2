<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2025 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\api\model\points\mall;

use app\api\model\Goods as GoodsModel;
use app\api\model\Setting as SettingModel;
use app\api\model\points\mall\GoodsSku as PMGoodsSkuModel;
use app\api\service\points\Mall as PointsMallService;
use app\common\model\points\mall\Goods as PMGoodsModel;
use app\common\enum\points\mall\Status as PMGoodsStatusEnum;
use app\common\library\helper;
use cores\exception\BaseException;

/**
 * 模型类: 积分商城-积分商品
 * Class Goods
 * @package app\api\model\points\mall
 */
class Goods extends PMGoodsModel
{
    /**
     * 隐藏字段
     * @var array
     */
    public $hidden = [
        'initial_sales',
        'actual_sales',
        'sort',
        'status',
        'is_delete',
        'store_id',
        'create_time',
        'update_time',
    ];

    /**
     * 获取积分商品列表
     * @param array $param 查询参数
     * @return mixed|\think\Paginator
     * @throws \think\Exception
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getList(array $param, int $listRows = 15)
    {
        // 筛选条件
        $query = $this->getQueryFilter($param);
        // 积分商品列表
        $list = $query->where('is_delete', '=', 0)
            ->order(['sort' => 'asc'])
            ->paginate($listRows);
        // 设置商品数据
        if (!$list->isEmpty()) {
            $list = $this->setGoodsListData($list, true);
        }
        return $list;
    }

    /**
     * 检索查询条件
     * @param array $param
     * @return \think\db\BaseQuery
     */
    private function getQueryFilter(array $param): \think\db\BaseQuery
    {
        // 查询条件
        $params = $this->setQueryDefaultValue($param, [
            'pmGoodsIds' => [],                         // 积分商品ID集
            'status' => PMGoodsStatusEnum::ON_SALE,     // 商品状态
            'categoryId' => 0,                          // 商品分类
            'rangeValues' => null,                      // 积分范围
            'listRows' => 15,                           // 每页数量
        ]);
        // 查询模型
        $query = $this->getNewQuery();
        // 筛选条件
        $filter = [];
        // 根据积分商品ID集
        if (\is_array($params['pmGoodsIds']) && !empty($params['pmGoodsIds'])) {
            $query->orderRaw('field(pm_goods_id, ' . \implode(',', $params['pmGoodsIds']) . ')');
            $filter[] = ['pm_goods_id', 'in', $params['pmGoodsIds']];
        }
        // 商品状态
        $params['status'] > 0 && $filter[] = ['status', '=', (int)$params['status']];
        // 商品分类ID
        $params['categoryId'] > 0 && $filter[] = ['category_id', '=', (int)$params['categoryId']];
        // 积分范围
        !empty($params['rangeValues']) && $filter[] = ['points_price_min', 'between', $params['rangeValues']];
        return $query->where($filter);
    }

    /**
     * 获取积分商品详情 (包含主商品信息, 用于页面详情)
     * @param int $pmGoodsId 积分商品ID
     * @return GoodsModel
     * @throws BaseException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getGoodsDetail(int $pmGoodsId): GoodsModel
    {
        // 获取积分商品详情
        $pmGoodsInfo = static::getFl($pmGoodsId,'points-mall-goods2');
        // 判断商品状态 (上架)
        if ($pmGoodsInfo['status'] == PMGoodsStatusEnum::OFF_SALE) {
            throwError('很抱歉，当前商品已下架');
        }
        // 获取主商品详情
        $goods = (new GoodsModel)->getDetails($pmGoodsInfo['goods_id'], false);
        // 整理积分商品信息
        $goods = $this->mergeMainGoods($pmGoodsInfo, $goods);
        // 商品sku信息
        $goods['skuList'] = $this->getGoodsSku($pmGoodsInfo['skuList'], $goods['skuList']);
        return $goods;
    }

    /**
     * 获取当前登录用户个人信息 (可用积分)
     * @return array|null
     * @throws BaseException
     */
    public function getPersonal(): ?array
    {
        return (new PointsMallService)->getPersonal();
    }

    /**
     * 获取积分设置 (积分名称)
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getPointsSetting(): array
    {
        return ['points_name' => SettingModel::getPointsName()];
    }

    /**
     * 获取积分商品基本信息
     * @param int $pmGoodsId
     * @return GoodsModel
     * @throws BaseException
     */
    public function getGoodsBasic(int $pmGoodsId): GoodsModel
    {
        // 获取积分商品详情
        $pmGoodsInfo = static::getFl($pmGoodsId,'points-mall-goods1');
        // 判断商品状态 (上架)
        if ($pmGoodsInfo['status'] == PMGoodsStatusEnum::OFF_SALE) {
            throwError('很抱歉，当前商品已下架');
        }
        // 获取主商品详情
        $goods = (new GoodsModel)->getBasic($pmGoodsInfo['goods_id'], false);
        // 隐藏冗余的属性
        $goods->hidden(GoodsModel::getHidden(['content', 'goods_images']));
        // 整理积分商品信息
        return $this->mergeMainGoods($pmGoodsInfo, $goods);
    }

    /**
     * 合并积分商品信息到主商品
     * @param mixed $pmGoodsInfo 积分商品详情
     * @param GoodsModel $goods 主商品详情
     * @return GoodsModel
     */
    private function mergeMainGoods($pmGoodsInfo, GoodsModel $goods): GoodsModel
    {
        $goods['original_price'] = $goods['goods_price_min'];
        $goods['pm_goods_id'] = $pmGoodsInfo['pm_goods_id'];
        $goods['category_id'] = $pmGoodsInfo['category_id'];
        $goods['buy_type'] = $pmGoodsInfo['buy_type'];
        $goods['points_price_min'] = $pmGoodsInfo['points_price_min'];
        $goods['paid_price_min'] = $pmGoodsInfo['paid_price_min'];
        $goods['goods_sales'] = $pmGoodsInfo['goods_sales'];
        $goods['is_restrict'] = $pmGoodsInfo['is_restrict'];
        $goods['restrict_single'] = $pmGoodsInfo['restrict_single'];
        $goods['restrict_total'] = $pmGoodsInfo['restrict_total'];
        return $goods;
    }

    /**
     * 获取积分商品的sku信息
     * @param $skuList
     * @param $mainSkuList
     * @return mixed
     */
    protected function getGoodsSku($skuList, $mainSkuList)
    {
        $skuList = helper::arrayColumn2Key($skuList, 'goods_sku_id');
        foreach ($mainSkuList as &$item) {
            $grouponSkuItem = clone $skuList[$item['goods_sku_id']];
            $item['points_price'] = $grouponSkuItem['points_price'];
            $item['paid_price'] = $grouponSkuItem['paid_price'];
            $item['original_price'] = $item['goods_price'];
        }
        return $mainSkuList;
    }

    /**
     * 获取订单提交的商品列表
     * @param int $pmGoodsId 积分商品ID
     * @param string $goodsSkuId 商品skuID
     * @param int $goodsNum 购买数量
     * @return array
     * @throws BaseException
     */
    public function getCheckoutGoodsList(int $pmGoodsId, string $goodsSkuId, int $goodsNum): array
    {
        // 积分商品详情
        $pmGoodsInfo = $this->getGoodsBasic($pmGoodsId);
        // 商品sku信息
        $pmGoodsInfo['skuInfo'] = PMGoodsSkuModel::getSkuInfo($pmGoodsInfo['goods_id'], $pmGoodsId, $goodsSkuId);
        // 商品封面 (优先sku封面)
        $pmGoodsInfo['goods_image'] = $pmGoodsInfo['skuInfo']['goods_image'] ?: $pmGoodsInfo['goods_image'];
        // 商品列表 (隐藏用不到的属性)
        $goodsList = [$pmGoodsInfo->hidden(GoodsModel::getHidden(['content', 'images', 'goods_images']))];
        foreach ($goodsList as &$item) {
            // 商品价格
            $item['goods_price'] = $item['skuInfo']['paid_price'];
            $item['line_price'] = $item['original_price'];
            // 积分兑换价格
            $item['points_price'] = $item['skuInfo']['points_price'];
            $item['paid_price'] = $item['skuInfo']['paid_price'];
            // 记录积分商品ID
            $item['goods_sku_id'] = $item['skuInfo']['goods_sku_id'];
            $item['goods_source_id'] = $item['pm_goods_id'];
            // 商品购买数量
            $item['total_num'] = $goodsNum;
            // 商品购买总金额
            $item['total_price'] = helper::bcmul($item['goods_price'], $goodsNum);
        }
        return $goodsList;
    }
}