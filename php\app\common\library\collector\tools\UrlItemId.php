<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2023 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\common\library\collector\tools;

/**
 * 根据url获取商品ID
 * Class UrlItemId
 * @package app\common\library\collector\tools
 */
class UrlItemId
{
    // 商品URL
    private string $url;

    // 商品来源
    private string $store;

    /**
     * 构造方法
     * @param string $url
     * @param string $store
     */
    public function __construct(string $url, string $store)
    {
        $this->url = $url;
        $this->store = $store;
    }

    /**
     * 根据url获取商品ID
     * @return string|null
     */
    public function getItemId(): ?string
    {
        $method = 'get' . ucfirst($this->store) . 'ItemId';
        if (method_exists($this, $method)) {
            return $this->$method($this->url);
        }
        return null;
    }

    /**
     * 根据url获取淘宝商品ID
     * url示例：https://item.taobao.com/item.htm?id=701101272400
     * @param string $url
     * @return string|null
     */
    private function getTaobaoItemId(string $url): ?string
    {
        return $this->parseQuery($url, 'id');
    }

    /**
     * 根据url获取天猫商品ID
     * url示例：https://detail.tmall.com/item.htm?id=681384164026
     * @param string $url
     * @return string|null
     */
    private function getTmallItemId(string $url): ?string
    {
        return $this->parseQuery($url, 'id');
    }

    /**
     * 根据url获取京东商品ID
     * url示例：https://item.jd.com/100006288905.html
     * @param string $url
     * @return string|null
     */
    private function getJdItemId(string $url): ?string
    {
        return $this->parseKey($url);
    }

    /**
     * 根据url获取拼多多商品ID
     * url示例：https://mobile.yangkeduo.com/goods1.html?goods_id=346226833721
     * @param string $url
     * @return string|null
     */
    private function getPddItemId(string $url): ?string
    {
        return $this->parseQuery($url, 'goods_id');
    }

    /**
     * 根据url获取阿里巴巴商品ID
     * url示例：https://detail.1688.com/offer/701191128168.html
     * @param string $url
     * @return string|null
     */
    private function getAlibabaItemId(string $url): ?string
    {
        return $this->parseKey($url);
    }

    /**
     * 获取url中的索引文件值
     * @param string $url
     * @param string $ext
     * @return string|null
     */
    private function parseKey(string $url, string $ext = 'html'): ?string
    {
        preg_match("/\/(\d+)\.{$ext}/", $url, $matches);
        if (empty($matches)) {
            return null;
        }
        return $matches[1];
    }

    /**
     * 获取url中的query参数
     * @param string $url
     * @param string $key
     * @return string|null
     */
    private function parseQuery(string $url, string $key): ?string
    {
        $parse = parse_url($url);
        if (empty($parse['query'])) {
            return null;
        }
        $result = [];
        parse_str($parse['query'], $result);
        return $result[$key] ?? null;
    }
}