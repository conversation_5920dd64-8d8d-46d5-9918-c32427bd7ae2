<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2023 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\common\model\dealer;

use cores\BaseModel;

/**
 * 分销商资金明细模型
 * Class Capital
 * @package app\common\model\dealer
 */
class Capital extends BaseModel
{
    // 定义表名
    protected $name = 'dealer_capital';

    // 定义主键
    protected $pk = 'id';

    /**
     * 分销商资金明细
     * @param array $data
     */
    public static function add(array $data)
    {
        $model = new static;
        $model->save(array_merge(['store_id' => $model::$storeId], $data));
    }
}