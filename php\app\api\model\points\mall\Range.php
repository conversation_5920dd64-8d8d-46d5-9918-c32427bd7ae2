<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\api\model\points\mall;

use app\common\model\points\mall\Range as RangeModel;

/**
 * 模型类: 积分商城-积分范围
 * Class Range
 * @package app\api\model\points\mall
 */
class Range extends RangeModel
{
    /**
     * 隐藏字段
     * @var array
     */
    protected $hidden = [
        'status',
        'sort',
        'is_delete',
        'store_id',
        'create_time',
        'update_time'
    ];

    /**
     * 获取显示的范围列表
     * @return \think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getShowList(): \think\Collection
    {
        return $this->getList(['status' => 1]);
    }
}
