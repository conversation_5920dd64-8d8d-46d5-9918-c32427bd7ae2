<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2025 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\api\service\dealer;

use app\api\model\dealer\Withdraw as WithdrawModel;
use app\api\model\PaymentTransfer as PaymentTransferModel;
use app\common\service\BaseService;
use app\common\service\Order as OrderService;
use app\api\service\dealer\TransferNotice as TransferNoticeService;
use app\common\enum\payment\Method as PaymentMethodEnum;
use app\common\enum\dealer\withdraw\PayType as PayTypeEnum;
use app\common\enum\dealer\withdraw\ApplyStatus as ApplyStatusEnum;
use app\common\enum\payment\transfer\OrderType as TransferOrderTypeEnum;
use app\common\library\payment\gateway\Driver as PaymentDriver;
use app\common\library\payment\Facade as PaymentFacade;
use cores\exception\BaseException;

/**
 * 服务类: 分销商提现 - 微信商家转账
 * Class TransferBills
 * @package app\api\service\dealer
 */
class TransferBills extends BaseService
{
    // 微信支付配置信息
    private ?PaymentDriver $paymentApp;

    // API返回的参数
    private array $result = [];

    // 微信支付配置信息
    private array $options = [];

    /**
     * 分销商提现：微信商家转账 (2025年新接口)
     * @param int $id 提现记录ID
     * @return bool
     * @throws BaseException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function mchTransfer(int $id): bool
    {
        // 获取提现记录详情
        $model = $this->getWithdrawDetail($id);
        // 获取最近的转账记录
        $lastTransferInfo = $this->getLastTransferInfo($model['id']);
        // 该提现单不存在第三方转账记录: 发起新的转账API
        if (empty($lastTransferInfo)) {
            // 微信商家转账API
            return $this->transferBills($model);
        }
        // 当最近的转账单失效时: 发起新转账API
        if (!$this->checkBillsState($lastTransferInfo['out_bill_no'])) {
            // 微信商家转账API
            return $this->transferBills($model);
        }
        // 当最近的转账单有效时: 沿用上次的记录
        $this->result = [
            'package_info' => $lastTransferInfo['package_info'],
            'out_bill_no' => $lastTransferInfo['out_bill_no'],
            'transfer_bill_no' => $lastTransferInfo['transfer_bill_no'],
        ];
        return true;
    }

    /**
     * 获取提现记录详情
     * @param int $id
     * @return WithdrawModel|array|null
     * @throws BaseException
     */
    private function getWithdrawDetail(int $id)
    {
        // 获取提现记录详情
        $model = WithdrawModel::detail($id);
        empty($model) && throwError('未找到指定的提现记录');
        // 验证提现记录是否合法
        if (!$this->validate($model)) {
            throwError('提现记录不合法');
        }
        // 验证已冻结佣金是否合法
        if (!$model->verifyUserFreezeMoney($model['user_id'], (string)$model['money'])) {
            throwError($model->getError());
        }
        return $model;
    }

    /**
     * 微信商家转账同步查询并更新状态
     * @param string $outBillNo 转账订单号
     * @return bool
     * @throws BaseException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function queryBillsNotice(string $outBillNo): bool
    {
        // 查询微信API转账单状态
        $result = $this->queryBills($outBillNo);
        // 更新商家转账订单记录和状态
        TransferNoticeService::notify([
            'outBillNo' => $result['out_bill_no'],
            'transferBillNo' => $result['transfer_bill_no'],
            'state' => $result['state']
        ], 'queryBillsNotice');
        return true;
    }

    /**
     * 记录第三方转账记录
     * @param WithdrawModel $model 提现记录详情
     * @return bool
     */
    private function recordTransfer(WithdrawModel $model): bool
    {
        $orderInfo = ['order_id' => $model['id'], 'user_id' => $model['user_id']];
        return PaymentTransferModel::record(
            $orderInfo,
            TransferOrderTypeEnum::DEALER_WITHDRAW,
            PaymentMethodEnum::WECHAT,
            $this->getResult()
        );
    }

    /**
     * 返回API参数 (给前端wx.requestMerchantTransfer使用)
     * @return array
     */
    public function getResult(): array
    {
        return [
            'appId' => $this->options[$this->options['mchType']]['appId'],
            'mchId' => $this->options[$this->options['mchType']]['mchId'],
            'package_info' => $this->result['package_info'],
            'out_bill_no' => $this->result['out_bill_no'],
            'transfer_bill_no' => $this->result['transfer_bill_no'],
        ];
    }

    /**
     * 发起微信商家转账API
     * @param WithdrawModel $model 提现记录详情
     * @return bool
     * @throws BaseException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    private function transferBills(WithdrawModel $model): bool
    {
        // 获取分销商用户微信小程序OpenID
        $openId = $model->getWeiXinOpenId($model['user_id'], $model['platform']);
        // 生成转账订单号
        $outBillNo = OrderService::createOrderNo();
        // 整理下单接口所需的附加数据
        $extra = ['openid' => $openId, 'remark' => '分销商佣金提现'];
        // 构建支付模块
        $Payment = $this->getPaymentApp();
        // 执行第三方支付下单API
        if (!$Payment->transferBills($outBillNo, (string)$model['money'], $extra)) {
            throwError($Payment->getError() ?: '发起商家转账API请求失败');
        }
        // 记录返回的参数
        $this->result = $Payment->getUnifyResult();
        // 记录第三方转账记录
        return $this->recordTransfer($model);
    }

    /**
     * 微信商户单号查询转账单
     * @param string $outBillNo 转账订单号
     * @return array|null
     * @throws BaseException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    private function queryBills(string $outBillNo): ?array
    {
        // 构建支付模块
        $Payment = $this->getPaymentApp();
        // 执行查询转账单API
        return $Payment->queryBills($outBillNo);
    }

    /**
     * 获取最近的转账记录
     * @param int $orderId 转账订单ID (提现记录ID)
     * @return PaymentTransferModel|array|mixed|\think\Model|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    private function getLastTransferInfo(int $orderId)
    {
        return PaymentTransferModel::getLastTransferInfo(
            $orderId,
            TransferOrderTypeEnum::DEALER_WITHDRAW,
            PaymentMethodEnum::WECHAT
        );
    }

    /**
     * 验证微信转账单是否可用
     * @param string $outBillNo
     * @return bool
     * @throws BaseException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    private function checkBillsState(string $outBillNo): bool
    {
        // 查询微信API转账单状态
        $result = $this->queryBills($outBillNo);
        // 当最近的转账单失效时: 发起新转账API
        // FAIL 转账失败
        // WAIT_USER_CONFIRM 待收款用户确认，可拉起微信收款确认页面进行收款确认
        return $result['state'] != 'FAIL';
    }

    /**
     * 构建第三方支付模块
     * @return PaymentDriver|null
     * @throws BaseException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    private function getPaymentApp(): ?PaymentDriver
    {
        if (empty($this->paymentApp)) {
            $client = \getPlatform();
            $this->paymentApp = PaymentFacade::store(PaymentMethodEnum::WECHAT)
                ->setOptions($this->getOptions(), $client);
        }
        return $this->paymentApp;
    }

    /**
     * 获取支付方式的配置信息
     * @return array|mixed
     * @throws BaseException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    private function getOptions()
    {
        if (empty($this->options)) {
            $this->options = WithdrawModel::getPaymentConfig(\getPlatform());
        }
        if ($this->options['mchType'] === 'provider') {
            throwError('很抱歉，微信商家转账API不支持服务商模式');
        }
        return $this->options;
    }

    /**
     * 验证提现记录是否合法
     * @param WithdrawModel $model 提现记录详情
     * @return bool
     */
    private function validate(WithdrawModel $model): bool
    {
        if ($model['pay_type'] != PayTypeEnum::WECHAT2) {
            return false;
        }
        if ($model['apply_status'] != ApplyStatusEnum::PASS) {
            return false;
        }
        return true;
    }
}