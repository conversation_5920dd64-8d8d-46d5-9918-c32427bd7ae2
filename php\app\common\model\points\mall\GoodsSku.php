<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\common\model\points\mall;

use cores\BaseModel;
use cores\exception\BaseException;
use app\common\library\helper;
use app\common\service\Goods as GoodsService;
use think\model\relation\BelongsTo;

/**
 * 模型类: 积分商城-积分商品SKU
 * Class GoodsSku
 * @package app\common\model\points\mall
 */
class GoodsSku extends BaseModel
{
    // 定义表名
    protected $name = 'points_mall_goods_sku';

    // 定义主键
    protected $pk = 'id';

    /**
     * 关联模型：积分商品
     * @return BelongsTo
     */
    public function goods(): BelongsTo
    {
        $module = self::getCalledModule();
        return $this->belongsTo("app\\{$module}\\model\\points\\mall\\Goods", 'pm_goods_id');
    }

    /**
     * 获取器：规格值ID集
     * @param $value
     * @return array|mixed
     */
    public function getSpecValueIdsAttr($value)
    {
        return helper::jsonDecode($value);
    }

    /**
     * 获取器：规格属性
     * @param $value
     * @return array|mixed
     */
    public function getGoodsPropsAttr($value)
    {
        return helper::jsonDecode($value);
    }

    /**
     * 设置器：规格值ID集
     * @param $value
     * @return false|string
     */
    public function setSpecValueIdsAttr($value)
    {
        return helper::jsonEncode($value);
    }

    /**
     * 设置器：规格属性
     * @param $value
     * @return false|string
     */
    public function setGoodsPropsAttr($value)
    {
        return helper::jsonEncode($value);
    }

    /**
     * 获取SKU信息详情
     * @param int $pmGoodsId
     * @param string $goodsSkuId
     * @return static|array|null
     */
    public static function detail(int $pmGoodsId, string $goodsSkuId)
    {
        return static::get(['pm_goods_id' => $pmGoodsId, 'goods_sku_id' => $goodsSkuId]);
    }

    /**
     * 获取秒杀商品SKU信息(指定的)
     * @param int $goodsId 主商品ID
     * @param int $pmGoodsId 积分商品ID
     * @param string $goodsSkuId 商品skuID
     * @return \app\common\model\GoodsSku|array|null
     * @throws BaseException
     */
    public static function getSkuInfo(int $goodsId, int $pmGoodsId, string $goodsSkuId)
    {
        $goodsSkuInfo = GoodsService::getSkuInfo($goodsId, $goodsSkuId);
        $pmSkuInfo = static::detail($pmGoodsId, $goodsSkuId);
        if (empty($goodsSkuInfo) || empty($pmSkuInfo)) {
            throwError('未找到SKU信息');
        }
        $goodsSkuInfo['original_price'] = $goodsSkuInfo['goods_price'];
        $goodsSkuInfo['points_price'] = $pmSkuInfo['points_price'];
        $goodsSkuInfo['paid_price'] = $pmSkuInfo['paid_price'];
        return $goodsSkuInfo;
    }
}