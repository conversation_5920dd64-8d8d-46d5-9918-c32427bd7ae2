{"remainingRequest": "D:\\2025\\gaodux\\gaodux5\\vue\\store\\node_modules\\babel-loader\\lib\\index.js!D:\\2025\\gaodux\\gaodux5\\vue\\store\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--6!D:\\2025\\gaodux\\gaodux5\\vue\\store\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\2025\\gaodux\\gaodux5\\vue\\store\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025\\gaodux\\gaodux5\\vue\\store\\src\\views\\page\\modules\\phone\\Phone.vue?vue&type=template&id=714e16ff&scoped=true&", "dependencies": [{"path": "D:\\2025\\gaodux\\gaodux5\\vue\\store\\src\\views\\page\\modules\\phone\\Phone.vue", "mtime": 1747065600000}, {"path": "D:\\2025\\gaodux\\gaodux5\\vue\\store\\babel.config.js", "mtime": 1656432000000}, {"path": "D:\\2025\\gaodux\\gaodux5\\vue\\store\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751804606056}, {"path": "D:\\2025\\gaodux\\gaodux5\\vue\\store\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751804606056}, {"path": "D:\\2025\\gaodux\\gaodux5\\vue\\store\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751804605661}, {"path": "D:\\2025\\gaodux\\gaodux5\\vue\\store\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1751804675994}, {"path": "D:\\2025\\gaodux\\gaodux5\\vue\\store\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751804606056}, {"path": "D:\\2025\\gaodux\\gaodux5\\vue\\store\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751804675994}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["render", "_vm", "_c", "_self", "ref", "staticClass", "class", "selected", "selectedIndex", "data", "page", "style", "titleTextColor", "backgroundColor", "titleBackgroundColor", "on", "click", "$event", "handelClickItem", "color", "_v", "_s", "params", "title", "_b", "attrs", "list", "items", "update", "handelDragItem", "animation", "filter", "_l", "item", "index", "key", "undrag", "inArray", "type", "undragList", "renderItemStyle", "padding", "paddingTop", "paddingLeft", "background", "dataItem", "dataIdx", "directives", "name", "rawName", "value", "expression", "borderRadius", "src", "imgUrl", "btnShape", "btnColor", "dataItm", "marginBottom", "itemMargin", "layout", "window", "length", "_e", "height", "videoUrl", "poster", "controls", "source", "defaultData", "show_type", "views_num", "image", "alt", "sticky", "paddingY", "paddingX", "searchStyle", "textAlign", "searchBg", "searchFontColor", "component", "PageIcon", "search", "placeholder", "textColor", "volumeFill", "fontSize", "text", "rowsNum", "width", "imageSize", "display", "column", "cardType", "goods_image", "show", "goodsNameRows", "goods_name", "sellingColor", "selling_point", "goods_sales", "priceColor", "goods_price_min", "line_price_min", "btnCartColor", "btnCartStyle", "borderTopWidth", "lineHeight", "borderTopColor", "lineColor", "borderTopStyle", "lineStyle", "opacity", "domProps", "innerHTML", "content", "require", "shop", "idx", "logo_url", "shop_name", "region", "province", "city", "address", "phone", "enable", "backgroundImage", "bgImage", "descTextColor", "descText", "rightTextColor", "rightText", "Icon", "arrowRight", "itemPaddingY", "demo", "helpList", "help", "hidx", "user", "avatar_url", "helpsCount", "floor_price", "original_price", "mainBtnTextColor", "mainBtnBgColor", "mainBtnText", "tagColor", "show_people", "active_sales", "groupon_price", "showCountdown", "cdTextColor", "countdownText", "cdNumBgColor", "cdNumColor", "progressColor", "progress", "salesColor", "sales_actual", "seckill_price_min", "coupon", "marginRight", "couponBgColor", "couponTextColor", "coupon_type", "reduce_price", "discount", "min_price", "receiveBgColor", "receiveTextColor", "href", "link", "target", "tabActiveFontColor", "tabActiveBgColor", "tabs", "tabItem", "active", "tabTextColor", "subName", "titleFontSize", "fontWeight", "titleFontWeight", "more", "moreTextColor", "enableIcon", "descFontSize", "descFontWeight", "desc", "points_price_min", "buy_type", "paid_price_min", "actionTools", "itm", "placement", "getPopupContainer", "$refs", "slot", "disabled", "stopPropagation", "handleActionToolItem", "staticRenderFns", "_withStripped"], "sources": ["D:/2025/gaodux/gaodux5/vue/store/src/views/page/modules/phone/Phone.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\"div\", { ref: \"phone-content\", staticClass: \"phone-content\" }, [\n    _c(\n      \"div\",\n      {\n        staticClass: \"phone-top optional\",\n        class: {\n          selected: \"page\" === _vm.selectedIndex,\n          [_vm.data.page.style.titleTextColor]: true,\n        },\n        style: { backgroundColor: _vm.data.page.style.titleBackgroundColor },\n        on: {\n          click: function ($event) {\n            return _vm.handelClickItem(\"page\")\n          },\n        },\n      },\n      [\n        _c(\n          \"p\",\n          {\n            staticClass: \"title\",\n            style: { color: _vm.data.page.style.titleTextColor },\n          },\n          [_vm._v(_vm._s(_vm.data.page.params.title))]\n        ),\n      ]\n    ),\n    _c(\n      \"div\",\n      { staticClass: \"phone-main\" },\n      [\n        _c(\n          \"draggable\",\n          _vm._b(\n            {\n              staticClass: \"content\",\n              attrs: { list: _vm.data.items },\n              on: { update: _vm.handelDragItem },\n            },\n            \"draggable\",\n            { animation: 120, filter: \".undrag\" },\n            false\n          ),\n          _vm._l(_vm.data.items, function (item, index) {\n            return _c(\n              \"div\",\n              {\n                key: index,\n                staticClass: \"devise-item optional\",\n                class: {\n                  selected: index === _vm.selectedIndex,\n                  undrag: _vm.inArray(item.type, _vm.undragList),\n                },\n                style: _vm.renderItemStyle(item),\n                on: {\n                  click: function ($event) {\n                    return _vm.handelClickItem(index)\n                  },\n                },\n              },\n              [\n                item.type == \"banner\"\n                  ? _c(\n                      \"div\",\n                      {\n                        staticClass: \"diy-banner\",\n                        style: {\n                          padding: `${item.style.paddingTop}px ${item.style.paddingLeft}px`,\n                          background: item.style.background,\n                        },\n                      },\n                      [\n                        _vm._l(item.data, function (dataItem, dataIdx) {\n                          return _c(\n                            \"div\",\n                            {\n                              directives: [\n                                {\n                                  name: \"show\",\n                                  rawName: \"v-show\",\n                                  value: dataIdx <= 1,\n                                  expression: \"dataIdx <= 1\",\n                                },\n                              ],\n                              key: `${index}_${dataIdx}_img`,\n                              staticClass: \"swiper-item\",\n                              style: {\n                                borderRadius: `${item.style.borderRadius}px`,\n                              },\n                            },\n                            [\n                              _c(\"img\", {\n                                staticClass: \"image\",\n                                attrs: { src: dataItem.imgUrl },\n                              }),\n                            ]\n                          )\n                        }),\n                        _c(\n                          \"div\",\n                          {\n                            staticClass: \"indicator-dots\",\n                            class: item.style.btnShape,\n                            style: {\n                              \"--padding-top\": `${item.style.paddingTop}px`,\n                            },\n                          },\n                          _vm._l(item.data, function (dataItem, dataIdx) {\n                            return _c(\"div\", {\n                              key: `${index}_${dataIdx}_dots`,\n                              staticClass: \"dots-item\",\n                              style: { background: item.style.btnColor },\n                            })\n                          }),\n                          0\n                        ),\n                      ],\n                      2\n                    )\n                  : item.type == \"image\"\n                  ? _c(\n                      \"div\",\n                      {\n                        staticClass: \"diy-image\",\n                        style: {\n                          padding: `${item.style.paddingTop}px ${item.style.paddingLeft}px`,\n                          background: item.style.background,\n                        },\n                      },\n                      _vm._l(item.data, function (dataItm, dataIdx) {\n                        return _c(\n                          \"div\",\n                          {\n                            key: `${index}_${dataIdx}`,\n                            staticClass: \"item-image\",\n                            style: {\n                              marginBottom: `${item.style.itemMargin}px`,\n                              borderRadius: `${item.style.borderRadius}px`,\n                            },\n                          },\n                          [\n                            _c(\"img\", {\n                              staticClass: \"image\",\n                              attrs: { src: dataItm.imgUrl },\n                            }),\n                          ]\n                        )\n                      }),\n                      0\n                    )\n                  : item.type == \"window\"\n                  ? _c(\n                      \"div\",\n                      {\n                        staticClass: \"diy-window\",\n                        style: {\n                          background: item.style.background,\n                          padding: `${item.style.paddingTop}px ${item.style.paddingLeft}px`,\n                        },\n                      },\n                      [\n                        item.style.layout > -1\n                          ? _c(\n                              \"ul\",\n                              {\n                                staticClass: \"data-list clearfix\",\n                                class: `avg-sm-${item.style.layout}`,\n                              },\n                              _vm._l(item.data, function (window, dataIdx) {\n                                return _c(\n                                  \"li\",\n                                  {\n                                    key: `${index}_${dataIdx}`,\n                                    staticClass: \"data-item\",\n                                    style: {\n                                      padding: `${item.style.paddingTop}px ${item.style.paddingLeft}px`,\n                                    },\n                                  },\n                                  [\n                                    _c(\"div\", { staticClass: \"item-image\" }, [\n                                      _c(\"img\", {\n                                        staticClass: \"image\",\n                                        attrs: { src: window.imgUrl },\n                                      }),\n                                    ]),\n                                  ]\n                                )\n                              }),\n                              0\n                            )\n                          : _c(\"div\", { staticClass: \"display\" }, [\n                              _c(\n                                \"div\",\n                                {\n                                  staticClass: \"display-left\",\n                                  style: {\n                                    padding: `${item.style.paddingTop}px ${item.style.paddingLeft}px`,\n                                  },\n                                },\n                                [\n                                  _c(\"img\", {\n                                    staticClass: \"image\",\n                                    attrs: { src: item.data[0].imgUrl },\n                                  }),\n                                ]\n                              ),\n                              _c(\"div\", { staticClass: \"display-right\" }, [\n                                item.data.length >= 2\n                                  ? _c(\n                                      \"div\",\n                                      {\n                                        staticClass: \"display-right1\",\n                                        style: {\n                                          padding: `${item.style.paddingTop}px ${item.style.paddingLeft}px`,\n                                        },\n                                      },\n                                      [\n                                        _c(\"img\", {\n                                          staticClass: \"image\",\n                                          attrs: { src: item.data[1].imgUrl },\n                                        }),\n                                      ]\n                                    )\n                                  : _vm._e(),\n                                _c(\"div\", { staticClass: \"display-right2\" }, [\n                                  item.data.length >= 3\n                                    ? _c(\n                                        \"div\",\n                                        {\n                                          staticClass: \"left\",\n                                          style: {\n                                            padding: `${item.style.paddingTop}px ${item.style.paddingLeft}px`,\n                                          },\n                                        },\n                                        [\n                                          _c(\"img\", {\n                                            staticClass: \"image\",\n                                            attrs: { src: item.data[2].imgUrl },\n                                          }),\n                                        ]\n                                      )\n                                    : _vm._e(),\n                                  item.data.length >= 4\n                                    ? _c(\n                                        \"div\",\n                                        {\n                                          staticClass: \"right\",\n                                          style: {\n                                            padding: `${item.style.paddingTop}px ${item.style.paddingLeft}px`,\n                                          },\n                                        },\n                                        [\n                                          _c(\"img\", {\n                                            staticClass: \"image\",\n                                            attrs: { src: item.data[3].imgUrl },\n                                          }),\n                                        ]\n                                      )\n                                    : _vm._e(),\n                                ]),\n                              ]),\n                            ]),\n                      ]\n                    )\n                  : item.type == \"hotZone\"\n                  ? _c(\n                      \"div\",\n                      {\n                        staticClass: \"diy-hotZone\",\n                        style: {\n                          padding: `${item.style.paddingTop}px ${item.style.paddingLeft}px`,\n                          background: item.style.background,\n                        },\n                      },\n                      [\n                        _c(\n                          \"div\",\n                          {\n                            staticClass: \"bg-image\",\n                            style: {\n                              borderRadius: `${item.style.borderRadius}px`,\n                            },\n                          },\n                          [\n                            _c(\"img\", {\n                              staticClass: \"image\",\n                              attrs: { src: item.data.imgUrl },\n                            }),\n                          ]\n                        ),\n                      ]\n                    )\n                  : item.type == \"video\"\n                  ? _c(\n                      \"div\",\n                      {\n                        staticClass: \"diy-video\",\n                        style: {\n                          padding: `${item.style.paddingTop}px ${item.style.paddingLeft}px`,\n                          background: item.style.background,\n                        },\n                      },\n                      [\n                        _c(\n                          \"video\",\n                          {\n                            style: { height: `${item.style.height}px` },\n                            attrs: {\n                              src: item.params.videoUrl,\n                              poster: item.params.poster,\n                              controls: \"\",\n                            },\n                          },\n                          [_vm._v(\"您的浏览器不支持 video 标签\")]\n                        ),\n                      ]\n                    )\n                  : item.type == \"article\"\n                  ? _c(\n                      \"div\",\n                      { staticClass: \"diy-article\" },\n                      _vm._l(\n                        item.params.source == \"choice\" && item.data.length\n                          ? item.data\n                          : item.defaultData,\n                        function (dataItm, dataIdx) {\n                          return _c(\n                            \"div\",\n                            {\n                              key: `${index}_${dataIdx}`,\n                              staticClass: \"article-item\",\n                              class: `show-type__${dataItm.show_type}`,\n                            },\n                            [\n                              dataItm.show_type == 10\n                                ? [\n                                    _c(\n                                      \"div\",\n                                      {\n                                        staticClass:\n                                          \"article-item__left flex-box\",\n                                      },\n                                      [\n                                        _c(\n                                          \"div\",\n                                          {\n                                            staticClass:\n                                              \"article-item__title twoline-hide\",\n                                          },\n                                          [\n                                            _c(\n                                              \"span\",\n                                              { staticClass: \"article-title\" },\n                                              [_vm._v(_vm._s(dataItm.title))]\n                                            ),\n                                          ]\n                                        ),\n                                        _c(\n                                          \"div\",\n                                          {\n                                            staticClass: \"article-item__footer\",\n                                          },\n                                          [\n                                            _c(\n                                              \"span\",\n                                              { staticClass: \"article-views\" },\n                                              [\n                                                _vm._v(\n                                                  _vm._s(dataItm.views_num) +\n                                                    \"次浏览\"\n                                                ),\n                                              ]\n                                            ),\n                                          ]\n                                        ),\n                                      ]\n                                    ),\n                                    _c(\n                                      \"div\",\n                                      { staticClass: \"article-item__image\" },\n                                      [\n                                        _c(\"img\", {\n                                          staticClass: \"image\",\n                                          attrs: {\n                                            src: dataItm.image,\n                                            alt: \"\",\n                                          },\n                                        }),\n                                      ]\n                                    ),\n                                  ]\n                                : _vm._e(),\n                              dataItm.show_type == 20\n                                ? [\n                                    _c(\n                                      \"div\",\n                                      { staticClass: \"article-item__title\" },\n                                      [\n                                        _c(\n                                          \"span\",\n                                          { staticClass: \"article-title\" },\n                                          [_vm._v(_vm._s(dataItm.title))]\n                                        ),\n                                      ]\n                                    ),\n                                    _c(\n                                      \"div\",\n                                      { staticClass: \"article-item__image\" },\n                                      [\n                                        _c(\"img\", {\n                                          staticClass: \"image\",\n                                          attrs: { src: dataItm.image },\n                                        }),\n                                      ]\n                                    ),\n                                    _c(\n                                      \"div\",\n                                      { staticClass: \"article-item__footer\" },\n                                      [\n                                        _c(\n                                          \"span\",\n                                          { staticClass: \"article-views\" },\n                                          [\n                                            _vm._v(\n                                              _vm._s(dataItm.views_num) +\n                                                \"次浏览\"\n                                            ),\n                                          ]\n                                        ),\n                                      ]\n                                    ),\n                                  ]\n                                : _vm._e(),\n                            ],\n                            2\n                          )\n                        }\n                      ),\n                      0\n                    )\n                  : item.type == \"search\"\n                  ? _c(\n                      \"div\",\n                      {\n                        staticClass: \"diy-search\",\n                        class: { sticky: item.params.sticky },\n                        style: {\n                          background: item.style.background,\n                          padding: `${item.style.paddingY}px ${item.style.paddingX}px`,\n                        },\n                      },\n                      [\n                        _c(\n                          \"div\",\n                          {\n                            staticClass: \"inner\",\n                            class: item.style.searchStyle,\n                          },\n                          [\n                            _c(\n                              \"div\",\n                              {\n                                staticClass: \"search-input\",\n                                style: {\n                                  textAlign: item.style.textAlign,\n                                  background: item.style.searchBg,\n                                  color: item.style.searchFontColor,\n                                },\n                              },\n                              [\n                                _c(\"a-icon\", {\n                                  staticClass: \"search-icon\",\n                                  attrs: { component: _vm.PageIcon.search },\n                                }),\n                                _c(\"span\", [\n                                  _vm._v(_vm._s(item.params.placeholder)),\n                                ]),\n                              ],\n                              1\n                            ),\n                          ]\n                        ),\n                      ]\n                    )\n                  : item.type == \"notice\"\n                  ? _c(\n                      \"div\",\n                      {\n                        staticClass: \"diy-notice\",\n                        style: { padding: `${item.style.paddingTop}px 0` },\n                      },\n                      [\n                        _c(\n                          \"div\",\n                          {\n                            staticClass: \"notice-body\",\n                            style: {\n                              background: item.style.background,\n                              color: item.style.textColor,\n                            },\n                          },\n                          [\n                            _c(\n                              \"div\",\n                              { staticClass: \"notice__icon\" },\n                              [\n                                _c(\"a-icon\", {\n                                  staticClass: \"notice-icon\",\n                                  attrs: { component: _vm.PageIcon.volumeFill },\n                                }),\n                              ],\n                              1\n                            ),\n                            _c(\n                              \"div\",\n                              {\n                                staticClass:\n                                  \"notice__text flex-box oneline-hide\",\n                                style: { fontSize: `${item.style.fontSize}px` },\n                              },\n                              [_c(\"span\", [_vm._v(_vm._s(item.params.text))])]\n                            ),\n                          ]\n                        ),\n                      ]\n                    )\n                  : item.type == \"navBar\"\n                  ? _c(\n                      \"div\",\n                      {\n                        staticClass: \"diy-navBar\",\n                        style: {\n                          padding: `${item.style.paddingTop}px 0`,\n                          background: item.style.background,\n                          color: item.style.textColor,\n                        },\n                      },\n                      [\n                        _c(\n                          \"div\",\n                          {\n                            staticClass: \"data-list clearfix\",\n                            class: `avg-sm-${item.style.rowsNum}`,\n                          },\n                          _vm._l(item.data, function (dataItm, dataIdx) {\n                            return _c(\n                              \"div\",\n                              {\n                                key: `${index}_${dataIdx}`,\n                                staticClass: \"item-nav\",\n                              },\n                              [\n                                _c(\"div\", { staticClass: \"item-image\" }, [\n                                  _c(\"img\", {\n                                    staticClass: \"image\",\n                                    style: {\n                                      width: `${item.style.imageSize}px`,\n                                      height: `${item.style.imageSize}px`,\n                                    },\n                                    attrs: { src: dataItm.imgUrl },\n                                  }),\n                                ]),\n                                _c(\n                                  \"p\",\n                                  { staticClass: \"item-text oneline-hide\" },\n                                  [_vm._v(_vm._s(dataItm.text))]\n                                ),\n                              ]\n                            )\n                          }),\n                          0\n                        ),\n                      ]\n                    )\n                  : item.type == \"goods\"\n                  ? _c(\n                      \"div\",\n                      {\n                        staticClass: \"diy-goods\",\n                        style: {\n                          background: item.style.background,\n                          padding: `${item.style.paddingY}px ${item.style.paddingX}px`,\n                        },\n                      },\n                      [\n                        _c(\n                          \"div\",\n                          {\n                            staticClass: \"goods-list\",\n                            class: [\n                              `display-${item.style.display}`,\n                              `column-${item.style.column}`,\n                            ],\n                            style: {\n                              marginBottom: `-${item.style.itemMargin}px`,\n                            },\n                          },\n                          _vm._l(\n                            item.params.source == \"choice\" && item.data.length\n                              ? item.data\n                              : item.defaultData,\n                            function (dataItm, dataIdx) {\n                              return _c(\n                                \"div\",\n                                {\n                                  key: `${index}_${dataIdx}`,\n                                  staticClass: \"goods-item\",\n                                  class: [`display-${item.style.cardType}`],\n                                  style: {\n                                    marginBottom: `${item.style.itemMargin}px`,\n                                    borderRadius: `${item.style.borderRadius}px`,\n                                  },\n                                },\n                                [\n                                  item.style.column == 1\n                                    ? [\n                                        _c(\"div\", { staticClass: \"flex\" }, [\n                                          _c(\n                                            \"div\",\n                                            { staticClass: \"goods-item-left\" },\n                                            [\n                                              _c(\"img\", {\n                                                staticClass: \"image\",\n                                                attrs: {\n                                                  src: dataItm.goods_image,\n                                                },\n                                              }),\n                                            ]\n                                          ),\n                                          _c(\n                                            \"div\",\n                                            { staticClass: \"goods-item-right\" },\n                                            [\n                                              _c(\n                                                \"div\",\n                                                { staticClass: \"goods-info\" },\n                                                [\n                                                  _vm.inArray(\n                                                    \"goodsName\",\n                                                    item.style.show\n                                                  )\n                                                    ? _c(\n                                                        \"div\",\n                                                        {\n                                                          staticClass:\n                                                            \"goods-name\",\n                                                          class: [\n                                                            item.style\n                                                              .goodsNameRows ==\n                                                            \"two\"\n                                                              ? \"twoline-hide\"\n                                                              : \"oneline-hide\",\n                                                            `row-${item.style.goodsNameRows}`,\n                                                          ],\n                                                        },\n                                                        [\n                                                          _vm._v(\n                                                            _vm._s(\n                                                              dataItm.goods_name\n                                                            )\n                                                          ),\n                                                        ]\n                                                      )\n                                                    : _vm._e(),\n                                                  _vm.inArray(\n                                                    \"sellingPoint\",\n                                                    item.style.show\n                                                  )\n                                                    ? _c(\n                                                        \"div\",\n                                                        {\n                                                          staticClass:\n                                                            \"goods-selling\",\n                                                        },\n                                                        [\n                                                          _c(\n                                                            \"span\",\n                                                            {\n                                                              staticClass:\n                                                                \"selling oneline-hide\",\n                                                              style: {\n                                                                color:\n                                                                  item.style\n                                                                    .sellingColor,\n                                                              },\n                                                            },\n                                                            [\n                                                              _vm._v(\n                                                                _vm._s(\n                                                                  dataItm.selling_point\n                                                                )\n                                                              ),\n                                                            ]\n                                                          ),\n                                                        ]\n                                                      )\n                                                    : _vm._e(),\n                                                  _vm.inArray(\n                                                    \"goodsSales\",\n                                                    item.style.show\n                                                  )\n                                                    ? _c(\n                                                        \"div\",\n                                                        {\n                                                          staticClass:\n                                                            \"goods-sales oneline-hide\",\n                                                        },\n                                                        [\n                                                          _c(\n                                                            \"span\",\n                                                            {\n                                                              staticClass:\n                                                                \"sales\",\n                                                            },\n                                                            [\n                                                              _vm._v(\n                                                                \"已售\" +\n                                                                  _vm._s(\n                                                                    dataItm.goods_sales\n                                                                  )\n                                                              ),\n                                                            ]\n                                                          ),\n                                                        ]\n                                                      )\n                                                    : _vm._e(),\n                                                  _c(\n                                                    \"div\",\n                                                    { staticClass: \"footer\" },\n                                                    [\n                                                      _c(\n                                                        \"div\",\n                                                        {\n                                                          staticClass:\n                                                            \"goods-price oneline-hide\",\n                                                          style: {\n                                                            color:\n                                                              item.style\n                                                                .priceColor,\n                                                          },\n                                                        },\n                                                        [\n                                                          _vm.inArray(\n                                                            \"goodsPrice\",\n                                                            item.style.show\n                                                          )\n                                                            ? [\n                                                                _c(\n                                                                  \"span\",\n                                                                  {\n                                                                    staticClass:\n                                                                      \"unit\",\n                                                                  },\n                                                                  [_vm._v(\"￥\")]\n                                                                ),\n                                                                _c(\n                                                                  \"span\",\n                                                                  {\n                                                                    staticClass:\n                                                                      \"value\",\n                                                                  },\n                                                                  [\n                                                                    _vm._v(\n                                                                      _vm._s(\n                                                                        dataItm.goods_price_min\n                                                                      )\n                                                                    ),\n                                                                  ]\n                                                                ),\n                                                              ]\n                                                            : _vm._e(),\n                                                          _vm.inArray(\n                                                            \"linePrice\",\n                                                            item.style.show\n                                                          )\n                                                            ? _c(\n                                                                \"span\",\n                                                                {\n                                                                  staticClass:\n                                                                    \"line-price\",\n                                                                },\n                                                                [\n                                                                  _c(\n                                                                    \"span\",\n                                                                    {\n                                                                      staticClass:\n                                                                        \"unit\",\n                                                                    },\n                                                                    [\n                                                                      _vm._v(\n                                                                        \"￥\"\n                                                                      ),\n                                                                    ]\n                                                                  ),\n                                                                  _c(\n                                                                    \"span\",\n                                                                    {\n                                                                      staticClass:\n                                                                        \"value\",\n                                                                    },\n                                                                    [\n                                                                      _vm._v(\n                                                                        _vm._s(\n                                                                          dataItm.line_price_min\n                                                                        )\n                                                                      ),\n                                                                    ]\n                                                                  ),\n                                                                ]\n                                                              )\n                                                            : _vm._e(),\n                                                        ],\n                                                        2\n                                                      ),\n                                                      _c(\n                                                        \"div\",\n                                                        {\n                                                          directives: [\n                                                            {\n                                                              name: \"show\",\n                                                              rawName: \"v-show\",\n                                                              value:\n                                                                _vm.inArray(\n                                                                  \"cartBtn\",\n                                                                  item.style\n                                                                    .show\n                                                                ) &&\n                                                                item.style\n                                                                  .column < 3,\n                                                              expression:\n                                                                \"inArray('cartBtn', item.style.show) && item.style.column < 3\",\n                                                            },\n                                                          ],\n                                                          staticClass: \"action\",\n                                                        },\n                                                        [\n                                                          _c(\n                                                            \"div\",\n                                                            {\n                                                              staticClass:\n                                                                \"btn-cart\",\n                                                              style: {\n                                                                color:\n                                                                  item.style\n                                                                    .btnCartColor,\n                                                              },\n                                                            },\n                                                            [\n                                                              _c(\"a-icon\", {\n                                                                staticClass:\n                                                                  \"cart-icon\",\n                                                                attrs: {\n                                                                  component:\n                                                                    _vm\n                                                                      .PageIcon[\n                                                                      `jiagou${item.style.btnCartStyle}`\n                                                                    ],\n                                                                },\n                                                              }),\n                                                            ],\n                                                            1\n                                                          ),\n                                                        ]\n                                                      ),\n                                                    ]\n                                                  ),\n                                                ]\n                                              ),\n                                            ]\n                                          ),\n                                        ]),\n                                      ]\n                                    : [\n                                        _c(\n                                          \"div\",\n                                          { staticClass: \"goods-image\" },\n                                          [\n                                            _c(\"img\", {\n                                              staticClass: \"image\",\n                                              attrs: {\n                                                src: dataItm.goods_image,\n                                              },\n                                            }),\n                                          ]\n                                        ),\n                                        _c(\n                                          \"div\",\n                                          { staticClass: \"goods-info\" },\n                                          [\n                                            _vm.inArray(\n                                              \"goodsName\",\n                                              item.style.show\n                                            )\n                                              ? _c(\n                                                  \"div\",\n                                                  {\n                                                    staticClass: \"goods-name\",\n                                                    class: [\n                                                      item.style\n                                                        .goodsNameRows == \"two\"\n                                                        ? \"twoline-hide\"\n                                                        : \"oneline-hide\",\n                                                      `row-${item.style.goodsNameRows}`,\n                                                    ],\n                                                  },\n                                                  [\n                                                    _vm._v(\n                                                      _vm._s(dataItm.goods_name)\n                                                    ),\n                                                  ]\n                                                )\n                                              : _vm._e(),\n                                            _vm.inArray(\n                                              \"sellingPoint\",\n                                              item.style.show\n                                            )\n                                              ? _c(\n                                                  \"div\",\n                                                  {\n                                                    staticClass:\n                                                      \"goods-selling\",\n                                                  },\n                                                  [\n                                                    _c(\n                                                      \"span\",\n                                                      {\n                                                        staticClass:\n                                                          \"selling oneline-hide\",\n                                                        style: {\n                                                          color:\n                                                            item.style\n                                                              .sellingColor,\n                                                        },\n                                                      },\n                                                      [\n                                                        _vm._v(\n                                                          _vm._s(\n                                                            dataItm.selling_point\n                                                          )\n                                                        ),\n                                                      ]\n                                                    ),\n                                                  ]\n                                                )\n                                              : _vm._e(),\n                                            _vm.inArray(\n                                              \"goodsSales\",\n                                              item.style.show\n                                            )\n                                              ? _c(\n                                                  \"div\",\n                                                  {\n                                                    staticClass:\n                                                      \"goods-sales oneline-hide\",\n                                                  },\n                                                  [\n                                                    _c(\n                                                      \"span\",\n                                                      { staticClass: \"sales\" },\n                                                      [\n                                                        _vm._v(\n                                                          \"已售\" +\n                                                            _vm._s(\n                                                              dataItm.goods_sales\n                                                            )\n                                                        ),\n                                                      ]\n                                                    ),\n                                                  ]\n                                                )\n                                              : _vm._e(),\n                                            _c(\n                                              \"div\",\n                                              { staticClass: \"footer\" },\n                                              [\n                                                _vm.inArray(\n                                                  \"goodsPrice\",\n                                                  item.style.show\n                                                )\n                                                  ? _c(\n                                                      \"div\",\n                                                      {\n                                                        staticClass:\n                                                          \"goods-price oneline-hide\",\n                                                        style: {\n                                                          color:\n                                                            item.style\n                                                              .priceColor,\n                                                        },\n                                                      },\n                                                      [\n                                                        _c(\n                                                          \"span\",\n                                                          {\n                                                            staticClass: \"unit\",\n                                                          },\n                                                          [_vm._v(\"￥\")]\n                                                        ),\n                                                        _c(\n                                                          \"span\",\n                                                          {\n                                                            staticClass:\n                                                              \"value\",\n                                                          },\n                                                          [\n                                                            _vm._v(\n                                                              _vm._s(\n                                                                dataItm.goods_price_min\n                                                              )\n                                                            ),\n                                                          ]\n                                                        ),\n                                                        _vm.inArray(\n                                                          \"linePrice\",\n                                                          item.style.show\n                                                        )\n                                                          ? _c(\n                                                              \"span\",\n                                                              {\n                                                                staticClass:\n                                                                  \"line-price\",\n                                                              },\n                                                              [\n                                                                _c(\n                                                                  \"span\",\n                                                                  {\n                                                                    staticClass:\n                                                                      \"unit\",\n                                                                  },\n                                                                  [_vm._v(\"￥\")]\n                                                                ),\n                                                                _c(\n                                                                  \"span\",\n                                                                  {\n                                                                    staticClass:\n                                                                      \"value\",\n                                                                  },\n                                                                  [\n                                                                    _vm._v(\n                                                                      _vm._s(\n                                                                        dataItm.line_price_min\n                                                                      )\n                                                                    ),\n                                                                  ]\n                                                                ),\n                                                              ]\n                                                            )\n                                                          : _vm._e(),\n                                                      ]\n                                                    )\n                                                  : _vm._e(),\n                                                _c(\n                                                  \"div\",\n                                                  {\n                                                    directives: [\n                                                      {\n                                                        name: \"show\",\n                                                        rawName: \"v-show\",\n                                                        value:\n                                                          _vm.inArray(\n                                                            \"cartBtn\",\n                                                            item.style.show\n                                                          ) &&\n                                                          item.style.column < 3,\n                                                        expression:\n                                                          \"inArray('cartBtn', item.style.show) && item.style.column < 3\",\n                                                      },\n                                                    ],\n                                                    staticClass: \"action\",\n                                                  },\n                                                  [\n                                                    _c(\n                                                      \"div\",\n                                                      {\n                                                        staticClass: \"btn-cart\",\n                                                        style: {\n                                                          color:\n                                                            item.style\n                                                              .btnCartColor,\n                                                        },\n                                                      },\n                                                      [\n                                                        _c(\"a-icon\", {\n                                                          staticClass:\n                                                            \"cart-icon\",\n                                                          attrs: {\n                                                            component:\n                                                              _vm.PageIcon[\n                                                                `jiagou${item.style.btnCartStyle}`\n                                                              ],\n                                                          },\n                                                        }),\n                                                      ],\n                                                      1\n                                                    ),\n                                                  ]\n                                                ),\n                                              ]\n                                            ),\n                                          ]\n                                        ),\n                                      ],\n                                ],\n                                2\n                              )\n                            }\n                          ),\n                          0\n                        ),\n                      ]\n                    )\n                  : item.type == \"blank\"\n                  ? _c(\"div\", {\n                      staticClass: \"diy-blank\",\n                      style: {\n                        height: `${item.style.height}px`,\n                        background: item.style.background,\n                      },\n                    })\n                  : item.type == \"guide\"\n                  ? _c(\n                      \"div\",\n                      {\n                        staticClass: \"diy-guide\",\n                        style: {\n                          padding: `${item.style.paddingTop}px 0`,\n                          background: item.style.background,\n                        },\n                      },\n                      [\n                        _c(\"p\", {\n                          staticClass: \"line\",\n                          style: {\n                            borderTopWidth: item.style.lineHeight + \"px\",\n                            borderTopColor: item.style.lineColor,\n                            borderTopStyle: item.style.lineStyle,\n                          },\n                        }),\n                      ]\n                    )\n                  : item.type == \"service\"\n                  ? _c(\n                      \"div\",\n                      {\n                        staticClass: \"diy-service\",\n                        style: { opacity: item.style.opacity / 100 },\n                      },\n                      [\n                        _c(\"div\", { staticClass: \"service-icon\" }, [\n                          _c(\"img\", {\n                            staticClass: \"image\",\n                            attrs: { src: item.params.image, alt: \"\" },\n                          }),\n                        ]),\n                      ]\n                    )\n                  : item.type == \"richText\"\n                  ? _c(\"div\", {\n                      staticClass: \"diy-richText\",\n                      style: {\n                        background: item.style.background,\n                        padding: `${item.style.paddingTop}px ${item.style.paddingLeft}px`,\n                      },\n                      domProps: { innerHTML: _vm._s(item.params.content) },\n                    })\n                  : item.type == \"officialAccount\"\n                  ? _c(\"div\", { staticClass: \"diy-officialAccount\" }, [\n                      _c(\"div\", { staticClass: \"item-top\" }, [\n                        _c(\"span\", [_vm._v(\"关联的公众号\")]),\n                      ]),\n                      _c(\"div\", { staticClass: \"item-content\" }, [\n                        _c(\"div\", { staticClass: \"item-cont-avatar\" }, [\n                          _c(\"img\", {\n                            staticClass: \"image\",\n                            attrs: {\n                              src: require(\"@/assets/img/circular.png\"),\n                              alt: \"\",\n                            },\n                          }),\n                        ]),\n                        _c(\"div\", { staticClass: \"item-cont-public\" }, [\n                          _c(\"div\", { staticClass: \"public-name\" }, [\n                            _c(\"span\", [_vm._v(\"公众号名称\")]),\n                          ]),\n                          _c(\"div\", { staticClass: \"public-describe\" }, [\n                            _c(\"span\", [\n                              _vm._v(\"公众号简介公众号简介公众号简介\"),\n                            ]),\n                          ]),\n                        ]),\n                        _c(\"div\", { staticClass: \"item-cont-active\" }, [\n                          _c(\"div\", { staticClass: \"active-btn\" }, [\n                            _c(\"span\", [_vm._v(\"关注\")]),\n                          ]),\n                        ]),\n                      ]),\n                    ])\n                  : item.type == \"shop\"\n                  ? _c(\n                      \"div\",\n                      {\n                        staticClass: \"diy-shop\",\n                        style: { background: item.style.background },\n                      },\n                      _vm._l(\n                        item.params.source == \"choice\" && item.data.length\n                          ? item.data\n                          : item.defaultData,\n                        function (shop, idx) {\n                          return _c(\n                            \"div\",\n                            { key: idx, staticClass: \"shop-item\" },\n                            [\n                              _vm.inArray(\"logo\", item.style.show)\n                                ? _c(\n                                    \"div\",\n                                    { staticClass: \"shop-item__logo\" },\n                                    [\n                                      _c(\"img\", {\n                                        staticClass: \"image\",\n                                        attrs: {\n                                          src: shop.logo_url,\n                                          alt: \"门店logo\",\n                                        },\n                                      }),\n                                    ]\n                                  )\n                                : _vm._e(),\n                              _c(\"div\", { staticClass: \"shop-item__content\" }, [\n                                _c(\"div\", { staticClass: \"shop-item__title\" }, [\n                                  _c(\"span\", [_vm._v(_vm._s(shop.shop_name))]),\n                                ]),\n                                _vm.inArray(\"address\", item.style.show)\n                                  ? _c(\n                                      \"div\",\n                                      {\n                                        staticClass:\n                                          \"shop-item__address oneline-hide\",\n                                      },\n                                      [\n                                        _c(\"span\", [\n                                          _vm._v(\n                                            \"门店地址：\" +\n                                              _vm._s(shop.region.province) +\n                                              _vm._s(shop.region.city) +\n                                              _vm._s(shop.region.region) +\n                                              _vm._s(shop.address)\n                                          ),\n                                        ]),\n                                      ]\n                                    )\n                                  : _vm._e(),\n                                _vm.inArray(\"phone\", item.style.show)\n                                  ? _c(\n                                      \"div\",\n                                      { staticClass: \"shop-item__phone\" },\n                                      [\n                                        _c(\"span\", [\n                                          _vm._v(\n                                            \"联系电话：\" + _vm._s(shop.phone)\n                                          ),\n                                        ]),\n                                      ]\n                                    )\n                                  : _vm._e(),\n                              ]),\n                            ]\n                          )\n                        }\n                      ),\n                      0\n                    )\n                  : item.type == \"bargain\"\n                  ? _c(\n                      \"div\",\n                      {\n                        staticClass: \"diy-bargain\",\n                        style: {\n                          background: item.style.background,\n                          padding: `${item.style.paddingY}px ${item.style.paddingX}px`,\n                        },\n                      },\n                      [\n                        _c(\n                          \"div\",\n                          {\n                            staticClass: \"container\",\n                            style: {\n                              borderRadius: `${item.style.borderRadius}px`,\n                            },\n                          },\n                          [\n                            item.params.title.enable\n                              ? _c(\n                                  \"div\",\n                                  {\n                                    staticClass: \"title-bar\",\n                                    style: {\n                                      backgroundImage: `url(${item.params.title.bgImage})`,\n                                    },\n                                  },\n                                  [\n                                    _c(\n                                      \"div\",\n                                      { staticClass: \"title-bar--left\" },\n                                      [\n                                        _c(\n                                          \"div\",\n                                          { staticClass: \"title-image\" },\n                                          [\n                                            _c(\"img\", {\n                                              staticClass: \"image\",\n                                              attrs: {\n                                                src: item.params.title.image,\n                                                alt: \"\",\n                                              },\n                                            }),\n                                          ]\n                                        ),\n                                        _c(\n                                          \"div\",\n                                          {\n                                            staticClass: \"title-desc\",\n                                            style: {\n                                              color:\n                                                item.style.title.descTextColor,\n                                            },\n                                          },\n                                          [\n                                            _c(\"span\", [\n                                              _vm._v(\n                                                _vm._s(\n                                                  item.params.title.descText\n                                                )\n                                              ),\n                                            ]),\n                                          ]\n                                        ),\n                                      ]\n                                    ),\n                                    _c(\n                                      \"div\",\n                                      { staticClass: \"title-bar--right\" },\n                                      [\n                                        _c(\n                                          \"div\",\n                                          {\n                                            staticClass: \"title-more\",\n                                            style: {\n                                              color:\n                                                item.style.title.rightTextColor,\n                                            },\n                                          },\n                                          [\n                                            _c(\n                                              \"span\",\n                                              { staticClass: \"more-text\" },\n                                              [\n                                                _vm._v(\n                                                  _vm._s(\n                                                    item.params.title.rightText\n                                                  )\n                                                ),\n                                              ]\n                                            ),\n                                            _c(\n                                              \"span\",\n                                              { staticClass: \"more-arrow\" },\n                                              [\n                                                _c(\"a-icon\", {\n                                                  attrs: {\n                                                    component:\n                                                      _vm.Icon.arrowRight,\n                                                  },\n                                                }),\n                                              ],\n                                              1\n                                            ),\n                                          ]\n                                        ),\n                                      ]\n                                    ),\n                                  ]\n                                )\n                              : _vm._e(),\n                            _c(\n                              \"div\",\n                              {\n                                staticClass: \"goods-list\",\n                                class: [\n                                  `display-${item.params.display}`,\n                                  `column-${item.params.column}`,\n                                ],\n                                style: {\n                                  marginBottom: `-${item.style.content.itemMargin}px`,\n                                  padding: `${item.style.content.itemPaddingY}px 10px`,\n                                },\n                              },\n                              _vm._l(\n                                item.params.source == \"choice\" &&\n                                  item.data.length\n                                  ? item.data\n                                  : item.defaultData,\n                                function (dataItm, dataIdx) {\n                                  return _c(\n                                    \"div\",\n                                    {\n                                      key: `${index}_${dataIdx}`,\n                                      staticClass: \"goods-item\",\n                                      style: {\n                                        marginBottom: `${item.style.content.itemMargin}px`,\n                                      },\n                                    },\n                                    [\n                                      item.params.column == 1\n                                        ? [\n                                            _c(\"div\", { staticClass: \"flex\" }, [\n                                              _c(\n                                                \"div\",\n                                                {\n                                                  staticClass:\n                                                    \"goods-item-left\",\n                                                },\n                                                [\n                                                  _c(\"img\", {\n                                                    staticClass: \"image\",\n                                                    style: {\n                                                      borderRadius: `${item.style.content.borderRadius}px`,\n                                                    },\n                                                    attrs: {\n                                                      src: dataItm.goods_image,\n                                                    },\n                                                  }),\n                                                ]\n                                              ),\n                                              _c(\n                                                \"div\",\n                                                {\n                                                  staticClass:\n                                                    \"goods-item-right\",\n                                                },\n                                                [\n                                                  _c(\n                                                    \"div\",\n                                                    {\n                                                      staticClass: \"goods-info\",\n                                                    },\n                                                    [\n                                                      _vm.inArray(\n                                                        \"goodsName\",\n                                                        item.params.show\n                                                      )\n                                                        ? _c(\n                                                            \"div\",\n                                                            {\n                                                              staticClass:\n                                                                \"goods-name\",\n                                                              class: [\n                                                                item.style\n                                                                  .content\n                                                                  .goodsNameRows ==\n                                                                \"two\"\n                                                                  ? \"twoline-hide\"\n                                                                  : \"oneline-hide\",\n                                                                `row-${item.style.goodsNameRows}`,\n                                                              ],\n                                                            },\n                                                            [\n                                                              _vm._v(\n                                                                _vm._s(\n                                                                  dataItm.goods_name\n                                                                )\n                                                              ),\n                                                            ]\n                                                          )\n                                                        : _vm._e(),\n                                                      _vm.inArray(\n                                                        \"peoples\",\n                                                        item.params.show\n                                                      )\n                                                        ? _c(\n                                                            \"div\",\n                                                            {\n                                                              staticClass:\n                                                                \"peoples\",\n                                                            },\n                                                            [\n                                                              _c(\n                                                                \"div\",\n                                                                {\n                                                                  staticClass:\n                                                                    \"user-list\",\n                                                                },\n                                                                _vm._l(\n                                                                  item.demo\n                                                                    .helpList,\n                                                                  function (\n                                                                    help,\n                                                                    hidx\n                                                                  ) {\n                                                                    return _c(\n                                                                      \"div\",\n                                                                      {\n                                                                        key: hidx,\n                                                                        staticClass:\n                                                                          \"user-item-avatar\",\n                                                                      },\n                                                                      [\n                                                                        _c(\n                                                                          \"img\",\n                                                                          {\n                                                                            staticClass:\n                                                                              \"image\",\n                                                                            attrs:\n                                                                              {\n                                                                                src: help\n                                                                                  .user\n                                                                                  .avatar_url,\n                                                                              },\n                                                                          }\n                                                                        ),\n                                                                      ]\n                                                                    )\n                                                                  }\n                                                                ),\n                                                                0\n                                                              ),\n                                                              _c(\n                                                                \"div\",\n                                                                {\n                                                                  staticClass:\n                                                                    \"people__text\",\n                                                                },\n                                                                [\n                                                                  _c(\"span\", [\n                                                                    _vm._v(\n                                                                      _vm._s(\n                                                                        item\n                                                                          .demo\n                                                                          .helpsCount\n                                                                      ) +\n                                                                        \"人正在砍价\"\n                                                                    ),\n                                                                  ]),\n                                                                ]\n                                                              ),\n                                                            ]\n                                                          )\n                                                        : _vm._e(),\n                                                      _c(\n                                                        \"div\",\n                                                        {\n                                                          staticClass: \"footer\",\n                                                        },\n                                                        [\n                                                          _c(\n                                                            \"div\",\n                                                            {\n                                                              staticClass:\n                                                                \"goods-price\",\n                                                              style: {\n                                                                color:\n                                                                  item.style\n                                                                    .content\n                                                                    .priceColor,\n                                                              },\n                                                            },\n                                                            [\n                                                              _c(\n                                                                \"div\",\n                                                                {\n                                                                  staticClass:\n                                                                    \"goods-price--row oneline-hide\",\n                                                                },\n                                                                [\n                                                                  _vm.inArray(\n                                                                    \"floorPrice\",\n                                                                    item.params\n                                                                      .show\n                                                                  )\n                                                                    ? [\n                                                                        _c(\n                                                                          \"span\",\n                                                                          {\n                                                                            staticClass:\n                                                                              \"small\",\n                                                                          },\n                                                                          [\n                                                                            _vm._v(\n                                                                              \"底价\"\n                                                                            ),\n                                                                          ]\n                                                                        ),\n                                                                        _c(\n                                                                          \"span\",\n                                                                          {\n                                                                            staticClass:\n                                                                              \"unit\",\n                                                                          },\n                                                                          [\n                                                                            _vm._v(\n                                                                              \"￥\"\n                                                                            ),\n                                                                          ]\n                                                                        ),\n                                                                        _c(\n                                                                          \"span\",\n                                                                          {\n                                                                            staticClass:\n                                                                              \"value\",\n                                                                          },\n                                                                          [\n                                                                            _vm._v(\n                                                                              _vm._s(\n                                                                                dataItm.floor_price\n                                                                              )\n                                                                            ),\n                                                                          ]\n                                                                        ),\n                                                                      ]\n                                                                    : _vm._e(),\n                                                                ],\n                                                                2\n                                                              ),\n                                                              _c(\n                                                                \"div\",\n                                                                {\n                                                                  staticClass:\n                                                                    \"goods-price--row oneline-hide\",\n                                                                },\n                                                                [\n                                                                  _vm.inArray(\n                                                                    \"originalPrice\",\n                                                                    item.params\n                                                                      .show\n                                                                  )\n                                                                    ? _c(\n                                                                        \"span\",\n                                                                        {\n                                                                          staticClass:\n                                                                            \"line-price\",\n                                                                        },\n                                                                        [\n                                                                          _c(\n                                                                            \"span\",\n                                                                            {\n                                                                              staticClass:\n                                                                                \"unit\",\n                                                                            },\n                                                                            [\n                                                                              _vm._v(\n                                                                                \"￥\"\n                                                                              ),\n                                                                            ]\n                                                                          ),\n                                                                          _c(\n                                                                            \"span\",\n                                                                            {\n                                                                              staticClass:\n                                                                                \"value\",\n                                                                            },\n                                                                            [\n                                                                              _vm._v(\n                                                                                _vm._s(\n                                                                                  dataItm.original_price\n                                                                                )\n                                                                              ),\n                                                                            ]\n                                                                          ),\n                                                                        ]\n                                                                      )\n                                                                    : _vm._e(),\n                                                                ]\n                                                              ),\n                                                            ]\n                                                          ),\n                                                          _c(\n                                                            \"div\",\n                                                            {\n                                                              directives: [\n                                                                {\n                                                                  name: \"show\",\n                                                                  rawName:\n                                                                    \"v-show\",\n                                                                  value:\n                                                                    _vm.inArray(\n                                                                      \"mainBtn\",\n                                                                      item\n                                                                        .params\n                                                                        .show\n                                                                    ) &&\n                                                                    item.params\n                                                                      .column <\n                                                                      3,\n                                                                  expression:\n                                                                    \"inArray('mainBtn', item.params.show) && item.params.column < 3\",\n                                                                },\n                                                              ],\n                                                              staticClass:\n                                                                \"action\",\n                                                            },\n                                                            [\n                                                              _c(\n                                                                \"div\",\n                                                                {\n                                                                  staticClass:\n                                                                    \"btn-main\",\n                                                                  style: {\n                                                                    color:\n                                                                      item.style\n                                                                        .content\n                                                                        .mainBtnTextColor,\n                                                                    background: `linear-gradient(to right, ${item.style.content.mainBtnBgColor})`,\n                                                                  },\n                                                                },\n                                                                [\n                                                                  _c(\"span\", [\n                                                                    _vm._v(\n                                                                      _vm._s(\n                                                                        item\n                                                                          .params\n                                                                          .mainBtnText\n                                                                      )\n                                                                    ),\n                                                                  ]),\n                                                                ]\n                                                              ),\n                                                            ]\n                                                          ),\n                                                        ]\n                                                      ),\n                                                    ]\n                                                  ),\n                                                ]\n                                              ),\n                                            ]),\n                                          ]\n                                        : [\n                                            _c(\n                                              \"div\",\n                                              {\n                                                staticClass: \"goods-image\",\n                                                style: {\n                                                  borderRadius: `${item.style.content.borderRadius}px`,\n                                                },\n                                              },\n                                              [\n                                                _c(\"img\", {\n                                                  staticClass: \"image\",\n                                                  attrs: {\n                                                    src: dataItm.goods_image,\n                                                  },\n                                                }),\n                                              ]\n                                            ),\n                                            _c(\n                                              \"div\",\n                                              { staticClass: \"goods-info\" },\n                                              [\n                                                _vm.inArray(\n                                                  \"goodsName\",\n                                                  item.params.show\n                                                )\n                                                  ? _c(\n                                                      \"div\",\n                                                      {\n                                                        staticClass:\n                                                          \"goods-name\",\n                                                        class: [\n                                                          item.style.content\n                                                            .goodsNameRows ==\n                                                          \"two\"\n                                                            ? \"twoline-hide\"\n                                                            : \"oneline-hide\",\n                                                          `row-${item.style.goodsNameRows}`,\n                                                        ],\n                                                      },\n                                                      [\n                                                        _vm._v(\n                                                          _vm._s(\n                                                            dataItm.goods_name\n                                                          )\n                                                        ),\n                                                      ]\n                                                    )\n                                                  : _vm._e(),\n                                                _c(\n                                                  \"div\",\n                                                  { staticClass: \"footer\" },\n                                                  [\n                                                    _vm.inArray(\n                                                      \"floorPrice\",\n                                                      item.params.show\n                                                    )\n                                                      ? _c(\n                                                          \"div\",\n                                                          {\n                                                            staticClass:\n                                                              \"goods-price oneline-hide\",\n                                                            style: {\n                                                              color:\n                                                                item.style\n                                                                  .content\n                                                                  .priceColor,\n                                                            },\n                                                          },\n                                                          [\n                                                            _c(\n                                                              \"span\",\n                                                              {\n                                                                staticClass:\n                                                                  \"unit\",\n                                                              },\n                                                              [_vm._v(\"￥\")]\n                                                            ),\n                                                            _c(\n                                                              \"span\",\n                                                              {\n                                                                staticClass:\n                                                                  \"value\",\n                                                              },\n                                                              [\n                                                                _vm._v(\n                                                                  _vm._s(\n                                                                    dataItm.floor_price\n                                                                  )\n                                                                ),\n                                                              ]\n                                                            ),\n                                                            _vm.inArray(\n                                                              \"originalPrice\",\n                                                              item.params.show\n                                                            )\n                                                              ? _c(\n                                                                  \"span\",\n                                                                  {\n                                                                    staticClass:\n                                                                      \"line-price\",\n                                                                  },\n                                                                  [\n                                                                    _c(\n                                                                      \"span\",\n                                                                      {\n                                                                        staticClass:\n                                                                          \"unit\",\n                                                                      },\n                                                                      [\n                                                                        _vm._v(\n                                                                          \"￥\"\n                                                                        ),\n                                                                      ]\n                                                                    ),\n                                                                    _c(\n                                                                      \"span\",\n                                                                      {\n                                                                        staticClass:\n                                                                          \"value\",\n                                                                      },\n                                                                      [\n                                                                        _vm._v(\n                                                                          _vm._s(\n                                                                            dataItm.original_price\n                                                                          )\n                                                                        ),\n                                                                      ]\n                                                                    ),\n                                                                  ]\n                                                                )\n                                                              : _vm._e(),\n                                                          ]\n                                                        )\n                                                      : _vm._e(),\n                                                  ]\n                                                ),\n                                              ]\n                                            ),\n                                          ],\n                                    ],\n                                    2\n                                  )\n                                }\n                              ),\n                              0\n                            ),\n                          ]\n                        ),\n                      ]\n                    )\n                  : item.type == \"groupon\"\n                  ? _c(\n                      \"div\",\n                      {\n                        staticClass: \"diy-groupon\",\n                        style: {\n                          background: item.style.background,\n                          padding: `${item.style.paddingY}px ${item.style.paddingX}px`,\n                        },\n                      },\n                      [\n                        _c(\n                          \"div\",\n                          {\n                            staticClass: \"container\",\n                            style: {\n                              borderRadius: `${item.style.borderRadius}px`,\n                            },\n                          },\n                          [\n                            item.params.title.enable\n                              ? _c(\n                                  \"div\",\n                                  {\n                                    staticClass: \"title-bar\",\n                                    style: {\n                                      backgroundImage: `url(${item.params.title.bgImage})`,\n                                    },\n                                  },\n                                  [\n                                    _c(\n                                      \"div\",\n                                      { staticClass: \"title-bar--left\" },\n                                      [\n                                        _c(\n                                          \"div\",\n                                          { staticClass: \"title-image\" },\n                                          [\n                                            _c(\"img\", {\n                                              staticClass: \"image\",\n                                              attrs: {\n                                                src: item.params.title.image,\n                                                alt: \"\",\n                                              },\n                                            }),\n                                          ]\n                                        ),\n                                        _c(\n                                          \"div\",\n                                          {\n                                            staticClass: \"title-desc\",\n                                            style: {\n                                              color:\n                                                item.style.title.descTextColor,\n                                            },\n                                          },\n                                          [\n                                            _c(\"span\", [\n                                              _vm._v(\n                                                _vm._s(\n                                                  item.params.title.descText\n                                                )\n                                              ),\n                                            ]),\n                                          ]\n                                        ),\n                                      ]\n                                    ),\n                                    _c(\n                                      \"div\",\n                                      { staticClass: \"title-bar--right\" },\n                                      [\n                                        _c(\n                                          \"div\",\n                                          {\n                                            staticClass: \"title-more\",\n                                            style: {\n                                              color:\n                                                item.style.title.rightTextColor,\n                                            },\n                                          },\n                                          [\n                                            _c(\n                                              \"span\",\n                                              { staticClass: \"more-text\" },\n                                              [\n                                                _vm._v(\n                                                  _vm._s(\n                                                    item.params.title.rightText\n                                                  )\n                                                ),\n                                              ]\n                                            ),\n                                            _c(\n                                              \"span\",\n                                              { staticClass: \"more-arrow\" },\n                                              [\n                                                _c(\"a-icon\", {\n                                                  attrs: {\n                                                    component:\n                                                      _vm.Icon.arrowRight,\n                                                  },\n                                                }),\n                                              ],\n                                              1\n                                            ),\n                                          ]\n                                        ),\n                                      ]\n                                    ),\n                                  ]\n                                )\n                              : _vm._e(),\n                            _c(\n                              \"div\",\n                              {\n                                staticClass: \"goods-list\",\n                                class: [\n                                  `display-${item.params.display}`,\n                                  `column-${item.params.column}`,\n                                ],\n                                style: {\n                                  marginBottom: `-${item.style.content.itemMargin}px`,\n                                  padding: `${item.style.content.itemPaddingY}px 10px`,\n                                },\n                              },\n                              _vm._l(\n                                item.params.source == \"choice\" &&\n                                  item.data.length\n                                  ? item.data\n                                  : item.defaultData,\n                                function (dataItm, dataIdx) {\n                                  return _c(\n                                    \"div\",\n                                    {\n                                      key: `${index}_${dataIdx}`,\n                                      staticClass: \"goods-item\",\n                                      style: {\n                                        marginBottom: `${item.style.content.itemMargin}px`,\n                                      },\n                                    },\n                                    [\n                                      item.params.column == 1\n                                        ? [\n                                            _c(\"div\", { staticClass: \"flex\" }, [\n                                              _c(\n                                                \"div\",\n                                                {\n                                                  staticClass:\n                                                    \"goods-item-left\",\n                                                },\n                                                [\n                                                  _c(\"img\", {\n                                                    staticClass: \"image\",\n                                                    style: {\n                                                      borderRadius: `${item.style.content.borderRadius}px`,\n                                                    },\n                                                    attrs: {\n                                                      src: dataItm.goods_image,\n                                                    },\n                                                  }),\n                                                ]\n                                              ),\n                                              _c(\n                                                \"div\",\n                                                {\n                                                  staticClass:\n                                                    \"goods-item-right\",\n                                                },\n                                                [\n                                                  _c(\n                                                    \"div\",\n                                                    {\n                                                      staticClass: \"goods-info\",\n                                                    },\n                                                    [\n                                                      _vm.inArray(\n                                                        \"goodsName\",\n                                                        item.params.show\n                                                      )\n                                                        ? _c(\n                                                            \"div\",\n                                                            {\n                                                              staticClass:\n                                                                \"goods-name\",\n                                                              class: [\n                                                                item.style\n                                                                  .content\n                                                                  .goodsNameRows ==\n                                                                \"two\"\n                                                                  ? \"twoline-hide\"\n                                                                  : \"oneline-hide\",\n                                                                `row-${item.style.goodsNameRows}`,\n                                                              ],\n                                                            },\n                                                            [\n                                                              _vm._v(\n                                                                _vm._s(\n                                                                  dataItm.goods_name\n                                                                )\n                                                              ),\n                                                            ]\n                                                          )\n                                                        : _vm._e(),\n                                                      _c(\n                                                        \"div\",\n                                                        {\n                                                          staticClass:\n                                                            \"people-sales\",\n                                                        },\n                                                        [\n                                                          _vm.inArray(\n                                                            \"people\",\n                                                            item.params.show\n                                                          )\n                                                            ? _c(\n                                                                \"div\",\n                                                                {\n                                                                  staticClass:\n                                                                    \"tag-item item-1\",\n                                                                  style: {\n                                                                    backgroundColor:\n                                                                      item.style\n                                                                        .content\n                                                                        .tagColor,\n                                                                  },\n                                                                },\n                                                                [\n                                                                  _c(\n                                                                    \"div\",\n                                                                    {\n                                                                      staticClass:\n                                                                        \"item-bg\",\n                                                                    },\n                                                                    [\n                                                                      _c(\n                                                                        \"span\",\n                                                                        [\n                                                                          _vm._v(\n                                                                            _vm._s(\n                                                                              dataItm.show_people\n                                                                            ) +\n                                                                              \"人团\"\n                                                                          ),\n                                                                        ]\n                                                                      ),\n                                                                    ]\n                                                                  ),\n                                                                ]\n                                                              )\n                                                            : _vm._e(),\n                                                          _vm.inArray(\n                                                            \"activeSales\",\n                                                            item.params.show\n                                                          )\n                                                            ? _c(\n                                                                \"div\",\n                                                                {\n                                                                  staticClass:\n                                                                    \"tag-item item-2\",\n                                                                  style: {\n                                                                    backgroundColor:\n                                                                      item.style\n                                                                        .content\n                                                                        .tagColor,\n                                                                    color:\n                                                                      item.style\n                                                                        .content\n                                                                        .tagColor,\n                                                                  },\n                                                                },\n                                                                [\n                                                                  _c(\n                                                                    \"div\",\n                                                                    {\n                                                                      staticClass:\n                                                                        \"item-bg\",\n                                                                    },\n                                                                    [\n                                                                      _c(\n                                                                        \"span\",\n                                                                        [\n                                                                          _vm._v(\n                                                                            \"已拼\" +\n                                                                              _vm._s(\n                                                                                dataItm.active_sales\n                                                                              ) +\n                                                                              \"件\"\n                                                                          ),\n                                                                        ]\n                                                                      ),\n                                                                    ]\n                                                                  ),\n                                                                ]\n                                                              )\n                                                            : _vm._e(),\n                                                        ]\n                                                      ),\n                                                      _c(\n                                                        \"div\",\n                                                        {\n                                                          staticClass: \"footer\",\n                                                        },\n                                                        [\n                                                          _c(\n                                                            \"div\",\n                                                            {\n                                                              staticClass:\n                                                                \"goods-price\",\n                                                              style: {\n                                                                color:\n                                                                  item.style\n                                                                    .content\n                                                                    .priceColor,\n                                                              },\n                                                            },\n                                                            [\n                                                              _c(\n                                                                \"div\",\n                                                                {\n                                                                  staticClass:\n                                                                    \"goods-price--row oneline-hide\",\n                                                                },\n                                                                [\n                                                                  _vm.inArray(\n                                                                    \"grouponPrice\",\n                                                                    item.params\n                                                                      .show\n                                                                  )\n                                                                    ? [\n                                                                        _c(\n                                                                          \"span\",\n                                                                          {\n                                                                            staticClass:\n                                                                              \"small\",\n                                                                          },\n                                                                          [\n                                                                            _vm._v(\n                                                                              \"拼团价\"\n                                                                            ),\n                                                                          ]\n                                                                        ),\n                                                                        _c(\n                                                                          \"span\",\n                                                                          {\n                                                                            staticClass:\n                                                                              \"unit\",\n                                                                          },\n                                                                          [\n                                                                            _vm._v(\n                                                                              \"￥\"\n                                                                            ),\n                                                                          ]\n                                                                        ),\n                                                                        _c(\n                                                                          \"span\",\n                                                                          {\n                                                                            staticClass:\n                                                                              \"value\",\n                                                                          },\n                                                                          [\n                                                                            _vm._v(\n                                                                              _vm._s(\n                                                                                dataItm.groupon_price\n                                                                              )\n                                                                            ),\n                                                                          ]\n                                                                        ),\n                                                                      ]\n                                                                    : _vm._e(),\n                                                                ],\n                                                                2\n                                                              ),\n                                                              _c(\n                                                                \"div\",\n                                                                {\n                                                                  staticClass:\n                                                                    \"goods-price--row oneline-hide\",\n                                                                },\n                                                                [\n                                                                  _vm.inArray(\n                                                                    \"originalPrice\",\n                                                                    item.params\n                                                                      .show\n                                                                  )\n                                                                    ? _c(\n                                                                        \"span\",\n                                                                        {\n                                                                          staticClass:\n                                                                            \"line-price\",\n                                                                        },\n                                                                        [\n                                                                          _c(\n                                                                            \"span\",\n                                                                            {\n                                                                              staticClass:\n                                                                                \"unit\",\n                                                                            },\n                                                                            [\n                                                                              _vm._v(\n                                                                                \"￥\"\n                                                                              ),\n                                                                            ]\n                                                                          ),\n                                                                          _c(\n                                                                            \"span\",\n                                                                            {\n                                                                              staticClass:\n                                                                                \"value\",\n                                                                            },\n                                                                            [\n                                                                              _vm._v(\n                                                                                _vm._s(\n                                                                                  dataItm.original_price\n                                                                                )\n                                                                              ),\n                                                                            ]\n                                                                          ),\n                                                                        ]\n                                                                      )\n                                                                    : _vm._e(),\n                                                                ]\n                                                              ),\n                                                            ]\n                                                          ),\n                                                          _c(\n                                                            \"div\",\n                                                            {\n                                                              directives: [\n                                                                {\n                                                                  name: \"show\",\n                                                                  rawName:\n                                                                    \"v-show\",\n                                                                  value:\n                                                                    _vm.inArray(\n                                                                      \"mainBtn\",\n                                                                      item\n                                                                        .params\n                                                                        .show\n                                                                    ) &&\n                                                                    item.params\n                                                                      .column <\n                                                                      3,\n                                                                  expression:\n                                                                    \"inArray('mainBtn', item.params.show) && item.params.column < 3\",\n                                                                },\n                                                              ],\n                                                              staticClass:\n                                                                \"action\",\n                                                            },\n                                                            [\n                                                              _c(\n                                                                \"div\",\n                                                                {\n                                                                  staticClass:\n                                                                    \"btn-main\",\n                                                                  style: {\n                                                                    color:\n                                                                      item.style\n                                                                        .content\n                                                                        .mainBtnTextColor,\n                                                                    background: `linear-gradient(to right, ${item.style.content.mainBtnBgColor})`,\n                                                                  },\n                                                                },\n                                                                [\n                                                                  _c(\"span\", [\n                                                                    _vm._v(\n                                                                      _vm._s(\n                                                                        item\n                                                                          .params\n                                                                          .mainBtnText\n                                                                      )\n                                                                    ),\n                                                                  ]),\n                                                                ]\n                                                              ),\n                                                            ]\n                                                          ),\n                                                        ]\n                                                      ),\n                                                    ]\n                                                  ),\n                                                ]\n                                              ),\n                                            ]),\n                                          ]\n                                        : [\n                                            _c(\n                                              \"div\",\n                                              {\n                                                staticClass: \"goods-image\",\n                                                style: {\n                                                  borderRadius: `${item.style.content.borderRadius}px`,\n                                                },\n                                              },\n                                              [\n                                                _c(\"img\", {\n                                                  staticClass: \"image\",\n                                                  attrs: {\n                                                    src: dataItm.goods_image,\n                                                  },\n                                                }),\n                                              ]\n                                            ),\n                                            _c(\n                                              \"div\",\n                                              { staticClass: \"goods-info\" },\n                                              [\n                                                _vm.inArray(\n                                                  \"goodsName\",\n                                                  item.params.show\n                                                )\n                                                  ? _c(\n                                                      \"div\",\n                                                      {\n                                                        staticClass:\n                                                          \"goods-name\",\n                                                        class: [\n                                                          item.style.content\n                                                            .goodsNameRows ==\n                                                          \"two\"\n                                                            ? \"twoline-hide\"\n                                                            : \"oneline-hide\",\n                                                          `row-${item.style.goodsNameRows}`,\n                                                        ],\n                                                      },\n                                                      [\n                                                        _vm._v(\n                                                          _vm._s(\n                                                            dataItm.goods_name\n                                                          )\n                                                        ),\n                                                      ]\n                                                    )\n                                                  : _vm._e(),\n                                                _c(\n                                                  \"div\",\n                                                  { staticClass: \"footer\" },\n                                                  [\n                                                    _vm.inArray(\n                                                      \"grouponPrice\",\n                                                      item.params.show\n                                                    )\n                                                      ? _c(\n                                                          \"div\",\n                                                          {\n                                                            staticClass:\n                                                              \"goods-price oneline-hide\",\n                                                            style: {\n                                                              color:\n                                                                item.style\n                                                                  .content\n                                                                  .priceColor,\n                                                            },\n                                                          },\n                                                          [\n                                                            _c(\n                                                              \"span\",\n                                                              {\n                                                                staticClass:\n                                                                  \"unit\",\n                                                              },\n                                                              [_vm._v(\"￥\")]\n                                                            ),\n                                                            _c(\n                                                              \"span\",\n                                                              {\n                                                                staticClass:\n                                                                  \"value\",\n                                                              },\n                                                              [\n                                                                _vm._v(\n                                                                  _vm._s(\n                                                                    dataItm.groupon_price\n                                                                  )\n                                                                ),\n                                                              ]\n                                                            ),\n                                                            _vm.inArray(\n                                                              \"originalPrice\",\n                                                              item.params.show\n                                                            )\n                                                              ? _c(\n                                                                  \"span\",\n                                                                  {\n                                                                    staticClass:\n                                                                      \"line-price\",\n                                                                  },\n                                                                  [\n                                                                    _c(\n                                                                      \"span\",\n                                                                      {\n                                                                        staticClass:\n                                                                          \"unit\",\n                                                                      },\n                                                                      [\n                                                                        _vm._v(\n                                                                          \"￥\"\n                                                                        ),\n                                                                      ]\n                                                                    ),\n                                                                    _c(\n                                                                      \"span\",\n                                                                      {\n                                                                        staticClass:\n                                                                          \"value\",\n                                                                      },\n                                                                      [\n                                                                        _vm._v(\n                                                                          _vm._s(\n                                                                            dataItm.original_price\n                                                                          )\n                                                                        ),\n                                                                      ]\n                                                                    ),\n                                                                  ]\n                                                                )\n                                                              : _vm._e(),\n                                                          ]\n                                                        )\n                                                      : _vm._e(),\n                                                  ]\n                                                ),\n                                              ]\n                                            ),\n                                          ],\n                                    ],\n                                    2\n                                  )\n                                }\n                              ),\n                              0\n                            ),\n                          ]\n                        ),\n                      ]\n                    )\n                  : item.type == \"sharp\"\n                  ? _c(\n                      \"div\",\n                      {\n                        staticClass: \"diy-sharp\",\n                        style: {\n                          background: item.style.background,\n                          padding: `${item.style.paddingY}px ${item.style.paddingX}px`,\n                        },\n                      },\n                      [\n                        _c(\n                          \"div\",\n                          {\n                            staticClass: \"container\",\n                            style: {\n                              borderRadius: `${item.style.borderRadius}px`,\n                            },\n                          },\n                          [\n                            item.params.title.enable\n                              ? _c(\n                                  \"div\",\n                                  {\n                                    staticClass: \"title-bar\",\n                                    style: {\n                                      backgroundImage: `url(${item.params.title.bgImage})`,\n                                    },\n                                  },\n                                  [\n                                    _c(\n                                      \"div\",\n                                      { staticClass: \"title-bar--left\" },\n                                      [\n                                        _c(\n                                          \"div\",\n                                          { staticClass: \"title-image\" },\n                                          [\n                                            _c(\"img\", {\n                                              staticClass: \"image\",\n                                              attrs: {\n                                                src: item.params.title.image,\n                                                alt: \"\",\n                                              },\n                                            }),\n                                          ]\n                                        ),\n                                        item.params.title.showCountdown\n                                          ? [\n                                              _c(\n                                                \"div\",\n                                                {\n                                                  staticClass:\n                                                    \"sharp-active-status\",\n                                                  style: {\n                                                    color:\n                                                      item.style.title\n                                                        .cdTextColor,\n                                                  },\n                                                },\n                                                [\n                                                  _c(\"span\", [\n                                                    _vm._v(\n                                                      _vm._s(\n                                                        item.params.title\n                                                          .countdownText\n                                                      )\n                                                    ),\n                                                  ]),\n                                                ]\n                                              ),\n                                              _c(\n                                                \"div\",\n                                                {\n                                                  staticClass:\n                                                    \"active-count-down\",\n                                                },\n                                                [\n                                                  _c(\n                                                    \"div\",\n                                                    {\n                                                      staticClass: \"clock flex\",\n                                                    },\n                                                    [\n                                                      _c(\n                                                        \"div\",\n                                                        {\n                                                          staticClass:\n                                                            \"clock-time\",\n                                                          style: {\n                                                            backgroundColor:\n                                                              item.style.title\n                                                                .cdNumBgColor,\n                                                            color:\n                                                              item.style.title\n                                                                .cdNumColor,\n                                                          },\n                                                        },\n                                                        [\n                                                          _c(\"span\", [\n                                                            _vm._v(\"00\"),\n                                                          ]),\n                                                        ]\n                                                      ),\n                                                      _c(\n                                                        \"div\",\n                                                        {\n                                                          staticClass:\n                                                            \"clock-symbol\",\n                                                          style: {\n                                                            color:\n                                                              item.style.title\n                                                                .cdTextColor,\n                                                          },\n                                                        },\n                                                        [\n                                                          _c(\"span\", [\n                                                            _vm._v(\":\"),\n                                                          ]),\n                                                        ]\n                                                      ),\n                                                      _c(\n                                                        \"div\",\n                                                        {\n                                                          staticClass:\n                                                            \"clock-time\",\n                                                          style: {\n                                                            backgroundColor:\n                                                              item.style.title\n                                                                .cdNumBgColor,\n                                                            color:\n                                                              item.style.title\n                                                                .cdNumColor,\n                                                          },\n                                                        },\n                                                        [\n                                                          _c(\"span\", [\n                                                            _vm._v(\"58\"),\n                                                          ]),\n                                                        ]\n                                                      ),\n                                                      _c(\n                                                        \"div\",\n                                                        {\n                                                          staticClass:\n                                                            \"clock-symbol\",\n                                                          style: {\n                                                            color:\n                                                              item.style.title\n                                                                .cdTextColor,\n                                                          },\n                                                        },\n                                                        [\n                                                          _c(\"span\", [\n                                                            _vm._v(\":\"),\n                                                          ]),\n                                                        ]\n                                                      ),\n                                                      _c(\n                                                        \"div\",\n                                                        {\n                                                          staticClass:\n                                                            \"clock-time\",\n                                                          style: {\n                                                            backgroundColor:\n                                                              item.style.title\n                                                                .cdNumBgColor,\n                                                            color:\n                                                              item.style.title\n                                                                .cdNumColor,\n                                                          },\n                                                        },\n                                                        [\n                                                          _c(\"span\", [\n                                                            _vm._v(\"04\"),\n                                                          ]),\n                                                        ]\n                                                      ),\n                                                    ]\n                                                  ),\n                                                ]\n                                              ),\n                                            ]\n                                          : _vm._e(),\n                                      ],\n                                      2\n                                    ),\n                                    _c(\n                                      \"div\",\n                                      { staticClass: \"title-bar--right\" },\n                                      [\n                                        _c(\n                                          \"div\",\n                                          {\n                                            staticClass: \"title-more\",\n                                            style: {\n                                              color:\n                                                item.style.title.rightTextColor,\n                                            },\n                                          },\n                                          [\n                                            _c(\n                                              \"span\",\n                                              { staticClass: \"more-text\" },\n                                              [\n                                                _vm._v(\n                                                  _vm._s(\n                                                    item.params.title.rightText\n                                                  )\n                                                ),\n                                              ]\n                                            ),\n                                            _c(\n                                              \"span\",\n                                              { staticClass: \"more-arrow\" },\n                                              [\n                                                _c(\"a-icon\", {\n                                                  attrs: {\n                                                    component:\n                                                      _vm.Icon.arrowRight,\n                                                  },\n                                                }),\n                                              ],\n                                              1\n                                            ),\n                                          ]\n                                        ),\n                                      ]\n                                    ),\n                                  ]\n                                )\n                              : _vm._e(),\n                            _c(\n                              \"div\",\n                              {\n                                staticClass: \"goods-list\",\n                                class: [\n                                  `display-${item.params.display}`,\n                                  `column-${item.params.column}`,\n                                ],\n                                style: {\n                                  marginBottom: `-${item.style.content.itemMargin}px`,\n                                  padding: `${item.style.content.itemPaddingY}px 10px`,\n                                },\n                              },\n                              _vm._l(item.data, function (dataItm, dataIdx) {\n                                return _c(\n                                  \"div\",\n                                  {\n                                    key: `${index}_${dataIdx}`,\n                                    staticClass: \"goods-item\",\n                                    style: {\n                                      marginBottom: `${item.style.content.itemMargin}px`,\n                                    },\n                                  },\n                                  [\n                                    item.params.column == 1\n                                      ? [\n                                          _c(\"div\", { staticClass: \"flex\" }, [\n                                            _c(\n                                              \"div\",\n                                              {\n                                                staticClass: \"goods-item-left\",\n                                              },\n                                              [\n                                                _c(\"img\", {\n                                                  staticClass: \"image\",\n                                                  style: {\n                                                    borderRadius: `${item.style.content.borderRadius}px`,\n                                                  },\n                                                  attrs: {\n                                                    src: dataItm.goods_image,\n                                                  },\n                                                }),\n                                              ]\n                                            ),\n                                            _c(\n                                              \"div\",\n                                              {\n                                                staticClass: \"goods-item-right\",\n                                              },\n                                              [\n                                                _c(\n                                                  \"div\",\n                                                  { staticClass: \"goods-info\" },\n                                                  [\n                                                    _vm.inArray(\n                                                      \"goodsName\",\n                                                      item.params.show\n                                                    )\n                                                      ? _c(\n                                                          \"div\",\n                                                          {\n                                                            staticClass:\n                                                              \"goods-name\",\n                                                            class: [\n                                                              item.style.content\n                                                                .goodsNameRows ==\n                                                              \"two\"\n                                                                ? \"twoline-hide\"\n                                                                : \"oneline-hide\",\n                                                              `row-${item.style.goodsNameRows}`,\n                                                            ],\n                                                          },\n                                                          [\n                                                            _vm._v(\n                                                              _vm._s(\n                                                                dataItm.goods_name\n                                                              )\n                                                            ),\n                                                          ]\n                                                        )\n                                                      : _vm._e(),\n                                                    _vm.inArray(\n                                                      \"progress\",\n                                                      item.params.show\n                                                    )\n                                                      ? _c(\n                                                          \"div\",\n                                                          {\n                                                            staticClass:\n                                                              \"sharp-progress\",\n                                                          },\n                                                          [\n                                                            _c(\n                                                              \"div\",\n                                                              {\n                                                                staticClass:\n                                                                  \"yoo-progress\",\n                                                                style: {\n                                                                  background: `linear-gradient(to right, ${item.style.content.progressColor})`,\n                                                                },\n                                                              },\n                                                              [\n                                                                _c(\n                                                                  \"div\",\n                                                                  {\n                                                                    staticClass:\n                                                                      \"yoo-progress--filter\",\n                                                                  },\n                                                                  [\n                                                                    _c(\"div\", {\n                                                                      staticClass:\n                                                                        \"yoo-progress--portion\",\n                                                                      style: {\n                                                                        width: `${dataItm.progress}%`,\n                                                                        background: `linear-gradient(to right, ${item.style.content.progressColor})`,\n                                                                      },\n                                                                    }),\n                                                                    _c(\n                                                                      \"span\",\n                                                                      {\n                                                                        staticClass:\n                                                                          \"yoo-progress--text\",\n                                                                      },\n                                                                      [\n                                                                        _vm._v(\n                                                                          _vm._s(\n                                                                            dataItm.progress\n                                                                          ) +\n                                                                            \"%\"\n                                                                        ),\n                                                                      ]\n                                                                    ),\n                                                                  ]\n                                                                ),\n                                                              ]\n                                                            ),\n                                                            _c(\n                                                              \"div\",\n                                                              {\n                                                                staticClass:\n                                                                  \"sharp-sales oneline-hide\",\n                                                                style: {\n                                                                  color:\n                                                                    item.style\n                                                                      .content\n                                                                      .salesColor,\n                                                                },\n                                                              },\n                                                              [\n                                                                _vm._v(\n                                                                  \"已抢\" +\n                                                                    _vm._s(\n                                                                      dataItm.sales_actual\n                                                                    ) +\n                                                                    \"件\"\n                                                                ),\n                                                              ]\n                                                            ),\n                                                          ]\n                                                        )\n                                                      : _vm._e(),\n                                                    _c(\n                                                      \"div\",\n                                                      { staticClass: \"footer\" },\n                                                      [\n                                                        _c(\n                                                          \"div\",\n                                                          {\n                                                            staticClass:\n                                                              \"goods-price\",\n                                                            style: {\n                                                              color:\n                                                                item.style\n                                                                  .content\n                                                                  .priceColor,\n                                                            },\n                                                          },\n                                                          [\n                                                            _c(\n                                                              \"div\",\n                                                              {\n                                                                staticClass:\n                                                                  \"goods-price--row oneline-hide\",\n                                                              },\n                                                              [\n                                                                _vm.inArray(\n                                                                  \"seckillPrice\",\n                                                                  item.params\n                                                                    .show\n                                                                )\n                                                                  ? [\n                                                                      _c(\n                                                                        \"span\",\n                                                                        {\n                                                                          staticClass:\n                                                                            \"small\",\n                                                                        },\n                                                                        [\n                                                                          _vm._v(\n                                                                            \"秒杀价\"\n                                                                          ),\n                                                                        ]\n                                                                      ),\n                                                                      _c(\n                                                                        \"span\",\n                                                                        {\n                                                                          staticClass:\n                                                                            \"unit\",\n                                                                        },\n                                                                        [\n                                                                          _vm._v(\n                                                                            \"￥\"\n                                                                          ),\n                                                                        ]\n                                                                      ),\n                                                                      _c(\n                                                                        \"span\",\n                                                                        {\n                                                                          staticClass:\n                                                                            \"value\",\n                                                                        },\n                                                                        [\n                                                                          _vm._v(\n                                                                            _vm._s(\n                                                                              dataItm.seckill_price_min\n                                                                            )\n                                                                          ),\n                                                                        ]\n                                                                      ),\n                                                                    ]\n                                                                  : _vm._e(),\n                                                              ],\n                                                              2\n                                                            ),\n                                                            _c(\n                                                              \"div\",\n                                                              {\n                                                                staticClass:\n                                                                  \"goods-price--row oneline-hide\",\n                                                              },\n                                                              [\n                                                                _vm.inArray(\n                                                                  \"originalPrice\",\n                                                                  item.params\n                                                                    .show\n                                                                )\n                                                                  ? _c(\n                                                                      \"span\",\n                                                                      {\n                                                                        staticClass:\n                                                                          \"line-price\",\n                                                                      },\n                                                                      [\n                                                                        _c(\n                                                                          \"span\",\n                                                                          {\n                                                                            staticClass:\n                                                                              \"unit\",\n                                                                          },\n                                                                          [\n                                                                            _vm._v(\n                                                                              \"￥\"\n                                                                            ),\n                                                                          ]\n                                                                        ),\n                                                                        _c(\n                                                                          \"span\",\n                                                                          {\n                                                                            staticClass:\n                                                                              \"value\",\n                                                                          },\n                                                                          [\n                                                                            _vm._v(\n                                                                              _vm._s(\n                                                                                dataItm.original_price\n                                                                              )\n                                                                            ),\n                                                                          ]\n                                                                        ),\n                                                                      ]\n                                                                    )\n                                                                  : _vm._e(),\n                                                              ]\n                                                            ),\n                                                          ]\n                                                        ),\n                                                        _c(\n                                                          \"div\",\n                                                          {\n                                                            directives: [\n                                                              {\n                                                                name: \"show\",\n                                                                rawName:\n                                                                  \"v-show\",\n                                                                value:\n                                                                  _vm.inArray(\n                                                                    \"mainBtn\",\n                                                                    item.params\n                                                                      .show\n                                                                  ) &&\n                                                                  item.params\n                                                                    .column < 3,\n                                                                expression:\n                                                                  \"inArray('mainBtn', item.params.show) && item.params.column < 3\",\n                                                              },\n                                                            ],\n                                                            staticClass:\n                                                              \"action\",\n                                                          },\n                                                          [\n                                                            _c(\n                                                              \"div\",\n                                                              {\n                                                                staticClass:\n                                                                  \"btn-main\",\n                                                                style: {\n                                                                  color:\n                                                                    item.style\n                                                                      .content\n                                                                      .mainBtnTextColor,\n                                                                  background: `linear-gradient(to right, ${item.style.content.mainBtnBgColor})`,\n                                                                },\n                                                              },\n                                                              [\n                                                                _c(\"span\", [\n                                                                  _vm._v(\n                                                                    _vm._s(\n                                                                      item\n                                                                        .params\n                                                                        .mainBtnText\n                                                                    )\n                                                                  ),\n                                                                ]),\n                                                              ]\n                                                            ),\n                                                          ]\n                                                        ),\n                                                      ]\n                                                    ),\n                                                  ]\n                                                ),\n                                              ]\n                                            ),\n                                          ]),\n                                        ]\n                                      : [\n                                          _c(\n                                            \"div\",\n                                            {\n                                              staticClass: \"goods-image\",\n                                              style: {\n                                                borderRadius: `${item.style.content.borderRadius}px`,\n                                              },\n                                            },\n                                            [\n                                              _c(\"img\", {\n                                                staticClass: \"image\",\n                                                attrs: {\n                                                  src: dataItm.goods_image,\n                                                },\n                                              }),\n                                            ]\n                                          ),\n                                          _c(\n                                            \"div\",\n                                            { staticClass: \"goods-info\" },\n                                            [\n                                              _vm.inArray(\n                                                \"goodsName\",\n                                                item.params.show\n                                              )\n                                                ? _c(\n                                                    \"div\",\n                                                    {\n                                                      staticClass: \"goods-name\",\n                                                      class: [\n                                                        item.style.content\n                                                          .goodsNameRows ==\n                                                        \"two\"\n                                                          ? \"twoline-hide\"\n                                                          : \"oneline-hide\",\n                                                        `row-${item.style.goodsNameRows}`,\n                                                      ],\n                                                    },\n                                                    [\n                                                      _vm._v(\n                                                        _vm._s(\n                                                          dataItm.goods_name\n                                                        )\n                                                      ),\n                                                    ]\n                                                  )\n                                                : _vm._e(),\n                                              _c(\n                                                \"div\",\n                                                { staticClass: \"footer\" },\n                                                [\n                                                  _vm.inArray(\n                                                    \"seckillPrice\",\n                                                    item.params.show\n                                                  )\n                                                    ? _c(\n                                                        \"div\",\n                                                        {\n                                                          staticClass:\n                                                            \"goods-price oneline-hide\",\n                                                          style: {\n                                                            color:\n                                                              item.style.content\n                                                                .priceColor,\n                                                          },\n                                                        },\n                                                        [\n                                                          _c(\n                                                            \"span\",\n                                                            {\n                                                              staticClass:\n                                                                \"unit\",\n                                                            },\n                                                            [_vm._v(\"￥\")]\n                                                          ),\n                                                          _c(\n                                                            \"span\",\n                                                            {\n                                                              staticClass:\n                                                                \"value\",\n                                                            },\n                                                            [\n                                                              _vm._v(\n                                                                _vm._s(\n                                                                  dataItm.seckill_price_min\n                                                                )\n                                                              ),\n                                                            ]\n                                                          ),\n                                                          _vm.inArray(\n                                                            \"originalPrice\",\n                                                            item.params.show\n                                                          )\n                                                            ? _c(\n                                                                \"span\",\n                                                                {\n                                                                  staticClass:\n                                                                    \"line-price\",\n                                                                },\n                                                                [\n                                                                  _c(\n                                                                    \"span\",\n                                                                    {\n                                                                      staticClass:\n                                                                        \"unit\",\n                                                                    },\n                                                                    [\n                                                                      _vm._v(\n                                                                        \"￥\"\n                                                                      ),\n                                                                    ]\n                                                                  ),\n                                                                  _c(\n                                                                    \"span\",\n                                                                    {\n                                                                      staticClass:\n                                                                        \"value\",\n                                                                    },\n                                                                    [\n                                                                      _vm._v(\n                                                                        _vm._s(\n                                                                          dataItm.original_price\n                                                                        )\n                                                                      ),\n                                                                    ]\n                                                                  ),\n                                                                ]\n                                                              )\n                                                            : _vm._e(),\n                                                        ]\n                                                      )\n                                                    : _vm._e(),\n                                                ]\n                                              ),\n                                            ]\n                                          ),\n                                        ],\n                                  ],\n                                  2\n                                )\n                              }),\n                              0\n                            ),\n                          ]\n                        ),\n                      ]\n                    )\n                  : item.type == \"coupon\"\n                  ? _c(\n                      \"div\",\n                      {\n                        staticClass: \"diy-coupon\",\n                        style: {\n                          padding: `${item.style.paddingTop}px 0`,\n                          background: item.style.background,\n                        },\n                      },\n                      [\n                        _c(\n                          \"div\",\n                          { staticClass: \"coupon-wrapper\" },\n                          _vm._l(\n                            item.params.source == \"choice\" && item.data.length\n                              ? item.data\n                              : item.defaultData,\n                            function (coupon, idx) {\n                              return _c(\n                                \"div\",\n                                {\n                                  key: idx,\n                                  staticClass: \"coupon-item\",\n                                  style: {\n                                    marginRight: `${item.style.marginRight}px`,\n                                  },\n                                },\n                                [\n                                  _c(\"i\", {\n                                    staticClass: \"before\",\n                                    style: {\n                                      background: item.style.background,\n                                    },\n                                  }),\n                                  _c(\n                                    \"div\",\n                                    {\n                                      staticClass: \"left-content\",\n                                      style: {\n                                        background: item.style.couponBgColor,\n                                        color: item.style.couponTextColor,\n                                      },\n                                    },\n                                    [\n                                      _c(\n                                        \"div\",\n                                        { staticClass: \"content-top\" },\n                                        [\n                                          coupon.coupon_type == 10\n                                            ? [\n                                                _c(\n                                                  \"span\",\n                                                  { staticClass: \"unit\" },\n                                                  [_vm._v(\"￥\")]\n                                                ),\n                                                _c(\n                                                  \"span\",\n                                                  { staticClass: \"price\" },\n                                                  [\n                                                    _vm._v(\n                                                      _vm._s(\n                                                        coupon.reduce_price\n                                                      )\n                                                    ),\n                                                  ]\n                                                ),\n                                              ]\n                                            : _vm._e(),\n                                          coupon.coupon_type == 20\n                                            ? [\n                                                _c(\n                                                  \"span\",\n                                                  { staticClass: \"price\" },\n                                                  [\n                                                    _vm._v(\n                                                      _vm._s(coupon.discount) +\n                                                        \"折\"\n                                                    ),\n                                                  ]\n                                                ),\n                                              ]\n                                            : _vm._e(),\n                                        ],\n                                        2\n                                      ),\n                                      _c(\n                                        \"div\",\n                                        { staticClass: \"content-bottom\" },\n                                        [\n                                          _c(\"span\", [\n                                            _vm._v(\n                                              \"满\" +\n                                                _vm._s(coupon.min_price) +\n                                                \"元可用\"\n                                            ),\n                                          ]),\n                                        ]\n                                      ),\n                                    ]\n                                  ),\n                                  _c(\n                                    \"div\",\n                                    {\n                                      staticClass: \"right-receive\",\n                                      style: {\n                                        background: item.style.receiveBgColor,\n                                        color: item.style.receiveTextColor,\n                                      },\n                                    },\n                                    [\n                                      _c(\"span\", [_vm._v(\"立即\")]),\n                                      _c(\"span\", [_vm._v(\"领取\")]),\n                                    ]\n                                  ),\n                                ]\n                              )\n                            }\n                          ),\n                          0\n                        ),\n                      ]\n                    )\n                  : item.type == \"special\"\n                  ? _c(\n                      \"div\",\n                      {\n                        staticClass: \"diy-special\",\n                        style: {\n                          padding: `${item.style.paddingTop}px 0`,\n                          background: item.style.background,\n                        },\n                      },\n                      [\n                        _c(\"div\", { staticClass: \"special-left\" }, [\n                          _c(\"img\", {\n                            staticClass: \"image\",\n                            attrs: { src: item.params.image, alt: \"\" },\n                          }),\n                        ]),\n                        _c(\n                          \"div\",\n                          {\n                            staticClass: \"special-content\",\n                            class: [`display_${item.params.display}`],\n                          },\n                          [\n                            _c(\n                              \"ul\",\n                              { staticClass: \"special-content-list\" },\n                              _vm._l(\n                                item.params.source == \"choice\" &&\n                                  item.data.length\n                                  ? item.data\n                                  : item.defaultData,\n                                function (dataItm, idx) {\n                                  return _c(\n                                    \"li\",\n                                    {\n                                      key: idx,\n                                      staticClass: \"content-item oneline-hide\",\n                                    },\n                                    [\n                                      _c(\n                                        \"span\",\n                                        {\n                                          style: {\n                                            color: item.style.textColor,\n                                          },\n                                        },\n                                        [_vm._v(_vm._s(dataItm.title))]\n                                      ),\n                                    ]\n                                  )\n                                }\n                              ),\n                              0\n                            ),\n                          ]\n                        ),\n                        _c(\n                          \"div\",\n                          { staticClass: \"special-more\" },\n                          [\n                            _c(\"a-icon\", {\n                              attrs: { component: _vm.Icon.arrowRight },\n                            }),\n                          ],\n                          1\n                        ),\n                      ]\n                    )\n                  : item.type == \"ICPLicense\"\n                  ? _c(\n                      \"div\",\n                      {\n                        staticClass: \"diy-ICPLicense\",\n                        style: {\n                          padding: `${item.style.paddingTop}px ${item.style.paddingLeft}px`,\n                          background: item.style.background,\n                        },\n                      },\n                      [\n                        _c(\n                          \"p\",\n                          {\n                            staticClass: \"line\",\n                            style: { textAlign: item.style.textAlign },\n                          },\n                          [\n                            _c(\n                              \"a\",\n                              {\n                                style: {\n                                  fontSize: item.style.fontSize + \"px\",\n                                  color: item.style.textColor,\n                                },\n                                attrs: {\n                                  href: item.params.link,\n                                  target: \"_blank\",\n                                },\n                              },\n                              [_vm._v(_vm._s(item.params.text))]\n                            ),\n                          ]\n                        ),\n                      ]\n                    )\n                  : item.type == \"goodsGroup\"\n                  ? _c(\n                      \"div\",\n                      {\n                        staticClass: \"diy-goodsGroup\",\n                        style: {\n                          background: item.style.background,\n                          padding: `${item.style.paddingY}px ${item.style.paddingX}px`,\n                        },\n                      },\n                      [\n                        _c(\n                          \"div\",\n                          {\n                            staticClass: \"tabs\",\n                            style: {\n                              \"--tab-active-font-color\":\n                                item.style.tabActiveFontColor,\n                              \"--tab-active-bg-color\":\n                                item.style.tabActiveBgColor,\n                              background: item.style.background,\n                            },\n                          },\n                          _vm._l(item.params.tabs, function (tabItem, dataIdx) {\n                            return _c(\n                              \"div\",\n                              {\n                                key: `${index}_${dataIdx}`,\n                                staticClass: \"tab-item\",\n                                class: { active: dataIdx == 0 },\n                              },\n                              [\n                                _c(\n                                  \"div\",\n                                  {\n                                    staticClass: \"tab-name\",\n                                    style: { color: item.style.tabTextColor },\n                                  },\n                                  [_vm._v(_vm._s(tabItem.name))]\n                                ),\n                                _c(\"div\", { staticClass: \"sub-name\" }, [\n                                  _vm._v(_vm._s(tabItem.subName)),\n                                ]),\n                              ]\n                            )\n                          }),\n                          0\n                        ),\n                        _c(\n                          \"div\",\n                          {\n                            staticClass: \"goods-list\",\n                            style: {\n                              marginBottom: `-${item.style.itemMargin}px`,\n                            },\n                          },\n                          _vm._l(item.defaultData, function (dataItm, dataIdx) {\n                            return _c(\n                              \"div\",\n                              {\n                                key: `${index}_${dataIdx}`,\n                                staticClass: \"goods-item\",\n                                class: [`display-${item.style.cardType}`],\n                                style: {\n                                  marginBottom: `${item.style.itemMargin}px`,\n                                  borderRadius: `${item.style.borderRadius}px`,\n                                },\n                              },\n                              [\n                                _c(\"div\", { staticClass: \"goods-image\" }, [\n                                  _c(\"img\", {\n                                    staticClass: \"image\",\n                                    attrs: {\n                                      src: dataItm.goods_image,\n                                      alt: \"\",\n                                    },\n                                  }),\n                                ]),\n                                _c(\"div\", { staticClass: \"goods-info\" }, [\n                                  _vm.inArray(\"goodsName\", item.style.show)\n                                    ? _c(\n                                        \"div\",\n                                        {\n                                          staticClass: \"goods-name\",\n                                          class: [\n                                            item.style.goodsNameRows == \"two\"\n                                              ? \"twoline-hide\"\n                                              : \"oneline-hide\",\n                                            `row-${item.style.goodsNameRows}`,\n                                          ],\n                                        },\n                                        [_vm._v(_vm._s(dataItm.goods_name))]\n                                      )\n                                    : _vm._e(),\n                                  _vm.inArray(\"sellingPoint\", item.style.show)\n                                    ? _c(\n                                        \"div\",\n                                        { staticClass: \"goods-selling\" },\n                                        [\n                                          _c(\n                                            \"span\",\n                                            {\n                                              staticClass:\n                                                \"selling oneline-hide\",\n                                              style: {\n                                                color: item.style.sellingColor,\n                                              },\n                                            },\n                                            [\n                                              _vm._v(\n                                                _vm._s(dataItm.selling_point)\n                                              ),\n                                            ]\n                                          ),\n                                        ]\n                                      )\n                                    : _vm._e(),\n                                  _vm.inArray(\"goodsSales\", item.style.show)\n                                    ? _c(\n                                        \"div\",\n                                        {\n                                          staticClass:\n                                            \"goods-sales oneline-hide\",\n                                        },\n                                        [\n                                          _c(\"span\", { staticClass: \"sales\" }, [\n                                            _vm._v(\n                                              \"已售\" +\n                                                _vm._s(dataItm.goods_sales)\n                                            ),\n                                          ]),\n                                        ]\n                                      )\n                                    : _vm._e(),\n                                  _c(\"div\", { staticClass: \"footer\" }, [\n                                    _c(\n                                      \"div\",\n                                      {\n                                        staticClass: \"goods-price oneline-hide\",\n                                        style: { color: item.style.priceColor },\n                                      },\n                                      [\n                                        _vm.inArray(\n                                          \"goodsPrice\",\n                                          item.style.show\n                                        )\n                                          ? [\n                                              _c(\n                                                \"span\",\n                                                { staticClass: \"unit\" },\n                                                [_vm._v(\"￥\")]\n                                              ),\n                                              _c(\n                                                \"span\",\n                                                { staticClass: \"value\" },\n                                                [\n                                                  _vm._v(\n                                                    _vm._s(\n                                                      dataItm.goods_price_min\n                                                    )\n                                                  ),\n                                                ]\n                                              ),\n                                            ]\n                                          : _vm._e(),\n                                        _vm.inArray(\n                                          \"linePrice\",\n                                          item.style.show\n                                        )\n                                          ? _c(\n                                              \"span\",\n                                              { staticClass: \"line-price\" },\n                                              [\n                                                _c(\n                                                  \"span\",\n                                                  { staticClass: \"unit\" },\n                                                  [_vm._v(\"￥\")]\n                                                ),\n                                                _c(\n                                                  \"span\",\n                                                  { staticClass: \"value\" },\n                                                  [\n                                                    _vm._v(\n                                                      _vm._s(\n                                                        dataItm.line_price_min\n                                                      )\n                                                    ),\n                                                  ]\n                                                ),\n                                              ]\n                                            )\n                                          : _vm._e(),\n                                      ],\n                                      2\n                                    ),\n                                    _c(\n                                      \"div\",\n                                      {\n                                        directives: [\n                                          {\n                                            name: \"show\",\n                                            rawName: \"v-show\",\n                                            value: _vm.inArray(\n                                              \"cartBtn\",\n                                              item.style.show\n                                            ),\n                                            expression:\n                                              \"inArray('cartBtn', item.style.show)\",\n                                          },\n                                        ],\n                                        staticClass: \"action\",\n                                      },\n                                      [\n                                        _c(\n                                          \"div\",\n                                          {\n                                            staticClass: \"btn-cart\",\n                                            style: {\n                                              color: item.style.btnCartColor,\n                                            },\n                                          },\n                                          [\n                                            _c(\"a-icon\", {\n                                              staticClass: \"cart-icon\",\n                                              attrs: {\n                                                component:\n                                                  _vm.PageIcon[\n                                                    `jiagou${item.style.btnCartStyle}`\n                                                  ],\n                                              },\n                                            }),\n                                          ],\n                                          1\n                                        ),\n                                      ]\n                                    ),\n                                  ]),\n                                ]),\n                              ]\n                            )\n                          }),\n                          0\n                        ),\n                      ]\n                    )\n                  : item.type == \"title\"\n                  ? _c(\n                      \"div\",\n                      {\n                        staticClass: \"diy-title\",\n                        style: {\n                          padding: `${item.style.paddingY}px 15px`,\n                          background: item.style.background,\n                        },\n                      },\n                      [\n                        _c(\"div\", { staticClass: \"title-content\" }, [\n                          _c(\"div\", { staticClass: \"title\" }, [\n                            _c(\n                              \"span\",\n                              {\n                                style: {\n                                  color: item.style.titleTextColor,\n                                  fontSize: `${item.params.titleFontSize}px`,\n                                  fontWeight: item.params.titleFontWeight,\n                                },\n                              },\n                              [_vm._v(_vm._s(item.params.title))]\n                            ),\n                          ]),\n                          item.params.more.enable\n                            ? _c(\n                                \"div\",\n                                {\n                                  staticClass: \"more-content\",\n                                  style: { color: item.style.moreTextColor },\n                                },\n                                [\n                                  _c(\"span\", { staticClass: \"more-text\" }, [\n                                    _vm._v(_vm._s(item.params.more.text)),\n                                  ]),\n                                  item.params.more.enableIcon\n                                    ? _c(\n                                        \"span\",\n                                        { staticClass: \"more-icon\" },\n                                        [\n                                          _c(\"a-icon\", {\n                                            attrs: {\n                                              component: _vm.Icon.arrowRight,\n                                            },\n                                          }),\n                                        ],\n                                        1\n                                      )\n                                    : _vm._e(),\n                                ]\n                              )\n                            : _vm._e(),\n                        ]),\n                        _c(\"div\", { staticClass: \"desc-content\" }, [\n                          _c(\n                            \"span\",\n                            {\n                              style: {\n                                color: item.style.descTextColor,\n                                fontSize: `${item.params.descFontSize}px`,\n                                fontWeight: item.params.descFontWeight,\n                              },\n                            },\n                            [_vm._v(_vm._s(item.params.desc))]\n                          ),\n                        ]),\n                      ]\n                    )\n                  : item.type == \"pointsMall\"\n                  ? _c(\n                      \"div\",\n                      {\n                        staticClass: \"diy-pointsMall\",\n                        style: {\n                          background: item.style.background,\n                          padding: `${item.style.paddingY}px ${item.style.paddingX}px`,\n                        },\n                      },\n                      [\n                        _c(\n                          \"div\",\n                          {\n                            staticClass: \"goods-list\",\n                            class: [\n                              `display-${item.style.display}`,\n                              `column-${item.style.column}`,\n                            ],\n                            style: {\n                              marginBottom: `-${item.style.itemMargin}px`,\n                            },\n                          },\n                          _vm._l(\n                            item.params.source == \"choice\" && item.data.length\n                              ? item.data\n                              : item.defaultData,\n                            function (dataItm, dataIdx) {\n                              return _c(\n                                \"div\",\n                                {\n                                  key: `${index}_${dataIdx}`,\n                                  staticClass: \"goods-item\",\n                                  class: [`display-${item.style.cardType}`],\n                                  style: {\n                                    marginBottom: `${item.style.itemMargin}px`,\n                                    borderRadius: `${item.style.borderRadius}px`,\n                                  },\n                                },\n                                [\n                                  _c(\"div\", { staticClass: \"goods-image\" }, [\n                                    _c(\"img\", {\n                                      staticClass: \"image\",\n                                      attrs: { src: dataItm.goods_image },\n                                    }),\n                                  ]),\n                                  _c(\"div\", { staticClass: \"goods-info\" }, [\n                                    _vm.inArray(\"goodsName\", item.style.show)\n                                      ? _c(\n                                          \"div\",\n                                          {\n                                            staticClass: \"goods-name\",\n                                            class: [\n                                              item.style.goodsNameRows == \"two\"\n                                                ? \"twoline-hide\"\n                                                : \"oneline-hide\",\n                                              `row-${item.style.goodsNameRows}`,\n                                            ],\n                                          },\n                                          [_vm._v(_vm._s(dataItm.goods_name))]\n                                        )\n                                      : _vm._e(),\n                                    _vm.inArray(\"sellingPoint\", item.style.show)\n                                      ? _c(\n                                          \"div\",\n                                          { staticClass: \"goods-selling\" },\n                                          [\n                                            _c(\n                                              \"span\",\n                                              {\n                                                staticClass:\n                                                  \"selling oneline-hide\",\n                                                style: {\n                                                  color:\n                                                    item.style.sellingColor,\n                                                },\n                                              },\n                                              [\n                                                _vm._v(\n                                                  _vm._s(dataItm.selling_point)\n                                                ),\n                                              ]\n                                            ),\n                                          ]\n                                        )\n                                      : _vm._e(),\n                                    _vm.inArray(\"goodsSales\", item.style.show)\n                                      ? _c(\n                                          \"div\",\n                                          {\n                                            staticClass:\n                                              \"goods-sales oneline-hide\",\n                                          },\n                                          [\n                                            _c(\n                                              \"span\",\n                                              { staticClass: \"sales\" },\n                                              [\n                                                _vm._v(\n                                                  \"已兑换\" +\n                                                    _vm._s(dataItm.goods_sales)\n                                                ),\n                                              ]\n                                            ),\n                                          ]\n                                        )\n                                      : _vm._e(),\n                                    _c(\"div\", { staticClass: \"footer\" }, [\n                                      _vm.inArray(\"goodsPrice\", item.style.show)\n                                        ? _c(\n                                            \"div\",\n                                            {\n                                              staticClass:\n                                                \"goods-price oneline-hide\",\n                                            },\n                                            [\n                                              _c(\n                                                \"div\",\n                                                { staticClass: \"points-icon\" },\n                                                [\n                                                  _c(\"img\", {\n                                                    staticClass: \"image\",\n                                                    attrs: {\n                                                      src: require(\"@/assets/img/points_01.png\"),\n                                                      alt: \"\",\n                                                    },\n                                                  }),\n                                                ]\n                                              ),\n                                              _c(\n                                                \"span\",\n                                                { staticClass: \"value\" },\n                                                [\n                                                  _vm._v(\n                                                    _vm._s(\n                                                      dataItm.points_price_min\n                                                    )\n                                                  ),\n                                                ]\n                                              ),\n                                              dataItm.buy_type == 20\n                                                ? [\n                                                    _c(\n                                                      \"span\",\n                                                      { staticClass: \"plus\" },\n                                                      [_vm._v(\"+\")]\n                                                    ),\n                                                    _c(\n                                                      \"span\",\n                                                      { staticClass: \"value\" },\n                                                      [\n                                                        _vm._v(\n                                                          _vm._s(\n                                                            dataItm.paid_price_min\n                                                          ) + \"元\"\n                                                        ),\n                                                      ]\n                                                    ),\n                                                  ]\n                                                : _vm._e(),\n                                            ],\n                                            2\n                                          )\n                                        : _vm._e(),\n                                    ]),\n                                  ]),\n                                ]\n                              )\n                            }\n                          ),\n                          0\n                        ),\n                      ]\n                    )\n                  : _vm._e(),\n                _c(\"div\", { staticClass: \"action-tools\" }, [\n                  _c(\n                    \"div\",\n                    { staticClass: \"tools-content\" },\n                    _vm._l(_vm.actionTools(item, index), function (itm, idx) {\n                      return _c(\n                        \"div\",\n                        { key: idx, staticClass: \"tools-item\" },\n                        [\n                          _c(\n                            \"a-tooltip\",\n                            {\n                              attrs: {\n                                placement: \"right\",\n                                getPopupContainer: () =>\n                                  _vm.$refs[\"phone-content\"],\n                              },\n                            },\n                            [\n                              _c(\n                                \"span\",\n                                {\n                                  staticClass: \"tooltip-text\",\n                                  attrs: { slot: \"title\" },\n                                  slot: \"title\",\n                                },\n                                [_vm._v(_vm._s(itm.title))]\n                              ),\n                              _c(\n                                \"div\",\n                                {\n                                  staticClass: \"item-btn\",\n                                  class: { disabled: itm.disabled },\n                                  on: {\n                                    click: function ($event) {\n                                      $event.stopPropagation()\n                                      return _vm.handleActionToolItem(\n                                        itm,\n                                        index\n                                      )\n                                    },\n                                  },\n                                },\n                                [\n                                  _c(\"a-icon\", {\n                                    staticClass: \"tools-icon\",\n                                    attrs: { type: itm.type },\n                                  }),\n                                ],\n                                1\n                              ),\n                            ]\n                          ),\n                        ],\n                        1\n                      )\n                    }),\n                    0\n                  ),\n                ]),\n              ]\n            )\n          }),\n          0\n        ),\n      ],\n      1\n    ),\n  ])\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": ";;;;;;AAAA,IAAIA,MAAM,GAAG,SAASA,MAAM,GAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,KAAK,EAAE;IAAEE,GAAG,EAAE,eAAe;IAAEC,WAAW,EAAE;EAAgB,CAAC,EAAE,CACvEH,EAAE,CACA,KAAK,EACL;IACEG,WAAW,EAAE,oBAAoB;IACjCC,KAAK;MACHC,QAAQ,EAAE,MAAM,KAAKN,GAAG,CAACO;IAAa,GACrCP,GAAG,CAACQ,IAAI,CAACC,IAAI,CAACC,KAAK,CAACC,cAAc,EAAG,IAAI,CAC3C;IACDD,KAAK,EAAE;MAAEE,eAAe,EAAEZ,GAAG,CAACQ,IAAI,CAACC,IAAI,CAACC,KAAK,CAACG;IAAqB,CAAC;IACpEC,EAAE,EAAE;MACFC,KAAK,EAAE,eAAUC,MAAM,EAAE;QACvB,OAAOhB,GAAG,CAACiB,eAAe,CAAC,MAAM,CAAC;MACpC;IACF;EACF,CAAC,EACD,CACEhB,EAAE,CACA,GAAG,EACH;IACEG,WAAW,EAAE,OAAO;IACpBM,KAAK,EAAE;MAAEQ,KAAK,EAAElB,GAAG,CAACQ,IAAI,CAACC,IAAI,CAACC,KAAK,CAACC;IAAe;EACrD,CAAC,EACD,CAACX,GAAG,CAACmB,EAAE,CAACnB,GAAG,CAACoB,EAAE,CAACpB,GAAG,CAACQ,IAAI,CAACC,IAAI,CAACY,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAC7C,CACF,CACF,EACDrB,EAAE,CACA,KAAK,EACL;IAAEG,WAAW,EAAE;EAAa,CAAC,EAC7B,CACEH,EAAE,CACA,WAAW,EACXD,GAAG,CAACuB,EAAE,CACJ;IACEnB,WAAW,EAAE,SAAS;IACtBoB,KAAK,EAAE;MAAEC,IAAI,EAAEzB,GAAG,CAACQ,IAAI,CAACkB;IAAM,CAAC;IAC/BZ,EAAE,EAAE;MAAEa,MAAM,EAAE3B,GAAG,CAAC4B;IAAe;EACnC,CAAC,EACD,WAAW,EACX;IAAEC,SAAS,EAAE,GAAG;IAAEC,MAAM,EAAE;EAAU,CAAC,EACrC,KAAK,CACN,EACD9B,GAAG,CAAC+B,EAAE,CAAC/B,GAAG,CAACQ,IAAI,CAACkB,KAAK,EAAE,UAAUM,IAAI,EAAEC,KAAK,EAAE;IAC5C,OAAOhC,EAAE,CACP,KAAK,EACL;MACEiC,GAAG,EAAED,KAAK;MACV7B,WAAW,EAAE,sBAAsB;MACnCC,KAAK,EAAE;QACLC,QAAQ,EAAE2B,KAAK,KAAKjC,GAAG,CAACO,aAAa;QACrC4B,MAAM,EAAEnC,GAAG,CAACoC,OAAO,CAACJ,IAAI,CAACK,IAAI,EAAErC,GAAG,CAACsC,UAAU;MAC/C,CAAC;MACD5B,KAAK,EAAEV,GAAG,CAACuC,eAAe,CAACP,IAAI,CAAC;MAChClB,EAAE,EAAE;QACFC,KAAK,EAAE,eAAUC,MAAM,EAAE;UACvB,OAAOhB,GAAG,CAACiB,eAAe,CAACgB,KAAK,CAAC;QACnC;MACF;IACF,CAAC,EACD,CACED,IAAI,CAACK,IAAI,IAAI,QAAQ,GACjBpC,EAAE,CACA,KAAK,EACL;MACEG,WAAW,EAAE,YAAY;MACzBM,KAAK,EAAE;QACL8B,OAAO,YAAKR,IAAI,CAACtB,KAAK,CAAC+B,UAAU,gBAAMT,IAAI,CAACtB,KAAK,CAACgC,WAAW,OAAI;QACjEC,UAAU,EAAEX,IAAI,CAACtB,KAAK,CAACiC;MACzB;IACF,CAAC,EACD,CACE3C,GAAG,CAAC+B,EAAE,CAACC,IAAI,CAACxB,IAAI,EAAE,UAAUoC,QAAQ,EAAEC,OAAO,EAAE;MAC7C,OAAO5C,EAAE,CACP,KAAK,EACL;QACE6C,UAAU,EAAE,CACV;UACEC,IAAI,EAAE,MAAM;UACZC,OAAO,EAAE,QAAQ;UACjBC,KAAK,EAAEJ,OAAO,IAAI,CAAC;UACnBK,UAAU,EAAE;QACd,CAAC,CACF;QACDhB,GAAG,YAAKD,KAAK,cAAIY,OAAO,SAAM;QAC9BzC,WAAW,EAAE,aAAa;QAC1BM,KAAK,EAAE;UACLyC,YAAY,YAAKnB,IAAI,CAACtB,KAAK,CAACyC,YAAY;QAC1C;MACF,CAAC,EACD,CACElD,EAAE,CAAC,KAAK,EAAE;QACRG,WAAW,EAAE,OAAO;QACpBoB,KAAK,EAAE;UAAE4B,GAAG,EAAER,QAAQ,CAACS;QAAO;MAChC,CAAC,CAAC,CACH,CACF;IACH,CAAC,CAAC,EACFpD,EAAE,CACA,KAAK,EACL;MACEG,WAAW,EAAE,gBAAgB;MAC7BC,KAAK,EAAE2B,IAAI,CAACtB,KAAK,CAAC4C,QAAQ;MAC1B5C,KAAK,EAAE;QACL,eAAe,YAAKsB,IAAI,CAACtB,KAAK,CAAC+B,UAAU;MAC3C;IACF,CAAC,EACDzC,GAAG,CAAC+B,EAAE,CAACC,IAAI,CAACxB,IAAI,EAAE,UAAUoC,QAAQ,EAAEC,OAAO,EAAE;MAC7C,OAAO5C,EAAE,CAAC,KAAK,EAAE;QACfiC,GAAG,YAAKD,KAAK,cAAIY,OAAO,UAAO;QAC/BzC,WAAW,EAAE,WAAW;QACxBM,KAAK,EAAE;UAAEiC,UAAU,EAAEX,IAAI,CAACtB,KAAK,CAAC6C;QAAS;MAC3C,CAAC,CAAC;IACJ,CAAC,CAAC,EACF,CAAC,CACF,CACF,EACD,CAAC,CACF,GACDvB,IAAI,CAACK,IAAI,IAAI,OAAO,GACpBpC,EAAE,CACA,KAAK,EACL;MACEG,WAAW,EAAE,WAAW;MACxBM,KAAK,EAAE;QACL8B,OAAO,YAAKR,IAAI,CAACtB,KAAK,CAAC+B,UAAU,gBAAMT,IAAI,CAACtB,KAAK,CAACgC,WAAW,OAAI;QACjEC,UAAU,EAAEX,IAAI,CAACtB,KAAK,CAACiC;MACzB;IACF,CAAC,EACD3C,GAAG,CAAC+B,EAAE,CAACC,IAAI,CAACxB,IAAI,EAAE,UAAUgD,OAAO,EAAEX,OAAO,EAAE;MAC5C,OAAO5C,EAAE,CACP,KAAK,EACL;QACEiC,GAAG,YAAKD,KAAK,cAAIY,OAAO,CAAE;QAC1BzC,WAAW,EAAE,YAAY;QACzBM,KAAK,EAAE;UACL+C,YAAY,YAAKzB,IAAI,CAACtB,KAAK,CAACgD,UAAU,OAAI;UAC1CP,YAAY,YAAKnB,IAAI,CAACtB,KAAK,CAACyC,YAAY;QAC1C;MACF,CAAC,EACD,CACElD,EAAE,CAAC,KAAK,EAAE;QACRG,WAAW,EAAE,OAAO;QACpBoB,KAAK,EAAE;UAAE4B,GAAG,EAAEI,OAAO,CAACH;QAAO;MAC/B,CAAC,CAAC,CACH,CACF;IACH,CAAC,CAAC,EACF,CAAC,CACF,GACDrB,IAAI,CAACK,IAAI,IAAI,QAAQ,GACrBpC,EAAE,CACA,KAAK,EACL;MACEG,WAAW,EAAE,YAAY;MACzBM,KAAK,EAAE;QACLiC,UAAU,EAAEX,IAAI,CAACtB,KAAK,CAACiC,UAAU;QACjCH,OAAO,YAAKR,IAAI,CAACtB,KAAK,CAAC+B,UAAU,gBAAMT,IAAI,CAACtB,KAAK,CAACgC,WAAW;MAC/D;IACF,CAAC,EACD,CACEV,IAAI,CAACtB,KAAK,CAACiD,MAAM,GAAG,CAAC,CAAC,GAClB1D,EAAE,CACA,IAAI,EACJ;MACEG,WAAW,EAAE,oBAAoB;MACjCC,KAAK,mBAAY2B,IAAI,CAACtB,KAAK,CAACiD,MAAM;IACpC,CAAC,EACD3D,GAAG,CAAC+B,EAAE,CAACC,IAAI,CAACxB,IAAI,EAAE,UAAUoD,MAAM,EAAEf,OAAO,EAAE;MAC3C,OAAO5C,EAAE,CACP,IAAI,EACJ;QACEiC,GAAG,YAAKD,KAAK,cAAIY,OAAO,CAAE;QAC1BzC,WAAW,EAAE,WAAW;QACxBM,KAAK,EAAE;UACL8B,OAAO,YAAKR,IAAI,CAACtB,KAAK,CAAC+B,UAAU,gBAAMT,IAAI,CAACtB,KAAK,CAACgC,WAAW;QAC/D;MACF,CAAC,EACD,CACEzC,EAAE,CAAC,KAAK,EAAE;QAAEG,WAAW,EAAE;MAAa,CAAC,EAAE,CACvCH,EAAE,CAAC,KAAK,EAAE;QACRG,WAAW,EAAE,OAAO;QACpBoB,KAAK,EAAE;UAAE4B,GAAG,EAAEQ,MAAM,CAACP;QAAO;MAC9B,CAAC,CAAC,CACH,CAAC,CACH,CACF;IACH,CAAC,CAAC,EACF,CAAC,CACF,GACDpD,EAAE,CAAC,KAAK,EAAE;MAAEG,WAAW,EAAE;IAAU,CAAC,EAAE,CACpCH,EAAE,CACA,KAAK,EACL;MACEG,WAAW,EAAE,cAAc;MAC3BM,KAAK,EAAE;QACL8B,OAAO,YAAKR,IAAI,CAACtB,KAAK,CAAC+B,UAAU,gBAAMT,IAAI,CAACtB,KAAK,CAACgC,WAAW;MAC/D;IACF,CAAC,EACD,CACEzC,EAAE,CAAC,KAAK,EAAE;MACRG,WAAW,EAAE,OAAO;MACpBoB,KAAK,EAAE;QAAE4B,GAAG,EAAEpB,IAAI,CAACxB,IAAI,CAAC,CAAC,CAAC,CAAC6C;MAAO;IACpC,CAAC,CAAC,CACH,CACF,EACDpD,EAAE,CAAC,KAAK,EAAE;MAAEG,WAAW,EAAE;IAAgB,CAAC,EAAE,CAC1C4B,IAAI,CAACxB,IAAI,CAACqD,MAAM,IAAI,CAAC,GACjB5D,EAAE,CACA,KAAK,EACL;MACEG,WAAW,EAAE,gBAAgB;MAC7BM,KAAK,EAAE;QACL8B,OAAO,YAAKR,IAAI,CAACtB,KAAK,CAAC+B,UAAU,gBAAMT,IAAI,CAACtB,KAAK,CAACgC,WAAW;MAC/D;IACF,CAAC,EACD,CACEzC,EAAE,CAAC,KAAK,EAAE;MACRG,WAAW,EAAE,OAAO;MACpBoB,KAAK,EAAE;QAAE4B,GAAG,EAAEpB,IAAI,CAACxB,IAAI,CAAC,CAAC,CAAC,CAAC6C;MAAO;IACpC,CAAC,CAAC,CACH,CACF,GACDrD,GAAG,CAAC8D,EAAE,EAAE,EACZ7D,EAAE,CAAC,KAAK,EAAE;MAAEG,WAAW,EAAE;IAAiB,CAAC,EAAE,CAC3C4B,IAAI,CAACxB,IAAI,CAACqD,MAAM,IAAI,CAAC,GACjB5D,EAAE,CACA,KAAK,EACL;MACEG,WAAW,EAAE,MAAM;MACnBM,KAAK,EAAE;QACL8B,OAAO,YAAKR,IAAI,CAACtB,KAAK,CAAC+B,UAAU,gBAAMT,IAAI,CAACtB,KAAK,CAACgC,WAAW;MAC/D;IACF,CAAC,EACD,CACEzC,EAAE,CAAC,KAAK,EAAE;MACRG,WAAW,EAAE,OAAO;MACpBoB,KAAK,EAAE;QAAE4B,GAAG,EAAEpB,IAAI,CAACxB,IAAI,CAAC,CAAC,CAAC,CAAC6C;MAAO;IACpC,CAAC,CAAC,CACH,CACF,GACDrD,GAAG,CAAC8D,EAAE,EAAE,EACZ9B,IAAI,CAACxB,IAAI,CAACqD,MAAM,IAAI,CAAC,GACjB5D,EAAE,CACA,KAAK,EACL;MACEG,WAAW,EAAE,OAAO;MACpBM,KAAK,EAAE;QACL8B,OAAO,YAAKR,IAAI,CAACtB,KAAK,CAAC+B,UAAU,gBAAMT,IAAI,CAACtB,KAAK,CAACgC,WAAW;MAC/D;IACF,CAAC,EACD,CACEzC,EAAE,CAAC,KAAK,EAAE;MACRG,WAAW,EAAE,OAAO;MACpBoB,KAAK,EAAE;QAAE4B,GAAG,EAAEpB,IAAI,CAACxB,IAAI,CAAC,CAAC,CAAC,CAAC6C;MAAO;IACpC,CAAC,CAAC,CACH,CACF,GACDrD,GAAG,CAAC8D,EAAE,EAAE,CACb,CAAC,CACH,CAAC,CACH,CAAC,CACP,CACF,GACD9B,IAAI,CAACK,IAAI,IAAI,SAAS,GACtBpC,EAAE,CACA,KAAK,EACL;MACEG,WAAW,EAAE,aAAa;MAC1BM,KAAK,EAAE;QACL8B,OAAO,YAAKR,IAAI,CAACtB,KAAK,CAAC+B,UAAU,gBAAMT,IAAI,CAACtB,KAAK,CAACgC,WAAW,OAAI;QACjEC,UAAU,EAAEX,IAAI,CAACtB,KAAK,CAACiC;MACzB;IACF,CAAC,EACD,CACE1C,EAAE,CACA,KAAK,EACL;MACEG,WAAW,EAAE,UAAU;MACvBM,KAAK,EAAE;QACLyC,YAAY,YAAKnB,IAAI,CAACtB,KAAK,CAACyC,YAAY;MAC1C;IACF,CAAC,EACD,CACElD,EAAE,CAAC,KAAK,EAAE;MACRG,WAAW,EAAE,OAAO;MACpBoB,KAAK,EAAE;QAAE4B,GAAG,EAAEpB,IAAI,CAACxB,IAAI,CAAC6C;MAAO;IACjC,CAAC,CAAC,CACH,CACF,CACF,CACF,GACDrB,IAAI,CAACK,IAAI,IAAI,OAAO,GACpBpC,EAAE,CACA,KAAK,EACL;MACEG,WAAW,EAAE,WAAW;MACxBM,KAAK,EAAE;QACL8B,OAAO,YAAKR,IAAI,CAACtB,KAAK,CAAC+B,UAAU,gBAAMT,IAAI,CAACtB,KAAK,CAACgC,WAAW,OAAI;QACjEC,UAAU,EAAEX,IAAI,CAACtB,KAAK,CAACiC;MACzB;IACF,CAAC,EACD,CACE1C,EAAE,CACA,OAAO,EACP;MACES,KAAK,EAAE;QAAEqD,MAAM,YAAK/B,IAAI,CAACtB,KAAK,CAACqD,MAAM;MAAK,CAAC;MAC3CvC,KAAK,EAAE;QACL4B,GAAG,EAAEpB,IAAI,CAACX,MAAM,CAAC2C,QAAQ;QACzBC,MAAM,EAAEjC,IAAI,CAACX,MAAM,CAAC4C,MAAM;QAC1BC,QAAQ,EAAE;MACZ;IACF,CAAC,EACD,CAAClE,GAAG,CAACmB,EAAE,CAAC,mBAAmB,CAAC,CAAC,CAC9B,CACF,CACF,GACDa,IAAI,CAACK,IAAI,IAAI,SAAS,GACtBpC,EAAE,CACA,KAAK,EACL;MAAEG,WAAW,EAAE;IAAc,CAAC,EAC9BJ,GAAG,CAAC+B,EAAE,CACJC,IAAI,CAACX,MAAM,CAAC8C,MAAM,IAAI,QAAQ,IAAInC,IAAI,CAACxB,IAAI,CAACqD,MAAM,GAC9C7B,IAAI,CAACxB,IAAI,GACTwB,IAAI,CAACoC,WAAW,EACpB,UAAUZ,OAAO,EAAEX,OAAO,EAAE;MAC1B,OAAO5C,EAAE,CACP,KAAK,EACL;QACEiC,GAAG,YAAKD,KAAK,cAAIY,OAAO,CAAE;QAC1BzC,WAAW,EAAE,cAAc;QAC3BC,KAAK,uBAAgBmD,OAAO,CAACa,SAAS;MACxC,CAAC,EACD,CACEb,OAAO,CAACa,SAAS,IAAI,EAAE,GACnB,CACEpE,EAAE,CACA,KAAK,EACL;QACEG,WAAW,EACT;MACJ,CAAC,EACD,CACEH,EAAE,CACA,KAAK,EACL;QACEG,WAAW,EACT;MACJ,CAAC,EACD,CACEH,EAAE,CACA,MAAM,EACN;QAAEG,WAAW,EAAE;MAAgB,CAAC,EAChC,CAACJ,GAAG,CAACmB,EAAE,CAACnB,GAAG,CAACoB,EAAE,CAACoC,OAAO,CAAClC,KAAK,CAAC,CAAC,CAAC,CAChC,CACF,CACF,EACDrB,EAAE,CACA,KAAK,EACL;QACEG,WAAW,EAAE;MACf,CAAC,EACD,CACEH,EAAE,CACA,MAAM,EACN;QAAEG,WAAW,EAAE;MAAgB,CAAC,EAChC,CACEJ,GAAG,CAACmB,EAAE,CACJnB,GAAG,CAACoB,EAAE,CAACoC,OAAO,CAACc,SAAS,CAAC,GACvB,KAAK,CACR,CACF,CACF,CACF,CACF,CACF,CACF,EACDrE,EAAE,CACA,KAAK,EACL;QAAEG,WAAW,EAAE;MAAsB,CAAC,EACtC,CACEH,EAAE,CAAC,KAAK,EAAE;QACRG,WAAW,EAAE,OAAO;QACpBoB,KAAK,EAAE;UACL4B,GAAG,EAAEI,OAAO,CAACe,KAAK;UAClBC,GAAG,EAAE;QACP;MACF,CAAC,CAAC,CACH,CACF,CACF,GACDxE,GAAG,CAAC8D,EAAE,EAAE,EACZN,OAAO,CAACa,SAAS,IAAI,EAAE,GACnB,CACEpE,EAAE,CACA,KAAK,EACL;QAAEG,WAAW,EAAE;MAAsB,CAAC,EACtC,CACEH,EAAE,CACA,MAAM,EACN;QAAEG,WAAW,EAAE;MAAgB,CAAC,EAChC,CAACJ,GAAG,CAACmB,EAAE,CAACnB,GAAG,CAACoB,EAAE,CAACoC,OAAO,CAAClC,KAAK,CAAC,CAAC,CAAC,CAChC,CACF,CACF,EACDrB,EAAE,CACA,KAAK,EACL;QAAEG,WAAW,EAAE;MAAsB,CAAC,EACtC,CACEH,EAAE,CAAC,KAAK,EAAE;QACRG,WAAW,EAAE,OAAO;QACpBoB,KAAK,EAAE;UAAE4B,GAAG,EAAEI,OAAO,CAACe;QAAM;MAC9B,CAAC,CAAC,CACH,CACF,EACDtE,EAAE,CACA,KAAK,EACL;QAAEG,WAAW,EAAE;MAAuB,CAAC,EACvC,CACEH,EAAE,CACA,MAAM,EACN;QAAEG,WAAW,EAAE;MAAgB,CAAC,EAChC,CACEJ,GAAG,CAACmB,EAAE,CACJnB,GAAG,CAACoB,EAAE,CAACoC,OAAO,CAACc,SAAS,CAAC,GACvB,KAAK,CACR,CACF,CACF,CACF,CACF,CACF,GACDtE,GAAG,CAAC8D,EAAE,EAAE,CACb,EACD,CAAC,CACF;IACH,CAAC,CACF,EACD,CAAC,CACF,GACD9B,IAAI,CAACK,IAAI,IAAI,QAAQ,GACrBpC,EAAE,CACA,KAAK,EACL;MACEG,WAAW,EAAE,YAAY;MACzBC,KAAK,EAAE;QAAEoE,MAAM,EAAEzC,IAAI,CAACX,MAAM,CAACoD;MAAO,CAAC;MACrC/D,KAAK,EAAE;QACLiC,UAAU,EAAEX,IAAI,CAACtB,KAAK,CAACiC,UAAU;QACjCH,OAAO,YAAKR,IAAI,CAACtB,KAAK,CAACgE,QAAQ,gBAAM1C,IAAI,CAACtB,KAAK,CAACiE,QAAQ;MAC1D;IACF,CAAC,EACD,CACE1E,EAAE,CACA,KAAK,EACL;MACEG,WAAW,EAAE,OAAO;MACpBC,KAAK,EAAE2B,IAAI,CAACtB,KAAK,CAACkE;IACpB,CAAC,EACD,CACE3E,EAAE,CACA,KAAK,EACL;MACEG,WAAW,EAAE,cAAc;MAC3BM,KAAK,EAAE;QACLmE,SAAS,EAAE7C,IAAI,CAACtB,KAAK,CAACmE,SAAS;QAC/BlC,UAAU,EAAEX,IAAI,CAACtB,KAAK,CAACoE,QAAQ;QAC/B5D,KAAK,EAAEc,IAAI,CAACtB,KAAK,CAACqE;MACpB;IACF,CAAC,EACD,CACE9E,EAAE,CAAC,QAAQ,EAAE;MACXG,WAAW,EAAE,aAAa;MAC1BoB,KAAK,EAAE;QAAEwD,SAAS,EAAEhF,GAAG,CAACiF,QAAQ,CAACC;MAAO;IAC1C,CAAC,CAAC,EACFjF,EAAE,CAAC,MAAM,EAAE,CACTD,GAAG,CAACmB,EAAE,CAACnB,GAAG,CAACoB,EAAE,CAACY,IAAI,CAACX,MAAM,CAAC8D,WAAW,CAAC,CAAC,CACxC,CAAC,CACH,EACD,CAAC,CACF,CACF,CACF,CACF,CACF,GACDnD,IAAI,CAACK,IAAI,IAAI,QAAQ,GACrBpC,EAAE,CACA,KAAK,EACL;MACEG,WAAW,EAAE,YAAY;MACzBM,KAAK,EAAE;QAAE8B,OAAO,YAAKR,IAAI,CAACtB,KAAK,CAAC+B,UAAU;MAAO;IACnD,CAAC,EACD,CACExC,EAAE,CACA,KAAK,EACL;MACEG,WAAW,EAAE,aAAa;MAC1BM,KAAK,EAAE;QACLiC,UAAU,EAAEX,IAAI,CAACtB,KAAK,CAACiC,UAAU;QACjCzB,KAAK,EAAEc,IAAI,CAACtB,KAAK,CAAC0E;MACpB;IACF,CAAC,EACD,CACEnF,EAAE,CACA,KAAK,EACL;MAAEG,WAAW,EAAE;IAAe,CAAC,EAC/B,CACEH,EAAE,CAAC,QAAQ,EAAE;MACXG,WAAW,EAAE,aAAa;MAC1BoB,KAAK,EAAE;QAAEwD,SAAS,EAAEhF,GAAG,CAACiF,QAAQ,CAACI;MAAW;IAC9C,CAAC,CAAC,CACH,EACD,CAAC,CACF,EACDpF,EAAE,CACA,KAAK,EACL;MACEG,WAAW,EACT,oCAAoC;MACtCM,KAAK,EAAE;QAAE4E,QAAQ,YAAKtD,IAAI,CAACtB,KAAK,CAAC4E,QAAQ;MAAK;IAChD,CAAC,EACD,CAACrF,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACmB,EAAE,CAACnB,GAAG,CAACoB,EAAE,CAACY,IAAI,CAACX,MAAM,CAACkE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CACjD,CACF,CACF,CACF,CACF,GACDvD,IAAI,CAACK,IAAI,IAAI,QAAQ,GACrBpC,EAAE,CACA,KAAK,EACL;MACEG,WAAW,EAAE,YAAY;MACzBM,KAAK,EAAE;QACL8B,OAAO,YAAKR,IAAI,CAACtB,KAAK,CAAC+B,UAAU,SAAM;QACvCE,UAAU,EAAEX,IAAI,CAACtB,KAAK,CAACiC,UAAU;QACjCzB,KAAK,EAAEc,IAAI,CAACtB,KAAK,CAAC0E;MACpB;IACF,CAAC,EACD,CACEnF,EAAE,CACA,KAAK,EACL;MACEG,WAAW,EAAE,oBAAoB;MACjCC,KAAK,mBAAY2B,IAAI,CAACtB,KAAK,CAAC8E,OAAO;IACrC,CAAC,EACDxF,GAAG,CAAC+B,EAAE,CAACC,IAAI,CAACxB,IAAI,EAAE,UAAUgD,OAAO,EAAEX,OAAO,EAAE;MAC5C,OAAO5C,EAAE,CACP,KAAK,EACL;QACEiC,GAAG,YAAKD,KAAK,cAAIY,OAAO,CAAE;QAC1BzC,WAAW,EAAE;MACf,CAAC,EACD,CACEH,EAAE,CAAC,KAAK,EAAE;QAAEG,WAAW,EAAE;MAAa,CAAC,EAAE,CACvCH,EAAE,CAAC,KAAK,EAAE;QACRG,WAAW,EAAE,OAAO;QACpBM,KAAK,EAAE;UACL+E,KAAK,YAAKzD,IAAI,CAACtB,KAAK,CAACgF,SAAS,OAAI;UAClC3B,MAAM,YAAK/B,IAAI,CAACtB,KAAK,CAACgF,SAAS;QACjC,CAAC;QACDlE,KAAK,EAAE;UAAE4B,GAAG,EAAEI,OAAO,CAACH;QAAO;MAC/B,CAAC,CAAC,CACH,CAAC,EACFpD,EAAE,CACA,GAAG,EACH;QAAEG,WAAW,EAAE;MAAyB,CAAC,EACzC,CAACJ,GAAG,CAACmB,EAAE,CAACnB,GAAG,CAACoB,EAAE,CAACoC,OAAO,CAAC+B,IAAI,CAAC,CAAC,CAAC,CAC/B,CACF,CACF;IACH,CAAC,CAAC,EACF,CAAC,CACF,CACF,CACF,GACDvD,IAAI,CAACK,IAAI,IAAI,OAAO,GACpBpC,EAAE,CACA,KAAK,EACL;MACEG,WAAW,EAAE,WAAW;MACxBM,KAAK,EAAE;QACLiC,UAAU,EAAEX,IAAI,CAACtB,KAAK,CAACiC,UAAU;QACjCH,OAAO,YAAKR,IAAI,CAACtB,KAAK,CAACgE,QAAQ,gBAAM1C,IAAI,CAACtB,KAAK,CAACiE,QAAQ;MAC1D;IACF,CAAC,EACD,CACE1E,EAAE,CACA,KAAK,EACL;MACEG,WAAW,EAAE,YAAY;MACzBC,KAAK,EAAE,mBACM2B,IAAI,CAACtB,KAAK,CAACiF,OAAO,oBACnB3D,IAAI,CAACtB,KAAK,CAACkF,MAAM,EAC5B;MACDlF,KAAK,EAAE;QACL+C,YAAY,aAAMzB,IAAI,CAACtB,KAAK,CAACgD,UAAU;MACzC;IACF,CAAC,EACD1D,GAAG,CAAC+B,EAAE,CACJC,IAAI,CAACX,MAAM,CAAC8C,MAAM,IAAI,QAAQ,IAAInC,IAAI,CAACxB,IAAI,CAACqD,MAAM,GAC9C7B,IAAI,CAACxB,IAAI,GACTwB,IAAI,CAACoC,WAAW,EACpB,UAAUZ,OAAO,EAAEX,OAAO,EAAE;MAC1B,OAAO5C,EAAE,CACP,KAAK,EACL;QACEiC,GAAG,YAAKD,KAAK,cAAIY,OAAO,CAAE;QAC1BzC,WAAW,EAAE,YAAY;QACzBC,KAAK,EAAE,mBAAY2B,IAAI,CAACtB,KAAK,CAACmF,QAAQ,EAAG;QACzCnF,KAAK,EAAE;UACL+C,YAAY,YAAKzB,IAAI,CAACtB,KAAK,CAACgD,UAAU,OAAI;UAC1CP,YAAY,YAAKnB,IAAI,CAACtB,KAAK,CAACyC,YAAY;QAC1C;MACF,CAAC,EACD,CACEnB,IAAI,CAACtB,KAAK,CAACkF,MAAM,IAAI,CAAC,GAClB,CACE3F,EAAE,CAAC,KAAK,EAAE;QAAEG,WAAW,EAAE;MAAO,CAAC,EAAE,CACjCH,EAAE,CACA,KAAK,EACL;QAAEG,WAAW,EAAE;MAAkB,CAAC,EAClC,CACEH,EAAE,CAAC,KAAK,EAAE;QACRG,WAAW,EAAE,OAAO;QACpBoB,KAAK,EAAE;UACL4B,GAAG,EAAEI,OAAO,CAACsC;QACf;MACF,CAAC,CAAC,CACH,CACF,EACD7F,EAAE,CACA,KAAK,EACL;QAAEG,WAAW,EAAE;MAAmB,CAAC,EACnC,CACEH,EAAE,CACA,KAAK,EACL;QAAEG,WAAW,EAAE;MAAa,CAAC,EAC7B,CACEJ,GAAG,CAACoC,OAAO,CACT,WAAW,EACXJ,IAAI,CAACtB,KAAK,CAACqF,IAAI,CAChB,GACG9F,EAAE,CACA,KAAK,EACL;QACEG,WAAW,EACT,YAAY;QACdC,KAAK,EAAE,CACL2B,IAAI,CAACtB,KAAK,CACPsF,aAAa,IAChB,KAAK,GACD,cAAc,GACd,cAAc,gBACXhE,IAAI,CAACtB,KAAK,CAACsF,aAAa;MAEnC,CAAC,EACD,CACEhG,GAAG,CAACmB,EAAE,CACJnB,GAAG,CAACoB,EAAE,CACJoC,OAAO,CAACyC,UAAU,CACnB,CACF,CACF,CACF,GACDjG,GAAG,CAAC8D,EAAE,EAAE,EACZ9D,GAAG,CAACoC,OAAO,CACT,cAAc,EACdJ,IAAI,CAACtB,KAAK,CAACqF,IAAI,CAChB,GACG9F,EAAE,CACA,KAAK,EACL;QACEG,WAAW,EACT;MACJ,CAAC,EACD,CACEH,EAAE,CACA,MAAM,EACN;QACEG,WAAW,EACT,sBAAsB;QACxBM,KAAK,EAAE;UACLQ,KAAK,EACHc,IAAI,CAACtB,KAAK,CACPwF;QACP;MACF,CAAC,EACD,CACElG,GAAG,CAACmB,EAAE,CACJnB,GAAG,CAACoB,EAAE,CACJoC,OAAO,CAAC2C,aAAa,CACtB,CACF,CACF,CACF,CACF,CACF,GACDnG,GAAG,CAAC8D,EAAE,EAAE,EACZ9D,GAAG,CAACoC,OAAO,CACT,YAAY,EACZJ,IAAI,CAACtB,KAAK,CAACqF,IAAI,CAChB,GACG9F,EAAE,CACA,KAAK,EACL;QACEG,WAAW,EACT;MACJ,CAAC,EACD,CACEH,EAAE,CACA,MAAM,EACN;QACEG,WAAW,EACT;MACJ,CAAC,EACD,CACEJ,GAAG,CAACmB,EAAE,CACJ,IAAI,GACFnB,GAAG,CAACoB,EAAE,CACJoC,OAAO,CAAC4C,WAAW,CACpB,CACJ,CACF,CACF,CACF,CACF,GACDpG,GAAG,CAAC8D,EAAE,EAAE,EACZ7D,EAAE,CACA,KAAK,EACL;QAAEG,WAAW,EAAE;MAAS,CAAC,EACzB,CACEH,EAAE,CACA,KAAK,EACL;QACEG,WAAW,EACT,0BAA0B;QAC5BM,KAAK,EAAE;UACLQ,KAAK,EACHc,IAAI,CAACtB,KAAK,CACP2F;QACP;MACF,CAAC,EACD,CACErG,GAAG,CAACoC,OAAO,CACT,YAAY,EACZJ,IAAI,CAACtB,KAAK,CAACqF,IAAI,CAChB,GACG,CACE9F,EAAE,CACA,MAAM,EACN;QACEG,WAAW,EACT;MACJ,CAAC,EACD,CAACJ,GAAG,CAACmB,EAAE,CAAC,GAAG,CAAC,CAAC,CACd,EACDlB,EAAE,CACA,MAAM,EACN;QACEG,WAAW,EACT;MACJ,CAAC,EACD,CACEJ,GAAG,CAACmB,EAAE,CACJnB,GAAG,CAACoB,EAAE,CACJoC,OAAO,CAAC8C,eAAe,CACxB,CACF,CACF,CACF,CACF,GACDtG,GAAG,CAAC8D,EAAE,EAAE,EACZ9D,GAAG,CAACoC,OAAO,CACT,WAAW,EACXJ,IAAI,CAACtB,KAAK,CAACqF,IAAI,CAChB,GACG9F,EAAE,CACA,MAAM,EACN;QACEG,WAAW,EACT;MACJ,CAAC,EACD,CACEH,EAAE,CACA,MAAM,EACN;QACEG,WAAW,EACT;MACJ,CAAC,EACD,CACEJ,GAAG,CAACmB,EAAE,CACJ,GAAG,CACJ,CACF,CACF,EACDlB,EAAE,CACA,MAAM,EACN;QACEG,WAAW,EACT;MACJ,CAAC,EACD,CACEJ,GAAG,CAACmB,EAAE,CACJnB,GAAG,CAACoB,EAAE,CACJoC,OAAO,CAAC+C,cAAc,CACvB,CACF,CACF,CACF,CACF,CACF,GACDvG,GAAG,CAAC8D,EAAE,EAAE,CACb,EACD,CAAC,CACF,EACD7D,EAAE,CACA,KAAK,EACL;QACE6C,UAAU,EAAE,CACV;UACEC,IAAI,EAAE,MAAM;UACZC,OAAO,EAAE,QAAQ;UACjBC,KAAK,EACHjD,GAAG,CAACoC,OAAO,CACT,SAAS,EACTJ,IAAI,CAACtB,KAAK,CACPqF,IAAI,CACR,IACD/D,IAAI,CAACtB,KAAK,CACPkF,MAAM,GAAG,CAAC;UACf1C,UAAU,EACR;QACJ,CAAC,CACF;QACD9C,WAAW,EAAE;MACf,CAAC,EACD,CACEH,EAAE,CACA,KAAK,EACL;QACEG,WAAW,EACT,UAAU;QACZM,KAAK,EAAE;UACLQ,KAAK,EACHc,IAAI,CAACtB,KAAK,CACP8F;QACP;MACF,CAAC,EACD,CACEvG,EAAE,CAAC,QAAQ,EAAE;QACXG,WAAW,EACT,WAAW;QACboB,KAAK,EAAE;UACLwD,SAAS,EACPhF,GAAG,CACAiF,QAAQ,iBACAjD,IAAI,CAACtB,KAAK,CAAC+F,YAAY;QAEtC;MACF,CAAC,CAAC,CACH,EACD,CAAC,CACF,CACF,CACF,CACF,CACF,CACF,CACF,CACF,CACF,CACF,CAAC,CACH,GACD,CACExG,EAAE,CACA,KAAK,EACL;QAAEG,WAAW,EAAE;MAAc,CAAC,EAC9B,CACEH,EAAE,CAAC,KAAK,EAAE;QACRG,WAAW,EAAE,OAAO;QACpBoB,KAAK,EAAE;UACL4B,GAAG,EAAEI,OAAO,CAACsC;QACf;MACF,CAAC,CAAC,CACH,CACF,EACD7F,EAAE,CACA,KAAK,EACL;QAAEG,WAAW,EAAE;MAAa,CAAC,EAC7B,CACEJ,GAAG,CAACoC,OAAO,CACT,WAAW,EACXJ,IAAI,CAACtB,KAAK,CAACqF,IAAI,CAChB,GACG9F,EAAE,CACA,KAAK,EACL;QACEG,WAAW,EAAE,YAAY;QACzBC,KAAK,EAAE,CACL2B,IAAI,CAACtB,KAAK,CACPsF,aAAa,IAAI,KAAK,GACrB,cAAc,GACd,cAAc,gBACXhE,IAAI,CAACtB,KAAK,CAACsF,aAAa;MAEnC,CAAC,EACD,CACEhG,GAAG,CAACmB,EAAE,CACJnB,GAAG,CAACoB,EAAE,CAACoC,OAAO,CAACyC,UAAU,CAAC,CAC3B,CACF,CACF,GACDjG,GAAG,CAAC8D,EAAE,EAAE,EACZ9D,GAAG,CAACoC,OAAO,CACT,cAAc,EACdJ,IAAI,CAACtB,KAAK,CAACqF,IAAI,CAChB,GACG9F,EAAE,CACA,KAAK,EACL;QACEG,WAAW,EACT;MACJ,CAAC,EACD,CACEH,EAAE,CACA,MAAM,EACN;QACEG,WAAW,EACT,sBAAsB;QACxBM,KAAK,EAAE;UACLQ,KAAK,EACHc,IAAI,CAACtB,KAAK,CACPwF;QACP;MACF,CAAC,EACD,CACElG,GAAG,CAACmB,EAAE,CACJnB,GAAG,CAACoB,EAAE,CACJoC,OAAO,CAAC2C,aAAa,CACtB,CACF,CACF,CACF,CACF,CACF,GACDnG,GAAG,CAAC8D,EAAE,EAAE,EACZ9D,GAAG,CAACoC,OAAO,CACT,YAAY,EACZJ,IAAI,CAACtB,KAAK,CAACqF,IAAI,CAChB,GACG9F,EAAE,CACA,KAAK,EACL;QACEG,WAAW,EACT;MACJ,CAAC,EACD,CACEH,EAAE,CACA,MAAM,EACN;QAAEG,WAAW,EAAE;MAAQ,CAAC,EACxB,CACEJ,GAAG,CAACmB,EAAE,CACJ,IAAI,GACFnB,GAAG,CAACoB,EAAE,CACJoC,OAAO,CAAC4C,WAAW,CACpB,CACJ,CACF,CACF,CACF,CACF,GACDpG,GAAG,CAAC8D,EAAE,EAAE,EACZ7D,EAAE,CACA,KAAK,EACL;QAAEG,WAAW,EAAE;MAAS,CAAC,EACzB,CACEJ,GAAG,CAACoC,OAAO,CACT,YAAY,EACZJ,IAAI,CAACtB,KAAK,CAACqF,IAAI,CAChB,GACG9F,EAAE,CACA,KAAK,EACL;QACEG,WAAW,EACT,0BAA0B;QAC5BM,KAAK,EAAE;UACLQ,KAAK,EACHc,IAAI,CAACtB,KAAK,CACP2F;QACP;MACF,CAAC,EACD,CACEpG,EAAE,CACA,MAAM,EACN;QACEG,WAAW,EAAE;MACf,CAAC,EACD,CAACJ,GAAG,CAACmB,EAAE,CAAC,GAAG,CAAC,CAAC,CACd,EACDlB,EAAE,CACA,MAAM,EACN;QACEG,WAAW,EACT;MACJ,CAAC,EACD,CACEJ,GAAG,CAACmB,EAAE,CACJnB,GAAG,CAACoB,EAAE,CACJoC,OAAO,CAAC8C,eAAe,CACxB,CACF,CACF,CACF,EACDtG,GAAG,CAACoC,OAAO,CACT,WAAW,EACXJ,IAAI,CAACtB,KAAK,CAACqF,IAAI,CAChB,GACG9F,EAAE,CACA,MAAM,EACN;QACEG,WAAW,EACT;MACJ,CAAC,EACD,CACEH,EAAE,CACA,MAAM,EACN;QACEG,WAAW,EACT;MACJ,CAAC,EACD,CAACJ,GAAG,CAACmB,EAAE,CAAC,GAAG,CAAC,CAAC,CACd,EACDlB,EAAE,CACA,MAAM,EACN;QACEG,WAAW,EACT;MACJ,CAAC,EACD,CACEJ,GAAG,CAACmB,EAAE,CACJnB,GAAG,CAACoB,EAAE,CACJoC,OAAO,CAAC+C,cAAc,CACvB,CACF,CACF,CACF,CACF,CACF,GACDvG,GAAG,CAAC8D,EAAE,EAAE,CACb,CACF,GACD9D,GAAG,CAAC8D,EAAE,EAAE,EACZ7D,EAAE,CACA,KAAK,EACL;QACE6C,UAAU,EAAE,CACV;UACEC,IAAI,EAAE,MAAM;UACZC,OAAO,EAAE,QAAQ;UACjBC,KAAK,EACHjD,GAAG,CAACoC,OAAO,CACT,SAAS,EACTJ,IAAI,CAACtB,KAAK,CAACqF,IAAI,CAChB,IACD/D,IAAI,CAACtB,KAAK,CAACkF,MAAM,GAAG,CAAC;UACvB1C,UAAU,EACR;QACJ,CAAC,CACF;QACD9C,WAAW,EAAE;MACf,CAAC,EACD,CACEH,EAAE,CACA,KAAK,EACL;QACEG,WAAW,EAAE,UAAU;QACvBM,KAAK,EAAE;UACLQ,KAAK,EACHc,IAAI,CAACtB,KAAK,CACP8F;QACP;MACF,CAAC,EACD,CACEvG,EAAE,CAAC,QAAQ,EAAE;QACXG,WAAW,EACT,WAAW;QACboB,KAAK,EAAE;UACLwD,SAAS,EACPhF,GAAG,CAACiF,QAAQ,iBACDjD,IAAI,CAACtB,KAAK,CAAC+F,YAAY;QAEtC;MACF,CAAC,CAAC,CACH,EACD,CAAC,CACF,CACF,CACF,CACF,CACF,CACF,CACF,CACF,CACN,EACD,CAAC,CACF;IACH,CAAC,CACF,EACD,CAAC,CACF,CACF,CACF,GACDzE,IAAI,CAACK,IAAI,IAAI,OAAO,GACpBpC,EAAE,CAAC,KAAK,EAAE;MACRG,WAAW,EAAE,WAAW;MACxBM,KAAK,EAAE;QACLqD,MAAM,YAAK/B,IAAI,CAACtB,KAAK,CAACqD,MAAM,OAAI;QAChCpB,UAAU,EAAEX,IAAI,CAACtB,KAAK,CAACiC;MACzB;IACF,CAAC,CAAC,GACFX,IAAI,CAACK,IAAI,IAAI,OAAO,GACpBpC,EAAE,CACA,KAAK,EACL;MACEG,WAAW,EAAE,WAAW;MACxBM,KAAK,EAAE;QACL8B,OAAO,YAAKR,IAAI,CAACtB,KAAK,CAAC+B,UAAU,SAAM;QACvCE,UAAU,EAAEX,IAAI,CAACtB,KAAK,CAACiC;MACzB;IACF,CAAC,EACD,CACE1C,EAAE,CAAC,GAAG,EAAE;MACNG,WAAW,EAAE,MAAM;MACnBM,KAAK,EAAE;QACLgG,cAAc,EAAE1E,IAAI,CAACtB,KAAK,CAACiG,UAAU,GAAG,IAAI;QAC5CC,cAAc,EAAE5E,IAAI,CAACtB,KAAK,CAACmG,SAAS;QACpCC,cAAc,EAAE9E,IAAI,CAACtB,KAAK,CAACqG;MAC7B;IACF,CAAC,CAAC,CACH,CACF,GACD/E,IAAI,CAACK,IAAI,IAAI,SAAS,GACtBpC,EAAE,CACA,KAAK,EACL;MACEG,WAAW,EAAE,aAAa;MAC1BM,KAAK,EAAE;QAAEsG,OAAO,EAAEhF,IAAI,CAACtB,KAAK,CAACsG,OAAO,GAAG;MAAI;IAC7C,CAAC,EACD,CACE/G,EAAE,CAAC,KAAK,EAAE;MAAEG,WAAW,EAAE;IAAe,CAAC,EAAE,CACzCH,EAAE,CAAC,KAAK,EAAE;MACRG,WAAW,EAAE,OAAO;MACpBoB,KAAK,EAAE;QAAE4B,GAAG,EAAEpB,IAAI,CAACX,MAAM,CAACkD,KAAK;QAAEC,GAAG,EAAE;MAAG;IAC3C,CAAC,CAAC,CACH,CAAC,CACH,CACF,GACDxC,IAAI,CAACK,IAAI,IAAI,UAAU,GACvBpC,EAAE,CAAC,KAAK,EAAE;MACRG,WAAW,EAAE,cAAc;MAC3BM,KAAK,EAAE;QACLiC,UAAU,EAAEX,IAAI,CAACtB,KAAK,CAACiC,UAAU;QACjCH,OAAO,YAAKR,IAAI,CAACtB,KAAK,CAAC+B,UAAU,gBAAMT,IAAI,CAACtB,KAAK,CAACgC,WAAW;MAC/D,CAAC;MACDuE,QAAQ,EAAE;QAAEC,SAAS,EAAElH,GAAG,CAACoB,EAAE,CAACY,IAAI,CAACX,MAAM,CAAC8F,OAAO;MAAE;IACrD,CAAC,CAAC,GACFnF,IAAI,CAACK,IAAI,IAAI,iBAAiB,GAC9BpC,EAAE,CAAC,KAAK,EAAE;MAAEG,WAAW,EAAE;IAAsB,CAAC,EAAE,CAChDH,EAAE,CAAC,KAAK,EAAE;MAAEG,WAAW,EAAE;IAAW,CAAC,EAAE,CACrCH,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACmB,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,CAC/B,CAAC,EACFlB,EAAE,CAAC,KAAK,EAAE;MAAEG,WAAW,EAAE;IAAe,CAAC,EAAE,CACzCH,EAAE,CAAC,KAAK,EAAE;MAAEG,WAAW,EAAE;IAAmB,CAAC,EAAE,CAC7CH,EAAE,CAAC,KAAK,EAAE;MACRG,WAAW,EAAE,OAAO;MACpBoB,KAAK,EAAE;QACL4B,GAAG,EAAEgE,OAAO,CAAC,2BAA2B,CAAC;QACzC5C,GAAG,EAAE;MACP;IACF,CAAC,CAAC,CACH,CAAC,EACFvE,EAAE,CAAC,KAAK,EAAE;MAAEG,WAAW,EAAE;IAAmB,CAAC,EAAE,CAC7CH,EAAE,CAAC,KAAK,EAAE;MAAEG,WAAW,EAAE;IAAc,CAAC,EAAE,CACxCH,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACmB,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,CAC9B,CAAC,EACFlB,EAAE,CAAC,KAAK,EAAE;MAAEG,WAAW,EAAE;IAAkB,CAAC,EAAE,CAC5CH,EAAE,CAAC,MAAM,EAAE,CACTD,GAAG,CAACmB,EAAE,CAAC,iBAAiB,CAAC,CAC1B,CAAC,CACH,CAAC,CACH,CAAC,EACFlB,EAAE,CAAC,KAAK,EAAE;MAAEG,WAAW,EAAE;IAAmB,CAAC,EAAE,CAC7CH,EAAE,CAAC,KAAK,EAAE;MAAEG,WAAW,EAAE;IAAa,CAAC,EAAE,CACvCH,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACmB,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAC3B,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,GACFa,IAAI,CAACK,IAAI,IAAI,MAAM,GACnBpC,EAAE,CACA,KAAK,EACL;MACEG,WAAW,EAAE,UAAU;MACvBM,KAAK,EAAE;QAAEiC,UAAU,EAAEX,IAAI,CAACtB,KAAK,CAACiC;MAAW;IAC7C,CAAC,EACD3C,GAAG,CAAC+B,EAAE,CACJC,IAAI,CAACX,MAAM,CAAC8C,MAAM,IAAI,QAAQ,IAAInC,IAAI,CAACxB,IAAI,CAACqD,MAAM,GAC9C7B,IAAI,CAACxB,IAAI,GACTwB,IAAI,CAACoC,WAAW,EACpB,UAAUiD,IAAI,EAAEC,GAAG,EAAE;MACnB,OAAOrH,EAAE,CACP,KAAK,EACL;QAAEiC,GAAG,EAAEoF,GAAG;QAAElH,WAAW,EAAE;MAAY,CAAC,EACtC,CACEJ,GAAG,CAACoC,OAAO,CAAC,MAAM,EAAEJ,IAAI,CAACtB,KAAK,CAACqF,IAAI,CAAC,GAChC9F,EAAE,CACA,KAAK,EACL;QAAEG,WAAW,EAAE;MAAkB,CAAC,EAClC,CACEH,EAAE,CAAC,KAAK,EAAE;QACRG,WAAW,EAAE,OAAO;QACpBoB,KAAK,EAAE;UACL4B,GAAG,EAAEiE,IAAI,CAACE,QAAQ;UAClB/C,GAAG,EAAE;QACP;MACF,CAAC,CAAC,CACH,CACF,GACDxE,GAAG,CAAC8D,EAAE,EAAE,EACZ7D,EAAE,CAAC,KAAK,EAAE;QAAEG,WAAW,EAAE;MAAqB,CAAC,EAAE,CAC/CH,EAAE,CAAC,KAAK,EAAE;QAAEG,WAAW,EAAE;MAAmB,CAAC,EAAE,CAC7CH,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACmB,EAAE,CAACnB,GAAG,CAACoB,EAAE,CAACiG,IAAI,CAACG,SAAS,CAAC,CAAC,CAAC,CAAC,CAC7C,CAAC,EACFxH,GAAG,CAACoC,OAAO,CAAC,SAAS,EAAEJ,IAAI,CAACtB,KAAK,CAACqF,IAAI,CAAC,GACnC9F,EAAE,CACA,KAAK,EACL;QACEG,WAAW,EACT;MACJ,CAAC,EACD,CACEH,EAAE,CAAC,MAAM,EAAE,CACTD,GAAG,CAACmB,EAAE,CACJ,OAAO,GACLnB,GAAG,CAACoB,EAAE,CAACiG,IAAI,CAACI,MAAM,CAACC,QAAQ,CAAC,GAC5B1H,GAAG,CAACoB,EAAE,CAACiG,IAAI,CAACI,MAAM,CAACE,IAAI,CAAC,GACxB3H,GAAG,CAACoB,EAAE,CAACiG,IAAI,CAACI,MAAM,CAACA,MAAM,CAAC,GAC1BzH,GAAG,CAACoB,EAAE,CAACiG,IAAI,CAACO,OAAO,CAAC,CACvB,CACF,CAAC,CACH,CACF,GACD5H,GAAG,CAAC8D,EAAE,EAAE,EACZ9D,GAAG,CAACoC,OAAO,CAAC,OAAO,EAAEJ,IAAI,CAACtB,KAAK,CAACqF,IAAI,CAAC,GACjC9F,EAAE,CACA,KAAK,EACL;QAAEG,WAAW,EAAE;MAAmB,CAAC,EACnC,CACEH,EAAE,CAAC,MAAM,EAAE,CACTD,GAAG,CAACmB,EAAE,CACJ,OAAO,GAAGnB,GAAG,CAACoB,EAAE,CAACiG,IAAI,CAACQ,KAAK,CAAC,CAC7B,CACF,CAAC,CACH,CACF,GACD7H,GAAG,CAAC8D,EAAE,EAAE,CACb,CAAC,CACH,CACF;IACH,CAAC,CACF,EACD,CAAC,CACF,GACD9B,IAAI,CAACK,IAAI,IAAI,SAAS,GACtBpC,EAAE,CACA,KAAK,EACL;MACEG,WAAW,EAAE,aAAa;MAC1BM,KAAK,EAAE;QACLiC,UAAU,EAAEX,IAAI,CAACtB,KAAK,CAACiC,UAAU;QACjCH,OAAO,YAAKR,IAAI,CAACtB,KAAK,CAACgE,QAAQ,gBAAM1C,IAAI,CAACtB,KAAK,CAACiE,QAAQ;MAC1D;IACF,CAAC,EACD,CACE1E,EAAE,CACA,KAAK,EACL;MACEG,WAAW,EAAE,WAAW;MACxBM,KAAK,EAAE;QACLyC,YAAY,YAAKnB,IAAI,CAACtB,KAAK,CAACyC,YAAY;MAC1C;IACF,CAAC,EACD,CACEnB,IAAI,CAACX,MAAM,CAACC,KAAK,CAACwG,MAAM,GACpB7H,EAAE,CACA,KAAK,EACL;MACEG,WAAW,EAAE,WAAW;MACxBM,KAAK,EAAE;QACLqH,eAAe,gBAAS/F,IAAI,CAACX,MAAM,CAACC,KAAK,CAAC0G,OAAO;MACnD;IACF,CAAC,EACD,CACE/H,EAAE,CACA,KAAK,EACL;MAAEG,WAAW,EAAE;IAAkB,CAAC,EAClC,CACEH,EAAE,CACA,KAAK,EACL;MAAEG,WAAW,EAAE;IAAc,CAAC,EAC9B,CACEH,EAAE,CAAC,KAAK,EAAE;MACRG,WAAW,EAAE,OAAO;MACpBoB,KAAK,EAAE;QACL4B,GAAG,EAAEpB,IAAI,CAACX,MAAM,CAACC,KAAK,CAACiD,KAAK;QAC5BC,GAAG,EAAE;MACP;IACF,CAAC,CAAC,CACH,CACF,EACDvE,EAAE,CACA,KAAK,EACL;MACEG,WAAW,EAAE,YAAY;MACzBM,KAAK,EAAE;QACLQ,KAAK,EACHc,IAAI,CAACtB,KAAK,CAACY,KAAK,CAAC2G;MACrB;IACF,CAAC,EACD,CACEhI,EAAE,CAAC,MAAM,EAAE,CACTD,GAAG,CAACmB,EAAE,CACJnB,GAAG,CAACoB,EAAE,CACJY,IAAI,CAACX,MAAM,CAACC,KAAK,CAAC4G,QAAQ,CAC3B,CACF,CACF,CAAC,CACH,CACF,CACF,CACF,EACDjI,EAAE,CACA,KAAK,EACL;MAAEG,WAAW,EAAE;IAAmB,CAAC,EACnC,CACEH,EAAE,CACA,KAAK,EACL;MACEG,WAAW,EAAE,YAAY;MACzBM,KAAK,EAAE;QACLQ,KAAK,EACHc,IAAI,CAACtB,KAAK,CAACY,KAAK,CAAC6G;MACrB;IACF,CAAC,EACD,CACElI,EAAE,CACA,MAAM,EACN;MAAEG,WAAW,EAAE;IAAY,CAAC,EAC5B,CACEJ,GAAG,CAACmB,EAAE,CACJnB,GAAG,CAACoB,EAAE,CACJY,IAAI,CAACX,MAAM,CAACC,KAAK,CAAC8G,SAAS,CAC5B,CACF,CACF,CACF,EACDnI,EAAE,CACA,MAAM,EACN;MAAEG,WAAW,EAAE;IAAa,CAAC,EAC7B,CACEH,EAAE,CAAC,QAAQ,EAAE;MACXuB,KAAK,EAAE;QACLwD,SAAS,EACPhF,GAAG,CAACqI,IAAI,CAACC;MACb;IACF,CAAC,CAAC,CACH,EACD,CAAC,CACF,CACF,CACF,CACF,CACF,CACF,CACF,GACDtI,GAAG,CAAC8D,EAAE,EAAE,EACZ7D,EAAE,CACA,KAAK,EACL;MACEG,WAAW,EAAE,YAAY;MACzBC,KAAK,EAAE,mBACM2B,IAAI,CAACX,MAAM,CAACsE,OAAO,oBACpB3D,IAAI,CAACX,MAAM,CAACuE,MAAM,EAC7B;MACDlF,KAAK,EAAE;QACL+C,YAAY,aAAMzB,IAAI,CAACtB,KAAK,CAACyG,OAAO,CAACzD,UAAU,OAAI;QACnDlB,OAAO,YAAKR,IAAI,CAACtB,KAAK,CAACyG,OAAO,CAACoB,YAAY;MAC7C;IACF,CAAC,EACDvI,GAAG,CAAC+B,EAAE,CACJC,IAAI,CAACX,MAAM,CAAC8C,MAAM,IAAI,QAAQ,IAC5BnC,IAAI,CAACxB,IAAI,CAACqD,MAAM,GACd7B,IAAI,CAACxB,IAAI,GACTwB,IAAI,CAACoC,WAAW,EACpB,UAAUZ,OAAO,EAAEX,OAAO,EAAE;MAC1B,OAAO5C,EAAE,CACP,KAAK,EACL;QACEiC,GAAG,YAAKD,KAAK,cAAIY,OAAO,CAAE;QAC1BzC,WAAW,EAAE,YAAY;QACzBM,KAAK,EAAE;UACL+C,YAAY,YAAKzB,IAAI,CAACtB,KAAK,CAACyG,OAAO,CAACzD,UAAU;QAChD;MACF,CAAC,EACD,CACE1B,IAAI,CAACX,MAAM,CAACuE,MAAM,IAAI,CAAC,GACnB,CACE3F,EAAE,CAAC,KAAK,EAAE;QAAEG,WAAW,EAAE;MAAO,CAAC,EAAE,CACjCH,EAAE,CACA,KAAK,EACL;QACEG,WAAW,EACT;MACJ,CAAC,EACD,CACEH,EAAE,CAAC,KAAK,EAAE;QACRG,WAAW,EAAE,OAAO;QACpBM,KAAK,EAAE;UACLyC,YAAY,YAAKnB,IAAI,CAACtB,KAAK,CAACyG,OAAO,CAAChE,YAAY;QAClD,CAAC;QACD3B,KAAK,EAAE;UACL4B,GAAG,EAAEI,OAAO,CAACsC;QACf;MACF,CAAC,CAAC,CACH,CACF,EACD7F,EAAE,CACA,KAAK,EACL;QACEG,WAAW,EACT;MACJ,CAAC,EACD,CACEH,EAAE,CACA,KAAK,EACL;QACEG,WAAW,EAAE;MACf,CAAC,EACD,CACEJ,GAAG,CAACoC,OAAO,CACT,WAAW,EACXJ,IAAI,CAACX,MAAM,CAAC0E,IAAI,CACjB,GACG9F,EAAE,CACA,KAAK,EACL;QACEG,WAAW,EACT,YAAY;QACdC,KAAK,EAAE,CACL2B,IAAI,CAACtB,KAAK,CACPyG,OAAO,CACPnB,aAAa,IAChB,KAAK,GACD,cAAc,GACd,cAAc,gBACXhE,IAAI,CAACtB,KAAK,CAACsF,aAAa;MAEnC,CAAC,EACD,CACEhG,GAAG,CAACmB,EAAE,CACJnB,GAAG,CAACoB,EAAE,CACJoC,OAAO,CAACyC,UAAU,CACnB,CACF,CACF,CACF,GACDjG,GAAG,CAAC8D,EAAE,EAAE,EACZ9D,GAAG,CAACoC,OAAO,CACT,SAAS,EACTJ,IAAI,CAACX,MAAM,CAAC0E,IAAI,CACjB,GACG9F,EAAE,CACA,KAAK,EACL;QACEG,WAAW,EACT;MACJ,CAAC,EACD,CACEH,EAAE,CACA,KAAK,EACL;QACEG,WAAW,EACT;MACJ,CAAC,EACDJ,GAAG,CAAC+B,EAAE,CACJC,IAAI,CAACwG,IAAI,CACNC,QAAQ,EACX,UACEC,IAAI,EACJC,IAAI,EACJ;QACA,OAAO1I,EAAE,CACP,KAAK,EACL;UACEiC,GAAG,EAAEyG,IAAI;UACTvI,WAAW,EACT;QACJ,CAAC,EACD,CACEH,EAAE,CACA,KAAK,EACL;UACEG,WAAW,EACT,OAAO;UACToB,KAAK,EACH;YACE4B,GAAG,EAAEsF,IAAI,CACNE,IAAI,CACJC;UACL;QACJ,CAAC,CACF,CACF,CACF;MACH,CAAC,CACF,EACD,CAAC,CACF,EACD5I,EAAE,CACA,KAAK,EACL;QACEG,WAAW,EACT;MACJ,CAAC,EACD,CACEH,EAAE,CAAC,MAAM,EAAE,CACTD,GAAG,CAACmB,EAAE,CACJnB,GAAG,CAACoB,EAAE,CACJY,IAAI,CACDwG,IAAI,CACJM,UAAU,CACd,GACC,OAAO,CACV,CACF,CAAC,CACH,CACF,CACF,CACF,GACD9I,GAAG,CAAC8D,EAAE,EAAE,EACZ7D,EAAE,CACA,KAAK,EACL;QACEG,WAAW,EAAE;MACf,CAAC,EACD,CACEH,EAAE,CACA,KAAK,EACL;QACEG,WAAW,EACT,aAAa;QACfM,KAAK,EAAE;UACLQ,KAAK,EACHc,IAAI,CAACtB,KAAK,CACPyG,OAAO,CACPd;QACP;MACF,CAAC,EACD,CACEpG,EAAE,CACA,KAAK,EACL;QACEG,WAAW,EACT;MACJ,CAAC,EACD,CACEJ,GAAG,CAACoC,OAAO,CACT,YAAY,EACZJ,IAAI,CAACX,MAAM,CACR0E,IAAI,CACR,GACG,CACE9F,EAAE,CACA,MAAM,EACN;QACEG,WAAW,EACT;MACJ,CAAC,EACD,CACEJ,GAAG,CAACmB,EAAE,CACJ,IAAI,CACL,CACF,CACF,EACDlB,EAAE,CACA,MAAM,EACN;QACEG,WAAW,EACT;MACJ,CAAC,EACD,CACEJ,GAAG,CAACmB,EAAE,CACJ,GAAG,CACJ,CACF,CACF,EACDlB,EAAE,CACA,MAAM,EACN;QACEG,WAAW,EACT;MACJ,CAAC,EACD,CACEJ,GAAG,CAACmB,EAAE,CACJnB,GAAG,CAACoB,EAAE,CACJoC,OAAO,CAACuF,WAAW,CACpB,CACF,CACF,CACF,CACF,GACD/I,GAAG,CAAC8D,EAAE,EAAE,CACb,EACD,CAAC,CACF,EACD7D,EAAE,CACA,KAAK,EACL;QACEG,WAAW,EACT;MACJ,CAAC,EACD,CACEJ,GAAG,CAACoC,OAAO,CACT,eAAe,EACfJ,IAAI,CAACX,MAAM,CACR0E,IAAI,CACR,GACG9F,EAAE,CACA,MAAM,EACN;QACEG,WAAW,EACT;MACJ,CAAC,EACD,CACEH,EAAE,CACA,MAAM,EACN;QACEG,WAAW,EACT;MACJ,CAAC,EACD,CACEJ,GAAG,CAACmB,EAAE,CACJ,GAAG,CACJ,CACF,CACF,EACDlB,EAAE,CACA,MAAM,EACN;QACEG,WAAW,EACT;MACJ,CAAC,EACD,CACEJ,GAAG,CAACmB,EAAE,CACJnB,GAAG,CAACoB,EAAE,CACJoC,OAAO,CAACwF,cAAc,CACvB,CACF,CACF,CACF,CACF,CACF,GACDhJ,GAAG,CAAC8D,EAAE,EAAE,CACb,CACF,CACF,CACF,EACD7D,EAAE,CACA,KAAK,EACL;QACE6C,UAAU,EAAE,CACV;UACEC,IAAI,EAAE,MAAM;UACZC,OAAO,EACL,QAAQ;UACVC,KAAK,EACHjD,GAAG,CAACoC,OAAO,CACT,SAAS,EACTJ,IAAI,CACDX,MAAM,CACN0E,IAAI,CACR,IACD/D,IAAI,CAACX,MAAM,CACRuE,MAAM,GACP,CAAC;UACL1C,UAAU,EACR;QACJ,CAAC,CACF;QACD9C,WAAW,EACT;MACJ,CAAC,EACD,CACEH,EAAE,CACA,KAAK,EACL;QACEG,WAAW,EACT,UAAU;QACZM,KAAK,EAAE;UACLQ,KAAK,EACHc,IAAI,CAACtB,KAAK,CACPyG,OAAO,CACP8B,gBAAgB;UACrBtG,UAAU,sCAA+BX,IAAI,CAACtB,KAAK,CAACyG,OAAO,CAAC+B,cAAc;QAC5E;MACF,CAAC,EACD,CACEjJ,EAAE,CAAC,MAAM,EAAE,CACTD,GAAG,CAACmB,EAAE,CACJnB,GAAG,CAACoB,EAAE,CACJY,IAAI,CACDX,MAAM,CACN8H,WAAW,CACf,CACF,CACF,CAAC,CACH,CACF,CACF,CACF,CACF,CACF,CACF,CACF,CACF,CACF,CACF,CAAC,CACH,GACD,CACElJ,EAAE,CACA,KAAK,EACL;QACEG,WAAW,EAAE,aAAa;QAC1BM,KAAK,EAAE;UACLyC,YAAY,YAAKnB,IAAI,CAACtB,KAAK,CAACyG,OAAO,CAAChE,YAAY;QAClD;MACF,CAAC,EACD,CACElD,EAAE,CAAC,KAAK,EAAE;QACRG,WAAW,EAAE,OAAO;QACpBoB,KAAK,EAAE;UACL4B,GAAG,EAAEI,OAAO,CAACsC;QACf;MACF,CAAC,CAAC,CACH,CACF,EACD7F,EAAE,CACA,KAAK,EACL;QAAEG,WAAW,EAAE;MAAa,CAAC,EAC7B,CACEJ,GAAG,CAACoC,OAAO,CACT,WAAW,EACXJ,IAAI,CAACX,MAAM,CAAC0E,IAAI,CACjB,GACG9F,EAAE,CACA,KAAK,EACL;QACEG,WAAW,EACT,YAAY;QACdC,KAAK,EAAE,CACL2B,IAAI,CAACtB,KAAK,CAACyG,OAAO,CACfnB,aAAa,IAChB,KAAK,GACD,cAAc,GACd,cAAc,gBACXhE,IAAI,CAACtB,KAAK,CAACsF,aAAa;MAEnC,CAAC,EACD,CACEhG,GAAG,CAACmB,EAAE,CACJnB,GAAG,CAACoB,EAAE,CACJoC,OAAO,CAACyC,UAAU,CACnB,CACF,CACF,CACF,GACDjG,GAAG,CAAC8D,EAAE,EAAE,EACZ7D,EAAE,CACA,KAAK,EACL;QAAEG,WAAW,EAAE;MAAS,CAAC,EACzB,CACEJ,GAAG,CAACoC,OAAO,CACT,YAAY,EACZJ,IAAI,CAACX,MAAM,CAAC0E,IAAI,CACjB,GACG9F,EAAE,CACA,KAAK,EACL;QACEG,WAAW,EACT,0BAA0B;QAC5BM,KAAK,EAAE;UACLQ,KAAK,EACHc,IAAI,CAACtB,KAAK,CACPyG,OAAO,CACPd;QACP;MACF,CAAC,EACD,CACEpG,EAAE,CACA,MAAM,EACN;QACEG,WAAW,EACT;MACJ,CAAC,EACD,CAACJ,GAAG,CAACmB,EAAE,CAAC,GAAG,CAAC,CAAC,CACd,EACDlB,EAAE,CACA,MAAM,EACN;QACEG,WAAW,EACT;MACJ,CAAC,EACD,CACEJ,GAAG,CAACmB,EAAE,CACJnB,GAAG,CAACoB,EAAE,CACJoC,OAAO,CAACuF,WAAW,CACpB,CACF,CACF,CACF,EACD/I,GAAG,CAACoC,OAAO,CACT,eAAe,EACfJ,IAAI,CAACX,MAAM,CAAC0E,IAAI,CACjB,GACG9F,EAAE,CACA,MAAM,EACN;QACEG,WAAW,EACT;MACJ,CAAC,EACD,CACEH,EAAE,CACA,MAAM,EACN;QACEG,WAAW,EACT;MACJ,CAAC,EACD,CACEJ,GAAG,CAACmB,EAAE,CACJ,GAAG,CACJ,CACF,CACF,EACDlB,EAAE,CACA,MAAM,EACN;QACEG,WAAW,EACT;MACJ,CAAC,EACD,CACEJ,GAAG,CAACmB,EAAE,CACJnB,GAAG,CAACoB,EAAE,CACJoC,OAAO,CAACwF,cAAc,CACvB,CACF,CACF,CACF,CACF,CACF,GACDhJ,GAAG,CAAC8D,EAAE,EAAE,CACb,CACF,GACD9D,GAAG,CAAC8D,EAAE,EAAE,CACb,CACF,CACF,CACF,CACF,CACN,EACD,CAAC,CACF;IACH,CAAC,CACF,EACD,CAAC,CACF,CACF,CACF,CACF,CACF,GACD9B,IAAI,CAACK,IAAI,IAAI,SAAS,GACtBpC,EAAE,CACA,KAAK,EACL;MACEG,WAAW,EAAE,aAAa;MAC1BM,KAAK,EAAE;QACLiC,UAAU,EAAEX,IAAI,CAACtB,KAAK,CAACiC,UAAU;QACjCH,OAAO,YAAKR,IAAI,CAACtB,KAAK,CAACgE,QAAQ,gBAAM1C,IAAI,CAACtB,KAAK,CAACiE,QAAQ;MAC1D;IACF,CAAC,EACD,CACE1E,EAAE,CACA,KAAK,EACL;MACEG,WAAW,EAAE,WAAW;MACxBM,KAAK,EAAE;QACLyC,YAAY,YAAKnB,IAAI,CAACtB,KAAK,CAACyC,YAAY;MAC1C;IACF,CAAC,EACD,CACEnB,IAAI,CAACX,MAAM,CAACC,KAAK,CAACwG,MAAM,GACpB7H,EAAE,CACA,KAAK,EACL;MACEG,WAAW,EAAE,WAAW;MACxBM,KAAK,EAAE;QACLqH,eAAe,gBAAS/F,IAAI,CAACX,MAAM,CAACC,KAAK,CAAC0G,OAAO;MACnD;IACF,CAAC,EACD,CACE/H,EAAE,CACA,KAAK,EACL;MAAEG,WAAW,EAAE;IAAkB,CAAC,EAClC,CACEH,EAAE,CACA,KAAK,EACL;MAAEG,WAAW,EAAE;IAAc,CAAC,EAC9B,CACEH,EAAE,CAAC,KAAK,EAAE;MACRG,WAAW,EAAE,OAAO;MACpBoB,KAAK,EAAE;QACL4B,GAAG,EAAEpB,IAAI,CAACX,MAAM,CAACC,KAAK,CAACiD,KAAK;QAC5BC,GAAG,EAAE;MACP;IACF,CAAC,CAAC,CACH,CACF,EACDvE,EAAE,CACA,KAAK,EACL;MACEG,WAAW,EAAE,YAAY;MACzBM,KAAK,EAAE;QACLQ,KAAK,EACHc,IAAI,CAACtB,KAAK,CAACY,KAAK,CAAC2G;MACrB;IACF,CAAC,EACD,CACEhI,EAAE,CAAC,MAAM,EAAE,CACTD,GAAG,CAACmB,EAAE,CACJnB,GAAG,CAACoB,EAAE,CACJY,IAAI,CAACX,MAAM,CAACC,KAAK,CAAC4G,QAAQ,CAC3B,CACF,CACF,CAAC,CACH,CACF,CACF,CACF,EACDjI,EAAE,CACA,KAAK,EACL;MAAEG,WAAW,EAAE;IAAmB,CAAC,EACnC,CACEH,EAAE,CACA,KAAK,EACL;MACEG,WAAW,EAAE,YAAY;MACzBM,KAAK,EAAE;QACLQ,KAAK,EACHc,IAAI,CAACtB,KAAK,CAACY,KAAK,CAAC6G;MACrB;IACF,CAAC,EACD,CACElI,EAAE,CACA,MAAM,EACN;MAAEG,WAAW,EAAE;IAAY,CAAC,EAC5B,CACEJ,GAAG,CAACmB,EAAE,CACJnB,GAAG,CAACoB,EAAE,CACJY,IAAI,CAACX,MAAM,CAACC,KAAK,CAAC8G,SAAS,CAC5B,CACF,CACF,CACF,EACDnI,EAAE,CACA,MAAM,EACN;MAAEG,WAAW,EAAE;IAAa,CAAC,EAC7B,CACEH,EAAE,CAAC,QAAQ,EAAE;MACXuB,KAAK,EAAE;QACLwD,SAAS,EACPhF,GAAG,CAACqI,IAAI,CAACC;MACb;IACF,CAAC,CAAC,CACH,EACD,CAAC,CACF,CACF,CACF,CACF,CACF,CACF,CACF,GACDtI,GAAG,CAAC8D,EAAE,EAAE,EACZ7D,EAAE,CACA,KAAK,EACL;MACEG,WAAW,EAAE,YAAY;MACzBC,KAAK,EAAE,mBACM2B,IAAI,CAACX,MAAM,CAACsE,OAAO,oBACpB3D,IAAI,CAACX,MAAM,CAACuE,MAAM,EAC7B;MACDlF,KAAK,EAAE;QACL+C,YAAY,aAAMzB,IAAI,CAACtB,KAAK,CAACyG,OAAO,CAACzD,UAAU,OAAI;QACnDlB,OAAO,YAAKR,IAAI,CAACtB,KAAK,CAACyG,OAAO,CAACoB,YAAY;MAC7C;IACF,CAAC,EACDvI,GAAG,CAAC+B,EAAE,CACJC,IAAI,CAACX,MAAM,CAAC8C,MAAM,IAAI,QAAQ,IAC5BnC,IAAI,CAACxB,IAAI,CAACqD,MAAM,GACd7B,IAAI,CAACxB,IAAI,GACTwB,IAAI,CAACoC,WAAW,EACpB,UAAUZ,OAAO,EAAEX,OAAO,EAAE;MAC1B,OAAO5C,EAAE,CACP,KAAK,EACL;QACEiC,GAAG,YAAKD,KAAK,cAAIY,OAAO,CAAE;QAC1BzC,WAAW,EAAE,YAAY;QACzBM,KAAK,EAAE;UACL+C,YAAY,YAAKzB,IAAI,CAACtB,KAAK,CAACyG,OAAO,CAACzD,UAAU;QAChD;MACF,CAAC,EACD,CACE1B,IAAI,CAACX,MAAM,CAACuE,MAAM,IAAI,CAAC,GACnB,CACE3F,EAAE,CAAC,KAAK,EAAE;QAAEG,WAAW,EAAE;MAAO,CAAC,EAAE,CACjCH,EAAE,CACA,KAAK,EACL;QACEG,WAAW,EACT;MACJ,CAAC,EACD,CACEH,EAAE,CAAC,KAAK,EAAE;QACRG,WAAW,EAAE,OAAO;QACpBM,KAAK,EAAE;UACLyC,YAAY,YAAKnB,IAAI,CAACtB,KAAK,CAACyG,OAAO,CAAChE,YAAY;QAClD,CAAC;QACD3B,KAAK,EAAE;UACL4B,GAAG,EAAEI,OAAO,CAACsC;QACf;MACF,CAAC,CAAC,CACH,CACF,EACD7F,EAAE,CACA,KAAK,EACL;QACEG,WAAW,EACT;MACJ,CAAC,EACD,CACEH,EAAE,CACA,KAAK,EACL;QACEG,WAAW,EAAE;MACf,CAAC,EACD,CACEJ,GAAG,CAACoC,OAAO,CACT,WAAW,EACXJ,IAAI,CAACX,MAAM,CAAC0E,IAAI,CACjB,GACG9F,EAAE,CACA,KAAK,EACL;QACEG,WAAW,EACT,YAAY;QACdC,KAAK,EAAE,CACL2B,IAAI,CAACtB,KAAK,CACPyG,OAAO,CACPnB,aAAa,IAChB,KAAK,GACD,cAAc,GACd,cAAc,gBACXhE,IAAI,CAACtB,KAAK,CAACsF,aAAa;MAEnC,CAAC,EACD,CACEhG,GAAG,CAACmB,EAAE,CACJnB,GAAG,CAACoB,EAAE,CACJoC,OAAO,CAACyC,UAAU,CACnB,CACF,CACF,CACF,GACDjG,GAAG,CAAC8D,EAAE,EAAE,EACZ7D,EAAE,CACA,KAAK,EACL;QACEG,WAAW,EACT;MACJ,CAAC,EACD,CACEJ,GAAG,CAACoC,OAAO,CACT,QAAQ,EACRJ,IAAI,CAACX,MAAM,CAAC0E,IAAI,CACjB,GACG9F,EAAE,CACA,KAAK,EACL;QACEG,WAAW,EACT,iBAAiB;QACnBM,KAAK,EAAE;UACLE,eAAe,EACboB,IAAI,CAACtB,KAAK,CACPyG,OAAO,CACPiC;QACP;MACF,CAAC,EACD,CACEnJ,EAAE,CACA,KAAK,EACL;QACEG,WAAW,EACT;MACJ,CAAC,EACD,CACEH,EAAE,CACA,MAAM,EACN,CACED,GAAG,CAACmB,EAAE,CACJnB,GAAG,CAACoB,EAAE,CACJoC,OAAO,CAAC6F,WAAW,CACpB,GACC,IAAI,CACP,CACF,CACF,CACF,CACF,CACF,CACF,GACDrJ,GAAG,CAAC8D,EAAE,EAAE,EACZ9D,GAAG,CAACoC,OAAO,CACT,aAAa,EACbJ,IAAI,CAACX,MAAM,CAAC0E,IAAI,CACjB,GACG9F,EAAE,CACA,KAAK,EACL;QACEG,WAAW,EACT,iBAAiB;QACnBM,KAAK,EAAE;UACLE,eAAe,EACboB,IAAI,CAACtB,KAAK,CACPyG,OAAO,CACPiC,QAAQ;UACblI,KAAK,EACHc,IAAI,CAACtB,KAAK,CACPyG,OAAO,CACPiC;QACP;MACF,CAAC,EACD,CACEnJ,EAAE,CACA,KAAK,EACL;QACEG,WAAW,EACT;MACJ,CAAC,EACD,CACEH,EAAE,CACA,MAAM,EACN,CACED,GAAG,CAACmB,EAAE,CACJ,IAAI,GACFnB,GAAG,CAACoB,EAAE,CACJoC,OAAO,CAAC8F,YAAY,CACrB,GACD,GAAG,CACN,CACF,CACF,CACF,CACF,CACF,CACF,GACDtJ,GAAG,CAAC8D,EAAE,EAAE,CACb,CACF,EACD7D,EAAE,CACA,KAAK,EACL;QACEG,WAAW,EAAE;MACf,CAAC,EACD,CACEH,EAAE,CACA,KAAK,EACL;QACEG,WAAW,EACT,aAAa;QACfM,KAAK,EAAE;UACLQ,KAAK,EACHc,IAAI,CAACtB,KAAK,CACPyG,OAAO,CACPd;QACP;MACF,CAAC,EACD,CACEpG,EAAE,CACA,KAAK,EACL;QACEG,WAAW,EACT;MACJ,CAAC,EACD,CACEJ,GAAG,CAACoC,OAAO,CACT,cAAc,EACdJ,IAAI,CAACX,MAAM,CACR0E,IAAI,CACR,GACG,CACE9F,EAAE,CACA,MAAM,EACN;QACEG,WAAW,EACT;MACJ,CAAC,EACD,CACEJ,GAAG,CAACmB,EAAE,CACJ,KAAK,CACN,CACF,CACF,EACDlB,EAAE,CACA,MAAM,EACN;QACEG,WAAW,EACT;MACJ,CAAC,EACD,CACEJ,GAAG,CAACmB,EAAE,CACJ,GAAG,CACJ,CACF,CACF,EACDlB,EAAE,CACA,MAAM,EACN;QACEG,WAAW,EACT;MACJ,CAAC,EACD,CACEJ,GAAG,CAACmB,EAAE,CACJnB,GAAG,CAACoB,EAAE,CACJoC,OAAO,CAAC+F,aAAa,CACtB,CACF,CACF,CACF,CACF,GACDvJ,GAAG,CAAC8D,EAAE,EAAE,CACb,EACD,CAAC,CACF,EACD7D,EAAE,CACA,KAAK,EACL;QACEG,WAAW,EACT;MACJ,CAAC,EACD,CACEJ,GAAG,CAACoC,OAAO,CACT,eAAe,EACfJ,IAAI,CAACX,MAAM,CACR0E,IAAI,CACR,GACG9F,EAAE,CACA,MAAM,EACN;QACEG,WAAW,EACT;MACJ,CAAC,EACD,CACEH,EAAE,CACA,MAAM,EACN;QACEG,WAAW,EACT;MACJ,CAAC,EACD,CACEJ,GAAG,CAACmB,EAAE,CACJ,GAAG,CACJ,CACF,CACF,EACDlB,EAAE,CACA,MAAM,EACN;QACEG,WAAW,EACT;MACJ,CAAC,EACD,CACEJ,GAAG,CAACmB,EAAE,CACJnB,GAAG,CAACoB,EAAE,CACJoC,OAAO,CAACwF,cAAc,CACvB,CACF,CACF,CACF,CACF,CACF,GACDhJ,GAAG,CAAC8D,EAAE,EAAE,CACb,CACF,CACF,CACF,EACD7D,EAAE,CACA,KAAK,EACL;QACE6C,UAAU,EAAE,CACV;UACEC,IAAI,EAAE,MAAM;UACZC,OAAO,EACL,QAAQ;UACVC,KAAK,EACHjD,GAAG,CAACoC,OAAO,CACT,SAAS,EACTJ,IAAI,CACDX,MAAM,CACN0E,IAAI,CACR,IACD/D,IAAI,CAACX,MAAM,CACRuE,MAAM,GACP,CAAC;UACL1C,UAAU,EACR;QACJ,CAAC,CACF;QACD9C,WAAW,EACT;MACJ,CAAC,EACD,CACEH,EAAE,CACA,KAAK,EACL;QACEG,WAAW,EACT,UAAU;QACZM,KAAK,EAAE;UACLQ,KAAK,EACHc,IAAI,CAACtB,KAAK,CACPyG,OAAO,CACP8B,gBAAgB;UACrBtG,UAAU,sCAA+BX,IAAI,CAACtB,KAAK,CAACyG,OAAO,CAAC+B,cAAc;QAC5E;MACF,CAAC,EACD,CACEjJ,EAAE,CAAC,MAAM,EAAE,CACTD,GAAG,CAACmB,EAAE,CACJnB,GAAG,CAACoB,EAAE,CACJY,IAAI,CACDX,MAAM,CACN8H,WAAW,CACf,CACF,CACF,CAAC,CACH,CACF,CACF,CACF,CACF,CACF,CACF,CACF,CACF,CACF,CACF,CAAC,CACH,GACD,CACElJ,EAAE,CACA,KAAK,EACL;QACEG,WAAW,EAAE,aAAa;QAC1BM,KAAK,EAAE;UACLyC,YAAY,YAAKnB,IAAI,CAACtB,KAAK,CAACyG,OAAO,CAAChE,YAAY;QAClD;MACF,CAAC,EACD,CACElD,EAAE,CAAC,KAAK,EAAE;QACRG,WAAW,EAAE,OAAO;QACpBoB,KAAK,EAAE;UACL4B,GAAG,EAAEI,OAAO,CAACsC;QACf;MACF,CAAC,CAAC,CACH,CACF,EACD7F,EAAE,CACA,KAAK,EACL;QAAEG,WAAW,EAAE;MAAa,CAAC,EAC7B,CACEJ,GAAG,CAACoC,OAAO,CACT,WAAW,EACXJ,IAAI,CAACX,MAAM,CAAC0E,IAAI,CACjB,GACG9F,EAAE,CACA,KAAK,EACL;QACEG,WAAW,EACT,YAAY;QACdC,KAAK,EAAE,CACL2B,IAAI,CAACtB,KAAK,CAACyG,OAAO,CACfnB,aAAa,IAChB,KAAK,GACD,cAAc,GACd,cAAc,gBACXhE,IAAI,CAACtB,KAAK,CAACsF,aAAa;MAEnC,CAAC,EACD,CACEhG,GAAG,CAACmB,EAAE,CACJnB,GAAG,CAACoB,EAAE,CACJoC,OAAO,CAACyC,UAAU,CACnB,CACF,CACF,CACF,GACDjG,GAAG,CAAC8D,EAAE,EAAE,EACZ7D,EAAE,CACA,KAAK,EACL;QAAEG,WAAW,EAAE;MAAS,CAAC,EACzB,CACEJ,GAAG,CAACoC,OAAO,CACT,cAAc,EACdJ,IAAI,CAACX,MAAM,CAAC0E,IAAI,CACjB,GACG9F,EAAE,CACA,KAAK,EACL;QACEG,WAAW,EACT,0BAA0B;QAC5BM,KAAK,EAAE;UACLQ,KAAK,EACHc,IAAI,CAACtB,KAAK,CACPyG,OAAO,CACPd;QACP;MACF,CAAC,EACD,CACEpG,EAAE,CACA,MAAM,EACN;QACEG,WAAW,EACT;MACJ,CAAC,EACD,CAACJ,GAAG,CAACmB,EAAE,CAAC,GAAG,CAAC,CAAC,CACd,EACDlB,EAAE,CACA,MAAM,EACN;QACEG,WAAW,EACT;MACJ,CAAC,EACD,CACEJ,GAAG,CAACmB,EAAE,CACJnB,GAAG,CAACoB,EAAE,CACJoC,OAAO,CAAC+F,aAAa,CACtB,CACF,CACF,CACF,EACDvJ,GAAG,CAACoC,OAAO,CACT,eAAe,EACfJ,IAAI,CAACX,MAAM,CAAC0E,IAAI,CACjB,GACG9F,EAAE,CACA,MAAM,EACN;QACEG,WAAW,EACT;MACJ,CAAC,EACD,CACEH,EAAE,CACA,MAAM,EACN;QACEG,WAAW,EACT;MACJ,CAAC,EACD,CACEJ,GAAG,CAACmB,EAAE,CACJ,GAAG,CACJ,CACF,CACF,EACDlB,EAAE,CACA,MAAM,EACN;QACEG,WAAW,EACT;MACJ,CAAC,EACD,CACEJ,GAAG,CAACmB,EAAE,CACJnB,GAAG,CAACoB,EAAE,CACJoC,OAAO,CAACwF,cAAc,CACvB,CACF,CACF,CACF,CACF,CACF,GACDhJ,GAAG,CAAC8D,EAAE,EAAE,CACb,CACF,GACD9D,GAAG,CAAC8D,EAAE,EAAE,CACb,CACF,CACF,CACF,CACF,CACN,EACD,CAAC,CACF;IACH,CAAC,CACF,EACD,CAAC,CACF,CACF,CACF,CACF,CACF,GACD9B,IAAI,CAACK,IAAI,IAAI,OAAO,GACpBpC,EAAE,CACA,KAAK,EACL;MACEG,WAAW,EAAE,WAAW;MACxBM,KAAK,EAAE;QACLiC,UAAU,EAAEX,IAAI,CAACtB,KAAK,CAACiC,UAAU;QACjCH,OAAO,YAAKR,IAAI,CAACtB,KAAK,CAACgE,QAAQ,gBAAM1C,IAAI,CAACtB,KAAK,CAACiE,QAAQ;MAC1D;IACF,CAAC,EACD,CACE1E,EAAE,CACA,KAAK,EACL;MACEG,WAAW,EAAE,WAAW;MACxBM,KAAK,EAAE;QACLyC,YAAY,YAAKnB,IAAI,CAACtB,KAAK,CAACyC,YAAY;MAC1C;IACF,CAAC,EACD,CACEnB,IAAI,CAACX,MAAM,CAACC,KAAK,CAACwG,MAAM,GACpB7H,EAAE,CACA,KAAK,EACL;MACEG,WAAW,EAAE,WAAW;MACxBM,KAAK,EAAE;QACLqH,eAAe,gBAAS/F,IAAI,CAACX,MAAM,CAACC,KAAK,CAAC0G,OAAO;MACnD;IACF,CAAC,EACD,CACE/H,EAAE,CACA,KAAK,EACL;MAAEG,WAAW,EAAE;IAAkB,CAAC,EAClC,CACEH,EAAE,CACA,KAAK,EACL;MAAEG,WAAW,EAAE;IAAc,CAAC,EAC9B,CACEH,EAAE,CAAC,KAAK,EAAE;MACRG,WAAW,EAAE,OAAO;MACpBoB,KAAK,EAAE;QACL4B,GAAG,EAAEpB,IAAI,CAACX,MAAM,CAACC,KAAK,CAACiD,KAAK;QAC5BC,GAAG,EAAE;MACP;IACF,CAAC,CAAC,CACH,CACF,EACDxC,IAAI,CAACX,MAAM,CAACC,KAAK,CAACkI,aAAa,GAC3B,CACEvJ,EAAE,CACA,KAAK,EACL;MACEG,WAAW,EACT,qBAAqB;MACvBM,KAAK,EAAE;QACLQ,KAAK,EACHc,IAAI,CAACtB,KAAK,CAACY,KAAK,CACbmI;MACP;IACF,CAAC,EACD,CACExJ,EAAE,CAAC,MAAM,EAAE,CACTD,GAAG,CAACmB,EAAE,CACJnB,GAAG,CAACoB,EAAE,CACJY,IAAI,CAACX,MAAM,CAACC,KAAK,CACdoI,aAAa,CACjB,CACF,CACF,CAAC,CACH,CACF,EACDzJ,EAAE,CACA,KAAK,EACL;MACEG,WAAW,EACT;IACJ,CAAC,EACD,CACEH,EAAE,CACA,KAAK,EACL;MACEG,WAAW,EAAE;IACf,CAAC,EACD,CACEH,EAAE,CACA,KAAK,EACL;MACEG,WAAW,EACT,YAAY;MACdM,KAAK,EAAE;QACLE,eAAe,EACboB,IAAI,CAACtB,KAAK,CAACY,KAAK,CACbqI,YAAY;QACjBzI,KAAK,EACHc,IAAI,CAACtB,KAAK,CAACY,KAAK,CACbsI;MACP;IACF,CAAC,EACD,CACE3J,EAAE,CAAC,MAAM,EAAE,CACTD,GAAG,CAACmB,EAAE,CAAC,IAAI,CAAC,CACb,CAAC,CACH,CACF,EACDlB,EAAE,CACA,KAAK,EACL;MACEG,WAAW,EACT,cAAc;MAChBM,KAAK,EAAE;QACLQ,KAAK,EACHc,IAAI,CAACtB,KAAK,CAACY,KAAK,CACbmI;MACP;IACF,CAAC,EACD,CACExJ,EAAE,CAAC,MAAM,EAAE,CACTD,GAAG,CAACmB,EAAE,CAAC,GAAG,CAAC,CACZ,CAAC,CACH,CACF,EACDlB,EAAE,CACA,KAAK,EACL;MACEG,WAAW,EACT,YAAY;MACdM,KAAK,EAAE;QACLE,eAAe,EACboB,IAAI,CAACtB,KAAK,CAACY,KAAK,CACbqI,YAAY;QACjBzI,KAAK,EACHc,IAAI,CAACtB,KAAK,CAACY,KAAK,CACbsI;MACP;IACF,CAAC,EACD,CACE3J,EAAE,CAAC,MAAM,EAAE,CACTD,GAAG,CAACmB,EAAE,CAAC,IAAI,CAAC,CACb,CAAC,CACH,CACF,EACDlB,EAAE,CACA,KAAK,EACL;MACEG,WAAW,EACT,cAAc;MAChBM,KAAK,EAAE;QACLQ,KAAK,EACHc,IAAI,CAACtB,KAAK,CAACY,KAAK,CACbmI;MACP;IACF,CAAC,EACD,CACExJ,EAAE,CAAC,MAAM,EAAE,CACTD,GAAG,CAACmB,EAAE,CAAC,GAAG,CAAC,CACZ,CAAC,CACH,CACF,EACDlB,EAAE,CACA,KAAK,EACL;MACEG,WAAW,EACT,YAAY;MACdM,KAAK,EAAE;QACLE,eAAe,EACboB,IAAI,CAACtB,KAAK,CAACY,KAAK,CACbqI,YAAY;QACjBzI,KAAK,EACHc,IAAI,CAACtB,KAAK,CAACY,KAAK,CACbsI;MACP;IACF,CAAC,EACD,CACE3J,EAAE,CAAC,MAAM,EAAE,CACTD,GAAG,CAACmB,EAAE,CAAC,IAAI,CAAC,CACb,CAAC,CACH,CACF,CACF,CACF,CACF,CACF,CACF,GACDnB,GAAG,CAAC8D,EAAE,EAAE,CACb,EACD,CAAC,CACF,EACD7D,EAAE,CACA,KAAK,EACL;MAAEG,WAAW,EAAE;IAAmB,CAAC,EACnC,CACEH,EAAE,CACA,KAAK,EACL;MACEG,WAAW,EAAE,YAAY;MACzBM,KAAK,EAAE;QACLQ,KAAK,EACHc,IAAI,CAACtB,KAAK,CAACY,KAAK,CAAC6G;MACrB;IACF,CAAC,EACD,CACElI,EAAE,CACA,MAAM,EACN;MAAEG,WAAW,EAAE;IAAY,CAAC,EAC5B,CACEJ,GAAG,CAACmB,EAAE,CACJnB,GAAG,CAACoB,EAAE,CACJY,IAAI,CAACX,MAAM,CAACC,KAAK,CAAC8G,SAAS,CAC5B,CACF,CACF,CACF,EACDnI,EAAE,CACA,MAAM,EACN;MAAEG,WAAW,EAAE;IAAa,CAAC,EAC7B,CACEH,EAAE,CAAC,QAAQ,EAAE;MACXuB,KAAK,EAAE;QACLwD,SAAS,EACPhF,GAAG,CAACqI,IAAI,CAACC;MACb;IACF,CAAC,CAAC,CACH,EACD,CAAC,CACF,CACF,CACF,CACF,CACF,CACF,CACF,GACDtI,GAAG,CAAC8D,EAAE,EAAE,EACZ7D,EAAE,CACA,KAAK,EACL;MACEG,WAAW,EAAE,YAAY;MACzBC,KAAK,EAAE,mBACM2B,IAAI,CAACX,MAAM,CAACsE,OAAO,oBACpB3D,IAAI,CAACX,MAAM,CAACuE,MAAM,EAC7B;MACDlF,KAAK,EAAE;QACL+C,YAAY,aAAMzB,IAAI,CAACtB,KAAK,CAACyG,OAAO,CAACzD,UAAU,OAAI;QACnDlB,OAAO,YAAKR,IAAI,CAACtB,KAAK,CAACyG,OAAO,CAACoB,YAAY;MAC7C;IACF,CAAC,EACDvI,GAAG,CAAC+B,EAAE,CAACC,IAAI,CAACxB,IAAI,EAAE,UAAUgD,OAAO,EAAEX,OAAO,EAAE;MAC5C,OAAO5C,EAAE,CACP,KAAK,EACL;QACEiC,GAAG,YAAKD,KAAK,cAAIY,OAAO,CAAE;QAC1BzC,WAAW,EAAE,YAAY;QACzBM,KAAK,EAAE;UACL+C,YAAY,YAAKzB,IAAI,CAACtB,KAAK,CAACyG,OAAO,CAACzD,UAAU;QAChD;MACF,CAAC,EACD,CACE1B,IAAI,CAACX,MAAM,CAACuE,MAAM,IAAI,CAAC,GACnB,CACE3F,EAAE,CAAC,KAAK,EAAE;QAAEG,WAAW,EAAE;MAAO,CAAC,EAAE,CACjCH,EAAE,CACA,KAAK,EACL;QACEG,WAAW,EAAE;MACf,CAAC,EACD,CACEH,EAAE,CAAC,KAAK,EAAE;QACRG,WAAW,EAAE,OAAO;QACpBM,KAAK,EAAE;UACLyC,YAAY,YAAKnB,IAAI,CAACtB,KAAK,CAACyG,OAAO,CAAChE,YAAY;QAClD,CAAC;QACD3B,KAAK,EAAE;UACL4B,GAAG,EAAEI,OAAO,CAACsC;QACf;MACF,CAAC,CAAC,CACH,CACF,EACD7F,EAAE,CACA,KAAK,EACL;QACEG,WAAW,EAAE;MACf,CAAC,EACD,CACEH,EAAE,CACA,KAAK,EACL;QAAEG,WAAW,EAAE;MAAa,CAAC,EAC7B,CACEJ,GAAG,CAACoC,OAAO,CACT,WAAW,EACXJ,IAAI,CAACX,MAAM,CAAC0E,IAAI,CACjB,GACG9F,EAAE,CACA,KAAK,EACL;QACEG,WAAW,EACT,YAAY;QACdC,KAAK,EAAE,CACL2B,IAAI,CAACtB,KAAK,CAACyG,OAAO,CACfnB,aAAa,IAChB,KAAK,GACD,cAAc,GACd,cAAc,gBACXhE,IAAI,CAACtB,KAAK,CAACsF,aAAa;MAEnC,CAAC,EACD,CACEhG,GAAG,CAACmB,EAAE,CACJnB,GAAG,CAACoB,EAAE,CACJoC,OAAO,CAACyC,UAAU,CACnB,CACF,CACF,CACF,GACDjG,GAAG,CAAC8D,EAAE,EAAE,EACZ9D,GAAG,CAACoC,OAAO,CACT,UAAU,EACVJ,IAAI,CAACX,MAAM,CAAC0E,IAAI,CACjB,GACG9F,EAAE,CACA,KAAK,EACL;QACEG,WAAW,EACT;MACJ,CAAC,EACD,CACEH,EAAE,CACA,KAAK,EACL;QACEG,WAAW,EACT,cAAc;QAChBM,KAAK,EAAE;UACLiC,UAAU,sCAA+BX,IAAI,CAACtB,KAAK,CAACyG,OAAO,CAAC0C,aAAa;QAC3E;MACF,CAAC,EACD,CACE5J,EAAE,CACA,KAAK,EACL;QACEG,WAAW,EACT;MACJ,CAAC,EACD,CACEH,EAAE,CAAC,KAAK,EAAE;QACRG,WAAW,EACT,uBAAuB;QACzBM,KAAK,EAAE;UACL+E,KAAK,YAAKjC,OAAO,CAACsG,QAAQ,MAAG;UAC7BnH,UAAU,sCAA+BX,IAAI,CAACtB,KAAK,CAACyG,OAAO,CAAC0C,aAAa;QAC3E;MACF,CAAC,CAAC,EACF5J,EAAE,CACA,MAAM,EACN;QACEG,WAAW,EACT;MACJ,CAAC,EACD,CACEJ,GAAG,CAACmB,EAAE,CACJnB,GAAG,CAACoB,EAAE,CACJoC,OAAO,CAACsG,QAAQ,CACjB,GACC,GAAG,CACN,CACF,CACF,CACF,CACF,CACF,CACF,EACD7J,EAAE,CACA,KAAK,EACL;QACEG,WAAW,EACT,0BAA0B;QAC5BM,KAAK,EAAE;UACLQ,KAAK,EACHc,IAAI,CAACtB,KAAK,CACPyG,OAAO,CACP4C;QACP;MACF,CAAC,EACD,CACE/J,GAAG,CAACmB,EAAE,CACJ,IAAI,GACFnB,GAAG,CAACoB,EAAE,CACJoC,OAAO,CAACwG,YAAY,CACrB,GACD,GAAG,CACN,CACF,CACF,CACF,CACF,GACDhK,GAAG,CAAC8D,EAAE,EAAE,EACZ7D,EAAE,CACA,KAAK,EACL;QAAEG,WAAW,EAAE;MAAS,CAAC,EACzB,CACEH,EAAE,CACA,KAAK,EACL;QACEG,WAAW,EACT,aAAa;QACfM,KAAK,EAAE;UACLQ,KAAK,EACHc,IAAI,CAACtB,KAAK,CACPyG,OAAO,CACPd;QACP;MACF,CAAC,EACD,CACEpG,EAAE,CACA,KAAK,EACL;QACEG,WAAW,EACT;MACJ,CAAC,EACD,CACEJ,GAAG,CAACoC,OAAO,CACT,cAAc,EACdJ,IAAI,CAACX,MAAM,CACR0E,IAAI,CACR,GACG,CACE9F,EAAE,CACA,MAAM,EACN;QACEG,WAAW,EACT;MACJ,CAAC,EACD,CACEJ,GAAG,CAACmB,EAAE,CACJ,KAAK,CACN,CACF,CACF,EACDlB,EAAE,CACA,MAAM,EACN;QACEG,WAAW,EACT;MACJ,CAAC,EACD,CACEJ,GAAG,CAACmB,EAAE,CACJ,GAAG,CACJ,CACF,CACF,EACDlB,EAAE,CACA,MAAM,EACN;QACEG,WAAW,EACT;MACJ,CAAC,EACD,CACEJ,GAAG,CAACmB,EAAE,CACJnB,GAAG,CAACoB,EAAE,CACJoC,OAAO,CAACyG,iBAAiB,CAC1B,CACF,CACF,CACF,CACF,GACDjK,GAAG,CAAC8D,EAAE,EAAE,CACb,EACD,CAAC,CACF,EACD7D,EAAE,CACA,KAAK,EACL;QACEG,WAAW,EACT;MACJ,CAAC,EACD,CACEJ,GAAG,CAACoC,OAAO,CACT,eAAe,EACfJ,IAAI,CAACX,MAAM,CACR0E,IAAI,CACR,GACG9F,EAAE,CACA,MAAM,EACN;QACEG,WAAW,EACT;MACJ,CAAC,EACD,CACEH,EAAE,CACA,MAAM,EACN;QACEG,WAAW,EACT;MACJ,CAAC,EACD,CACEJ,GAAG,CAACmB,EAAE,CACJ,GAAG,CACJ,CACF,CACF,EACDlB,EAAE,CACA,MAAM,EACN;QACEG,WAAW,EACT;MACJ,CAAC,EACD,CACEJ,GAAG,CAACmB,EAAE,CACJnB,GAAG,CAACoB,EAAE,CACJoC,OAAO,CAACwF,cAAc,CACvB,CACF,CACF,CACF,CACF,CACF,GACDhJ,GAAG,CAAC8D,EAAE,EAAE,CACb,CACF,CACF,CACF,EACD7D,EAAE,CACA,KAAK,EACL;QACE6C,UAAU,EAAE,CACV;UACEC,IAAI,EAAE,MAAM;UACZC,OAAO,EACL,QAAQ;UACVC,KAAK,EACHjD,GAAG,CAACoC,OAAO,CACT,SAAS,EACTJ,IAAI,CAACX,MAAM,CACR0E,IAAI,CACR,IACD/D,IAAI,CAACX,MAAM,CACRuE,MAAM,GAAG,CAAC;UACf1C,UAAU,EACR;QACJ,CAAC,CACF;QACD9C,WAAW,EACT;MACJ,CAAC,EACD,CACEH,EAAE,CACA,KAAK,EACL;QACEG,WAAW,EACT,UAAU;QACZM,KAAK,EAAE;UACLQ,KAAK,EACHc,IAAI,CAACtB,KAAK,CACPyG,OAAO,CACP8B,gBAAgB;UACrBtG,UAAU,sCAA+BX,IAAI,CAACtB,KAAK,CAACyG,OAAO,CAAC+B,cAAc;QAC5E;MACF,CAAC,EACD,CACEjJ,EAAE,CAAC,MAAM,EAAE,CACTD,GAAG,CAACmB,EAAE,CACJnB,GAAG,CAACoB,EAAE,CACJY,IAAI,CACDX,MAAM,CACN8H,WAAW,CACf,CACF,CACF,CAAC,CACH,CACF,CACF,CACF,CACF,CACF,CACF,CACF,CACF,CACF,CACF,CAAC,CACH,GACD,CACElJ,EAAE,CACA,KAAK,EACL;QACEG,WAAW,EAAE,aAAa;QAC1BM,KAAK,EAAE;UACLyC,YAAY,YAAKnB,IAAI,CAACtB,KAAK,CAACyG,OAAO,CAAChE,YAAY;QAClD;MACF,CAAC,EACD,CACElD,EAAE,CAAC,KAAK,EAAE;QACRG,WAAW,EAAE,OAAO;QACpBoB,KAAK,EAAE;UACL4B,GAAG,EAAEI,OAAO,CAACsC;QACf;MACF,CAAC,CAAC,CACH,CACF,EACD7F,EAAE,CACA,KAAK,EACL;QAAEG,WAAW,EAAE;MAAa,CAAC,EAC7B,CACEJ,GAAG,CAACoC,OAAO,CACT,WAAW,EACXJ,IAAI,CAACX,MAAM,CAAC0E,IAAI,CACjB,GACG9F,EAAE,CACA,KAAK,EACL;QACEG,WAAW,EAAE,YAAY;QACzBC,KAAK,EAAE,CACL2B,IAAI,CAACtB,KAAK,CAACyG,OAAO,CACfnB,aAAa,IAChB,KAAK,GACD,cAAc,GACd,cAAc,gBACXhE,IAAI,CAACtB,KAAK,CAACsF,aAAa;MAEnC,CAAC,EACD,CACEhG,GAAG,CAACmB,EAAE,CACJnB,GAAG,CAACoB,EAAE,CACJoC,OAAO,CAACyC,UAAU,CACnB,CACF,CACF,CACF,GACDjG,GAAG,CAAC8D,EAAE,EAAE,EACZ7D,EAAE,CACA,KAAK,EACL;QAAEG,WAAW,EAAE;MAAS,CAAC,EACzB,CACEJ,GAAG,CAACoC,OAAO,CACT,cAAc,EACdJ,IAAI,CAACX,MAAM,CAAC0E,IAAI,CACjB,GACG9F,EAAE,CACA,KAAK,EACL;QACEG,WAAW,EACT,0BAA0B;QAC5BM,KAAK,EAAE;UACLQ,KAAK,EACHc,IAAI,CAACtB,KAAK,CAACyG,OAAO,CACfd;QACP;MACF,CAAC,EACD,CACEpG,EAAE,CACA,MAAM,EACN;QACEG,WAAW,EACT;MACJ,CAAC,EACD,CAACJ,GAAG,CAACmB,EAAE,CAAC,GAAG,CAAC,CAAC,CACd,EACDlB,EAAE,CACA,MAAM,EACN;QACEG,WAAW,EACT;MACJ,CAAC,EACD,CACEJ,GAAG,CAACmB,EAAE,CACJnB,GAAG,CAACoB,EAAE,CACJoC,OAAO,CAACyG,iBAAiB,CAC1B,CACF,CACF,CACF,EACDjK,GAAG,CAACoC,OAAO,CACT,eAAe,EACfJ,IAAI,CAACX,MAAM,CAAC0E,IAAI,CACjB,GACG9F,EAAE,CACA,MAAM,EACN;QACEG,WAAW,EACT;MACJ,CAAC,EACD,CACEH,EAAE,CACA,MAAM,EACN;QACEG,WAAW,EACT;MACJ,CAAC,EACD,CACEJ,GAAG,CAACmB,EAAE,CACJ,GAAG,CACJ,CACF,CACF,EACDlB,EAAE,CACA,MAAM,EACN;QACEG,WAAW,EACT;MACJ,CAAC,EACD,CACEJ,GAAG,CAACmB,EAAE,CACJnB,GAAG,CAACoB,EAAE,CACJoC,OAAO,CAACwF,cAAc,CACvB,CACF,CACF,CACF,CACF,CACF,GACDhJ,GAAG,CAAC8D,EAAE,EAAE,CACb,CACF,GACD9D,GAAG,CAAC8D,EAAE,EAAE,CACb,CACF,CACF,CACF,CACF,CACN,EACD,CAAC,CACF;IACH,CAAC,CAAC,EACF,CAAC,CACF,CACF,CACF,CACF,CACF,GACD9B,IAAI,CAACK,IAAI,IAAI,QAAQ,GACrBpC,EAAE,CACA,KAAK,EACL;MACEG,WAAW,EAAE,YAAY;MACzBM,KAAK,EAAE;QACL8B,OAAO,YAAKR,IAAI,CAACtB,KAAK,CAAC+B,UAAU,SAAM;QACvCE,UAAU,EAAEX,IAAI,CAACtB,KAAK,CAACiC;MACzB;IACF,CAAC,EACD,CACE1C,EAAE,CACA,KAAK,EACL;MAAEG,WAAW,EAAE;IAAiB,CAAC,EACjCJ,GAAG,CAAC+B,EAAE,CACJC,IAAI,CAACX,MAAM,CAAC8C,MAAM,IAAI,QAAQ,IAAInC,IAAI,CAACxB,IAAI,CAACqD,MAAM,GAC9C7B,IAAI,CAACxB,IAAI,GACTwB,IAAI,CAACoC,WAAW,EACpB,UAAU8F,MAAM,EAAE5C,GAAG,EAAE;MACrB,OAAOrH,EAAE,CACP,KAAK,EACL;QACEiC,GAAG,EAAEoF,GAAG;QACRlH,WAAW,EAAE,aAAa;QAC1BM,KAAK,EAAE;UACLyJ,WAAW,YAAKnI,IAAI,CAACtB,KAAK,CAACyJ,WAAW;QACxC;MACF,CAAC,EACD,CACElK,EAAE,CAAC,GAAG,EAAE;QACNG,WAAW,EAAE,QAAQ;QACrBM,KAAK,EAAE;UACLiC,UAAU,EAAEX,IAAI,CAACtB,KAAK,CAACiC;QACzB;MACF,CAAC,CAAC,EACF1C,EAAE,CACA,KAAK,EACL;QACEG,WAAW,EAAE,cAAc;QAC3BM,KAAK,EAAE;UACLiC,UAAU,EAAEX,IAAI,CAACtB,KAAK,CAAC0J,aAAa;UACpClJ,KAAK,EAAEc,IAAI,CAACtB,KAAK,CAAC2J;QACpB;MACF,CAAC,EACD,CACEpK,EAAE,CACA,KAAK,EACL;QAAEG,WAAW,EAAE;MAAc,CAAC,EAC9B,CACE8J,MAAM,CAACI,WAAW,IAAI,EAAE,GACpB,CACErK,EAAE,CACA,MAAM,EACN;QAAEG,WAAW,EAAE;MAAO,CAAC,EACvB,CAACJ,GAAG,CAACmB,EAAE,CAAC,GAAG,CAAC,CAAC,CACd,EACDlB,EAAE,CACA,MAAM,EACN;QAAEG,WAAW,EAAE;MAAQ,CAAC,EACxB,CACEJ,GAAG,CAACmB,EAAE,CACJnB,GAAG,CAACoB,EAAE,CACJ8I,MAAM,CAACK,YAAY,CACpB,CACF,CACF,CACF,CACF,GACDvK,GAAG,CAAC8D,EAAE,EAAE,EACZoG,MAAM,CAACI,WAAW,IAAI,EAAE,GACpB,CACErK,EAAE,CACA,MAAM,EACN;QAAEG,WAAW,EAAE;MAAQ,CAAC,EACxB,CACEJ,GAAG,CAACmB,EAAE,CACJnB,GAAG,CAACoB,EAAE,CAAC8I,MAAM,CAACM,QAAQ,CAAC,GACrB,GAAG,CACN,CACF,CACF,CACF,GACDxK,GAAG,CAAC8D,EAAE,EAAE,CACb,EACD,CAAC,CACF,EACD7D,EAAE,CACA,KAAK,EACL;QAAEG,WAAW,EAAE;MAAiB,CAAC,EACjC,CACEH,EAAE,CAAC,MAAM,EAAE,CACTD,GAAG,CAACmB,EAAE,CACJ,GAAG,GACDnB,GAAG,CAACoB,EAAE,CAAC8I,MAAM,CAACO,SAAS,CAAC,GACxB,KAAK,CACR,CACF,CAAC,CACH,CACF,CACF,CACF,EACDxK,EAAE,CACA,KAAK,EACL;QACEG,WAAW,EAAE,eAAe;QAC5BM,KAAK,EAAE;UACLiC,UAAU,EAAEX,IAAI,CAACtB,KAAK,CAACgK,cAAc;UACrCxJ,KAAK,EAAEc,IAAI,CAACtB,KAAK,CAACiK;QACpB;MACF,CAAC,EACD,CACE1K,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACmB,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAC1BlB,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACmB,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAC3B,CACF,CACF,CACF;IACH,CAAC,CACF,EACD,CAAC,CACF,CACF,CACF,GACDa,IAAI,CAACK,IAAI,IAAI,SAAS,GACtBpC,EAAE,CACA,KAAK,EACL;MACEG,WAAW,EAAE,aAAa;MAC1BM,KAAK,EAAE;QACL8B,OAAO,YAAKR,IAAI,CAACtB,KAAK,CAAC+B,UAAU,SAAM;QACvCE,UAAU,EAAEX,IAAI,CAACtB,KAAK,CAACiC;MACzB;IACF,CAAC,EACD,CACE1C,EAAE,CAAC,KAAK,EAAE;MAAEG,WAAW,EAAE;IAAe,CAAC,EAAE,CACzCH,EAAE,CAAC,KAAK,EAAE;MACRG,WAAW,EAAE,OAAO;MACpBoB,KAAK,EAAE;QAAE4B,GAAG,EAAEpB,IAAI,CAACX,MAAM,CAACkD,KAAK;QAAEC,GAAG,EAAE;MAAG;IAC3C,CAAC,CAAC,CACH,CAAC,EACFvE,EAAE,CACA,KAAK,EACL;MACEG,WAAW,EAAE,iBAAiB;MAC9BC,KAAK,EAAE,mBAAY2B,IAAI,CAACX,MAAM,CAACsE,OAAO;IACxC,CAAC,EACD,CACE1F,EAAE,CACA,IAAI,EACJ;MAAEG,WAAW,EAAE;IAAuB,CAAC,EACvCJ,GAAG,CAAC+B,EAAE,CACJC,IAAI,CAACX,MAAM,CAAC8C,MAAM,IAAI,QAAQ,IAC5BnC,IAAI,CAACxB,IAAI,CAACqD,MAAM,GACd7B,IAAI,CAACxB,IAAI,GACTwB,IAAI,CAACoC,WAAW,EACpB,UAAUZ,OAAO,EAAE8D,GAAG,EAAE;MACtB,OAAOrH,EAAE,CACP,IAAI,EACJ;QACEiC,GAAG,EAAEoF,GAAG;QACRlH,WAAW,EAAE;MACf,CAAC,EACD,CACEH,EAAE,CACA,MAAM,EACN;QACES,KAAK,EAAE;UACLQ,KAAK,EAAEc,IAAI,CAACtB,KAAK,CAAC0E;QACpB;MACF,CAAC,EACD,CAACpF,GAAG,CAACmB,EAAE,CAACnB,GAAG,CAACoB,EAAE,CAACoC,OAAO,CAAClC,KAAK,CAAC,CAAC,CAAC,CAChC,CACF,CACF;IACH,CAAC,CACF,EACD,CAAC,CACF,CACF,CACF,EACDrB,EAAE,CACA,KAAK,EACL;MAAEG,WAAW,EAAE;IAAe,CAAC,EAC/B,CACEH,EAAE,CAAC,QAAQ,EAAE;MACXuB,KAAK,EAAE;QAAEwD,SAAS,EAAEhF,GAAG,CAACqI,IAAI,CAACC;MAAW;IAC1C,CAAC,CAAC,CACH,EACD,CAAC,CACF,CACF,CACF,GACDtG,IAAI,CAACK,IAAI,IAAI,YAAY,GACzBpC,EAAE,CACA,KAAK,EACL;MACEG,WAAW,EAAE,gBAAgB;MAC7BM,KAAK,EAAE;QACL8B,OAAO,YAAKR,IAAI,CAACtB,KAAK,CAAC+B,UAAU,gBAAMT,IAAI,CAACtB,KAAK,CAACgC,WAAW,OAAI;QACjEC,UAAU,EAAEX,IAAI,CAACtB,KAAK,CAACiC;MACzB;IACF,CAAC,EACD,CACE1C,EAAE,CACA,GAAG,EACH;MACEG,WAAW,EAAE,MAAM;MACnBM,KAAK,EAAE;QAAEmE,SAAS,EAAE7C,IAAI,CAACtB,KAAK,CAACmE;MAAU;IAC3C,CAAC,EACD,CACE5E,EAAE,CACA,GAAG,EACH;MACES,KAAK,EAAE;QACL4E,QAAQ,EAAEtD,IAAI,CAACtB,KAAK,CAAC4E,QAAQ,GAAG,IAAI;QACpCpE,KAAK,EAAEc,IAAI,CAACtB,KAAK,CAAC0E;MACpB,CAAC;MACD5D,KAAK,EAAE;QACLoJ,IAAI,EAAE5I,IAAI,CAACX,MAAM,CAACwJ,IAAI;QACtBC,MAAM,EAAE;MACV;IACF,CAAC,EACD,CAAC9K,GAAG,CAACmB,EAAE,CAACnB,GAAG,CAACoB,EAAE,CAACY,IAAI,CAACX,MAAM,CAACkE,IAAI,CAAC,CAAC,CAAC,CACnC,CACF,CACF,CACF,CACF,GACDvD,IAAI,CAACK,IAAI,IAAI,YAAY,GACzBpC,EAAE,CACA,KAAK,EACL;MACEG,WAAW,EAAE,gBAAgB;MAC7BM,KAAK,EAAE;QACLiC,UAAU,EAAEX,IAAI,CAACtB,KAAK,CAACiC,UAAU;QACjCH,OAAO,YAAKR,IAAI,CAACtB,KAAK,CAACgE,QAAQ,gBAAM1C,IAAI,CAACtB,KAAK,CAACiE,QAAQ;MAC1D;IACF,CAAC,EACD,CACE1E,EAAE,CACA,KAAK,EACL;MACEG,WAAW,EAAE,MAAM;MACnBM,KAAK,EAAE;QACL,yBAAyB,EACvBsB,IAAI,CAACtB,KAAK,CAACqK,kBAAkB;QAC/B,uBAAuB,EACrB/I,IAAI,CAACtB,KAAK,CAACsK,gBAAgB;QAC7BrI,UAAU,EAAEX,IAAI,CAACtB,KAAK,CAACiC;MACzB;IACF,CAAC,EACD3C,GAAG,CAAC+B,EAAE,CAACC,IAAI,CAACX,MAAM,CAAC4J,IAAI,EAAE,UAAUC,OAAO,EAAErI,OAAO,EAAE;MACnD,OAAO5C,EAAE,CACP,KAAK,EACL;QACEiC,GAAG,YAAKD,KAAK,cAAIY,OAAO,CAAE;QAC1BzC,WAAW,EAAE,UAAU;QACvBC,KAAK,EAAE;UAAE8K,MAAM,EAAEtI,OAAO,IAAI;QAAE;MAChC,CAAC,EACD,CACE5C,EAAE,CACA,KAAK,EACL;QACEG,WAAW,EAAE,UAAU;QACvBM,KAAK,EAAE;UAAEQ,KAAK,EAAEc,IAAI,CAACtB,KAAK,CAAC0K;QAAa;MAC1C,CAAC,EACD,CAACpL,GAAG,CAACmB,EAAE,CAACnB,GAAG,CAACoB,EAAE,CAAC8J,OAAO,CAACnI,IAAI,CAAC,CAAC,CAAC,CAC/B,EACD9C,EAAE,CAAC,KAAK,EAAE;QAAEG,WAAW,EAAE;MAAW,CAAC,EAAE,CACrCJ,GAAG,CAACmB,EAAE,CAACnB,GAAG,CAACoB,EAAE,CAAC8J,OAAO,CAACG,OAAO,CAAC,CAAC,CAChC,CAAC,CACH,CACF;IACH,CAAC,CAAC,EACF,CAAC,CACF,EACDpL,EAAE,CACA,KAAK,EACL;MACEG,WAAW,EAAE,YAAY;MACzBM,KAAK,EAAE;QACL+C,YAAY,aAAMzB,IAAI,CAACtB,KAAK,CAACgD,UAAU;MACzC;IACF,CAAC,EACD1D,GAAG,CAAC+B,EAAE,CAACC,IAAI,CAACoC,WAAW,EAAE,UAAUZ,OAAO,EAAEX,OAAO,EAAE;MACnD,OAAO5C,EAAE,CACP,KAAK,EACL;QACEiC,GAAG,YAAKD,KAAK,cAAIY,OAAO,CAAE;QAC1BzC,WAAW,EAAE,YAAY;QACzBC,KAAK,EAAE,mBAAY2B,IAAI,CAACtB,KAAK,CAACmF,QAAQ,EAAG;QACzCnF,KAAK,EAAE;UACL+C,YAAY,YAAKzB,IAAI,CAACtB,KAAK,CAACgD,UAAU,OAAI;UAC1CP,YAAY,YAAKnB,IAAI,CAACtB,KAAK,CAACyC,YAAY;QAC1C;MACF,CAAC,EACD,CACElD,EAAE,CAAC,KAAK,EAAE;QAAEG,WAAW,EAAE;MAAc,CAAC,EAAE,CACxCH,EAAE,CAAC,KAAK,EAAE;QACRG,WAAW,EAAE,OAAO;QACpBoB,KAAK,EAAE;UACL4B,GAAG,EAAEI,OAAO,CAACsC,WAAW;UACxBtB,GAAG,EAAE;QACP;MACF,CAAC,CAAC,CACH,CAAC,EACFvE,EAAE,CAAC,KAAK,EAAE;QAAEG,WAAW,EAAE;MAAa,CAAC,EAAE,CACvCJ,GAAG,CAACoC,OAAO,CAAC,WAAW,EAAEJ,IAAI,CAACtB,KAAK,CAACqF,IAAI,CAAC,GACrC9F,EAAE,CACA,KAAK,EACL;QACEG,WAAW,EAAE,YAAY;QACzBC,KAAK,EAAE,CACL2B,IAAI,CAACtB,KAAK,CAACsF,aAAa,IAAI,KAAK,GAC7B,cAAc,GACd,cAAc,gBACXhE,IAAI,CAACtB,KAAK,CAACsF,aAAa;MAEnC,CAAC,EACD,CAAChG,GAAG,CAACmB,EAAE,CAACnB,GAAG,CAACoB,EAAE,CAACoC,OAAO,CAACyC,UAAU,CAAC,CAAC,CAAC,CACrC,GACDjG,GAAG,CAAC8D,EAAE,EAAE,EACZ9D,GAAG,CAACoC,OAAO,CAAC,cAAc,EAAEJ,IAAI,CAACtB,KAAK,CAACqF,IAAI,CAAC,GACxC9F,EAAE,CACA,KAAK,EACL;QAAEG,WAAW,EAAE;MAAgB,CAAC,EAChC,CACEH,EAAE,CACA,MAAM,EACN;QACEG,WAAW,EACT,sBAAsB;QACxBM,KAAK,EAAE;UACLQ,KAAK,EAAEc,IAAI,CAACtB,KAAK,CAACwF;QACpB;MACF,CAAC,EACD,CACElG,GAAG,CAACmB,EAAE,CACJnB,GAAG,CAACoB,EAAE,CAACoC,OAAO,CAAC2C,aAAa,CAAC,CAC9B,CACF,CACF,CACF,CACF,GACDnG,GAAG,CAAC8D,EAAE,EAAE,EACZ9D,GAAG,CAACoC,OAAO,CAAC,YAAY,EAAEJ,IAAI,CAACtB,KAAK,CAACqF,IAAI,CAAC,GACtC9F,EAAE,CACA,KAAK,EACL;QACEG,WAAW,EACT;MACJ,CAAC,EACD,CACEH,EAAE,CAAC,MAAM,EAAE;QAAEG,WAAW,EAAE;MAAQ,CAAC,EAAE,CACnCJ,GAAG,CAACmB,EAAE,CACJ,IAAI,GACFnB,GAAG,CAACoB,EAAE,CAACoC,OAAO,CAAC4C,WAAW,CAAC,CAC9B,CACF,CAAC,CACH,CACF,GACDpG,GAAG,CAAC8D,EAAE,EAAE,EACZ7D,EAAE,CAAC,KAAK,EAAE;QAAEG,WAAW,EAAE;MAAS,CAAC,EAAE,CACnCH,EAAE,CACA,KAAK,EACL;QACEG,WAAW,EAAE,0BAA0B;QACvCM,KAAK,EAAE;UAAEQ,KAAK,EAAEc,IAAI,CAACtB,KAAK,CAAC2F;QAAW;MACxC,CAAC,EACD,CACErG,GAAG,CAACoC,OAAO,CACT,YAAY,EACZJ,IAAI,CAACtB,KAAK,CAACqF,IAAI,CAChB,GACG,CACE9F,EAAE,CACA,MAAM,EACN;QAAEG,WAAW,EAAE;MAAO,CAAC,EACvB,CAACJ,GAAG,CAACmB,EAAE,CAAC,GAAG,CAAC,CAAC,CACd,EACDlB,EAAE,CACA,MAAM,EACN;QAAEG,WAAW,EAAE;MAAQ,CAAC,EACxB,CACEJ,GAAG,CAACmB,EAAE,CACJnB,GAAG,CAACoB,EAAE,CACJoC,OAAO,CAAC8C,eAAe,CACxB,CACF,CACF,CACF,CACF,GACDtG,GAAG,CAAC8D,EAAE,EAAE,EACZ9D,GAAG,CAACoC,OAAO,CACT,WAAW,EACXJ,IAAI,CAACtB,KAAK,CAACqF,IAAI,CAChB,GACG9F,EAAE,CACA,MAAM,EACN;QAAEG,WAAW,EAAE;MAAa,CAAC,EAC7B,CACEH,EAAE,CACA,MAAM,EACN;QAAEG,WAAW,EAAE;MAAO,CAAC,EACvB,CAACJ,GAAG,CAACmB,EAAE,CAAC,GAAG,CAAC,CAAC,CACd,EACDlB,EAAE,CACA,MAAM,EACN;QAAEG,WAAW,EAAE;MAAQ,CAAC,EACxB,CACEJ,GAAG,CAACmB,EAAE,CACJnB,GAAG,CAACoB,EAAE,CACJoC,OAAO,CAAC+C,cAAc,CACvB,CACF,CACF,CACF,CACF,CACF,GACDvG,GAAG,CAAC8D,EAAE,EAAE,CACb,EACD,CAAC,CACF,EACD7D,EAAE,CACA,KAAK,EACL;QACE6C,UAAU,EAAE,CACV;UACEC,IAAI,EAAE,MAAM;UACZC,OAAO,EAAE,QAAQ;UACjBC,KAAK,EAAEjD,GAAG,CAACoC,OAAO,CAChB,SAAS,EACTJ,IAAI,CAACtB,KAAK,CAACqF,IAAI,CAChB;UACD7C,UAAU,EACR;QACJ,CAAC,CACF;QACD9C,WAAW,EAAE;MACf,CAAC,EACD,CACEH,EAAE,CACA,KAAK,EACL;QACEG,WAAW,EAAE,UAAU;QACvBM,KAAK,EAAE;UACLQ,KAAK,EAAEc,IAAI,CAACtB,KAAK,CAAC8F;QACpB;MACF,CAAC,EACD,CACEvG,EAAE,CAAC,QAAQ,EAAE;QACXG,WAAW,EAAE,WAAW;QACxBoB,KAAK,EAAE;UACLwD,SAAS,EACPhF,GAAG,CAACiF,QAAQ,iBACDjD,IAAI,CAACtB,KAAK,CAAC+F,YAAY;QAEtC;MACF,CAAC,CAAC,CACH,EACD,CAAC,CACF,CACF,CACF,CACF,CAAC,CACH,CAAC,CACH,CACF;IACH,CAAC,CAAC,EACF,CAAC,CACF,CACF,CACF,GACDzE,IAAI,CAACK,IAAI,IAAI,OAAO,GACpBpC,EAAE,CACA,KAAK,EACL;MACEG,WAAW,EAAE,WAAW;MACxBM,KAAK,EAAE;QACL8B,OAAO,YAAKR,IAAI,CAACtB,KAAK,CAACgE,QAAQ,YAAS;QACxC/B,UAAU,EAAEX,IAAI,CAACtB,KAAK,CAACiC;MACzB;IACF,CAAC,EACD,CACE1C,EAAE,CAAC,KAAK,EAAE;MAAEG,WAAW,EAAE;IAAgB,CAAC,EAAE,CAC1CH,EAAE,CAAC,KAAK,EAAE;MAAEG,WAAW,EAAE;IAAQ,CAAC,EAAE,CAClCH,EAAE,CACA,MAAM,EACN;MACES,KAAK,EAAE;QACLQ,KAAK,EAAEc,IAAI,CAACtB,KAAK,CAACC,cAAc;QAChC2E,QAAQ,YAAKtD,IAAI,CAACX,MAAM,CAACiK,aAAa,OAAI;QAC1CC,UAAU,EAAEvJ,IAAI,CAACX,MAAM,CAACmK;MAC1B;IACF,CAAC,EACD,CAACxL,GAAG,CAACmB,EAAE,CAACnB,GAAG,CAACoB,EAAE,CAACY,IAAI,CAACX,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CACpC,CACF,CAAC,EACFU,IAAI,CAACX,MAAM,CAACoK,IAAI,CAAC3D,MAAM,GACnB7H,EAAE,CACA,KAAK,EACL;MACEG,WAAW,EAAE,cAAc;MAC3BM,KAAK,EAAE;QAAEQ,KAAK,EAAEc,IAAI,CAACtB,KAAK,CAACgL;MAAc;IAC3C,CAAC,EACD,CACEzL,EAAE,CAAC,MAAM,EAAE;MAAEG,WAAW,EAAE;IAAY,CAAC,EAAE,CACvCJ,GAAG,CAACmB,EAAE,CAACnB,GAAG,CAACoB,EAAE,CAACY,IAAI,CAACX,MAAM,CAACoK,IAAI,CAAClG,IAAI,CAAC,CAAC,CACtC,CAAC,EACFvD,IAAI,CAACX,MAAM,CAACoK,IAAI,CAACE,UAAU,GACvB1L,EAAE,CACA,MAAM,EACN;MAAEG,WAAW,EAAE;IAAY,CAAC,EAC5B,CACEH,EAAE,CAAC,QAAQ,EAAE;MACXuB,KAAK,EAAE;QACLwD,SAAS,EAAEhF,GAAG,CAACqI,IAAI,CAACC;MACtB;IACF,CAAC,CAAC,CACH,EACD,CAAC,CACF,GACDtI,GAAG,CAAC8D,EAAE,EAAE,CACb,CACF,GACD9D,GAAG,CAAC8D,EAAE,EAAE,CACb,CAAC,EACF7D,EAAE,CAAC,KAAK,EAAE;MAAEG,WAAW,EAAE;IAAe,CAAC,EAAE,CACzCH,EAAE,CACA,MAAM,EACN;MACES,KAAK,EAAE;QACLQ,KAAK,EAAEc,IAAI,CAACtB,KAAK,CAACuH,aAAa;QAC/B3C,QAAQ,YAAKtD,IAAI,CAACX,MAAM,CAACuK,YAAY,OAAI;QACzCL,UAAU,EAAEvJ,IAAI,CAACX,MAAM,CAACwK;MAC1B;IACF,CAAC,EACD,CAAC7L,GAAG,CAACmB,EAAE,CAACnB,GAAG,CAACoB,EAAE,CAACY,IAAI,CAACX,MAAM,CAACyK,IAAI,CAAC,CAAC,CAAC,CACnC,CACF,CAAC,CACH,CACF,GACD9J,IAAI,CAACK,IAAI,IAAI,YAAY,GACzBpC,EAAE,CACA,KAAK,EACL;MACEG,WAAW,EAAE,gBAAgB;MAC7BM,KAAK,EAAE;QACLiC,UAAU,EAAEX,IAAI,CAACtB,KAAK,CAACiC,UAAU;QACjCH,OAAO,YAAKR,IAAI,CAACtB,KAAK,CAACgE,QAAQ,gBAAM1C,IAAI,CAACtB,KAAK,CAACiE,QAAQ;MAC1D;IACF,CAAC,EACD,CACE1E,EAAE,CACA,KAAK,EACL;MACEG,WAAW,EAAE,YAAY;MACzBC,KAAK,EAAE,mBACM2B,IAAI,CAACtB,KAAK,CAACiF,OAAO,oBACnB3D,IAAI,CAACtB,KAAK,CAACkF,MAAM,EAC5B;MACDlF,KAAK,EAAE;QACL+C,YAAY,aAAMzB,IAAI,CAACtB,KAAK,CAACgD,UAAU;MACzC;IACF,CAAC,EACD1D,GAAG,CAAC+B,EAAE,CACJC,IAAI,CAACX,MAAM,CAAC8C,MAAM,IAAI,QAAQ,IAAInC,IAAI,CAACxB,IAAI,CAACqD,MAAM,GAC9C7B,IAAI,CAACxB,IAAI,GACTwB,IAAI,CAACoC,WAAW,EACpB,UAAUZ,OAAO,EAAEX,OAAO,EAAE;MAC1B,OAAO5C,EAAE,CACP,KAAK,EACL;QACEiC,GAAG,YAAKD,KAAK,cAAIY,OAAO,CAAE;QAC1BzC,WAAW,EAAE,YAAY;QACzBC,KAAK,EAAE,mBAAY2B,IAAI,CAACtB,KAAK,CAACmF,QAAQ,EAAG;QACzCnF,KAAK,EAAE;UACL+C,YAAY,YAAKzB,IAAI,CAACtB,KAAK,CAACgD,UAAU,OAAI;UAC1CP,YAAY,YAAKnB,IAAI,CAACtB,KAAK,CAACyC,YAAY;QAC1C;MACF,CAAC,EACD,CACElD,EAAE,CAAC,KAAK,EAAE;QAAEG,WAAW,EAAE;MAAc,CAAC,EAAE,CACxCH,EAAE,CAAC,KAAK,EAAE;QACRG,WAAW,EAAE,OAAO;QACpBoB,KAAK,EAAE;UAAE4B,GAAG,EAAEI,OAAO,CAACsC;QAAY;MACpC,CAAC,CAAC,CACH,CAAC,EACF7F,EAAE,CAAC,KAAK,EAAE;QAAEG,WAAW,EAAE;MAAa,CAAC,EAAE,CACvCJ,GAAG,CAACoC,OAAO,CAAC,WAAW,EAAEJ,IAAI,CAACtB,KAAK,CAACqF,IAAI,CAAC,GACrC9F,EAAE,CACA,KAAK,EACL;QACEG,WAAW,EAAE,YAAY;QACzBC,KAAK,EAAE,CACL2B,IAAI,CAACtB,KAAK,CAACsF,aAAa,IAAI,KAAK,GAC7B,cAAc,GACd,cAAc,gBACXhE,IAAI,CAACtB,KAAK,CAACsF,aAAa;MAEnC,CAAC,EACD,CAAChG,GAAG,CAACmB,EAAE,CAACnB,GAAG,CAACoB,EAAE,CAACoC,OAAO,CAACyC,UAAU,CAAC,CAAC,CAAC,CACrC,GACDjG,GAAG,CAAC8D,EAAE,EAAE,EACZ9D,GAAG,CAACoC,OAAO,CAAC,cAAc,EAAEJ,IAAI,CAACtB,KAAK,CAACqF,IAAI,CAAC,GACxC9F,EAAE,CACA,KAAK,EACL;QAAEG,WAAW,EAAE;MAAgB,CAAC,EAChC,CACEH,EAAE,CACA,MAAM,EACN;QACEG,WAAW,EACT,sBAAsB;QACxBM,KAAK,EAAE;UACLQ,KAAK,EACHc,IAAI,CAACtB,KAAK,CAACwF;QACf;MACF,CAAC,EACD,CACElG,GAAG,CAACmB,EAAE,CACJnB,GAAG,CAACoB,EAAE,CAACoC,OAAO,CAAC2C,aAAa,CAAC,CAC9B,CACF,CACF,CACF,CACF,GACDnG,GAAG,CAAC8D,EAAE,EAAE,EACZ9D,GAAG,CAACoC,OAAO,CAAC,YAAY,EAAEJ,IAAI,CAACtB,KAAK,CAACqF,IAAI,CAAC,GACtC9F,EAAE,CACA,KAAK,EACL;QACEG,WAAW,EACT;MACJ,CAAC,EACD,CACEH,EAAE,CACA,MAAM,EACN;QAAEG,WAAW,EAAE;MAAQ,CAAC,EACxB,CACEJ,GAAG,CAACmB,EAAE,CACJ,KAAK,GACHnB,GAAG,CAACoB,EAAE,CAACoC,OAAO,CAAC4C,WAAW,CAAC,CAC9B,CACF,CACF,CACF,CACF,GACDpG,GAAG,CAAC8D,EAAE,EAAE,EACZ7D,EAAE,CAAC,KAAK,EAAE;QAAEG,WAAW,EAAE;MAAS,CAAC,EAAE,CACnCJ,GAAG,CAACoC,OAAO,CAAC,YAAY,EAAEJ,IAAI,CAACtB,KAAK,CAACqF,IAAI,CAAC,GACtC9F,EAAE,CACA,KAAK,EACL;QACEG,WAAW,EACT;MACJ,CAAC,EACD,CACEH,EAAE,CACA,KAAK,EACL;QAAEG,WAAW,EAAE;MAAc,CAAC,EAC9B,CACEH,EAAE,CAAC,KAAK,EAAE;QACRG,WAAW,EAAE,OAAO;QACpBoB,KAAK,EAAE;UACL4B,GAAG,EAAEgE,OAAO,CAAC,4BAA4B,CAAC;UAC1C5C,GAAG,EAAE;QACP;MACF,CAAC,CAAC,CACH,CACF,EACDvE,EAAE,CACA,MAAM,EACN;QAAEG,WAAW,EAAE;MAAQ,CAAC,EACxB,CACEJ,GAAG,CAACmB,EAAE,CACJnB,GAAG,CAACoB,EAAE,CACJoC,OAAO,CAACuI,gBAAgB,CACzB,CACF,CACF,CACF,EACDvI,OAAO,CAACwI,QAAQ,IAAI,EAAE,GAClB,CACE/L,EAAE,CACA,MAAM,EACN;QAAEG,WAAW,EAAE;MAAO,CAAC,EACvB,CAACJ,GAAG,CAACmB,EAAE,CAAC,GAAG,CAAC,CAAC,CACd,EACDlB,EAAE,CACA,MAAM,EACN;QAAEG,WAAW,EAAE;MAAQ,CAAC,EACxB,CACEJ,GAAG,CAACmB,EAAE,CACJnB,GAAG,CAACoB,EAAE,CACJoC,OAAO,CAACyI,cAAc,CACvB,GAAG,GAAG,CACR,CACF,CACF,CACF,GACDjM,GAAG,CAAC8D,EAAE,EAAE,CACb,EACD,CAAC,CACF,GACD9D,GAAG,CAAC8D,EAAE,EAAE,CACb,CAAC,CACH,CAAC,CACH,CACF;IACH,CAAC,CACF,EACD,CAAC,CACF,CACF,CACF,GACD9D,GAAG,CAAC8D,EAAE,EAAE,EACZ7D,EAAE,CAAC,KAAK,EAAE;MAAEG,WAAW,EAAE;IAAe,CAAC,EAAE,CACzCH,EAAE,CACA,KAAK,EACL;MAAEG,WAAW,EAAE;IAAgB,CAAC,EAChCJ,GAAG,CAAC+B,EAAE,CAAC/B,GAAG,CAACkM,WAAW,CAAClK,IAAI,EAAEC,KAAK,CAAC,EAAE,UAAUkK,GAAG,EAAE7E,GAAG,EAAE;MACvD,OAAOrH,EAAE,CACP,KAAK,EACL;QAAEiC,GAAG,EAAEoF,GAAG;QAAElH,WAAW,EAAE;MAAa,CAAC,EACvC,CACEH,EAAE,CACA,WAAW,EACX;QACEuB,KAAK,EAAE;UACL4K,SAAS,EAAE,OAAO;UAClBC,iBAAiB,EAAE;YAAA,OACjBrM,GAAG,CAACsM,KAAK,CAAC,eAAe,CAAC;UAAA;QAC9B;MACF,CAAC,EACD,CACErM,EAAE,CACA,MAAM,EACN;QACEG,WAAW,EAAE,cAAc;QAC3BoB,KAAK,EAAE;UAAE+K,IAAI,EAAE;QAAQ,CAAC;QACxBA,IAAI,EAAE;MACR,CAAC,EACD,CAACvM,GAAG,CAACmB,EAAE,CAACnB,GAAG,CAACoB,EAAE,CAAC+K,GAAG,CAAC7K,KAAK,CAAC,CAAC,CAAC,CAC5B,EACDrB,EAAE,CACA,KAAK,EACL;QACEG,WAAW,EAAE,UAAU;QACvBC,KAAK,EAAE;UAAEmM,QAAQ,EAAEL,GAAG,CAACK;QAAS,CAAC;QACjC1L,EAAE,EAAE;UACFC,KAAK,EAAE,eAAUC,MAAM,EAAE;YACvBA,MAAM,CAACyL,eAAe,EAAE;YACxB,OAAOzM,GAAG,CAAC0M,oBAAoB,CAC7BP,GAAG,EACHlK,KAAK,CACN;UACH;QACF;MACF,CAAC,EACD,CACEhC,EAAE,CAAC,QAAQ,EAAE;QACXG,WAAW,EAAE,YAAY;QACzBoB,KAAK,EAAE;UAAEa,IAAI,EAAE8J,GAAG,CAAC9J;QAAK;MAC1B,CAAC,CAAC,CACH,EACD,CAAC,CACF,CACF,CACF,CACF,EACD,CAAC,CACF;IACH,CAAC,CAAC,EACF,CAAC,CACF,CACF,CAAC,CACH,CACF;EACH,CAAC,CAAC,EACF,CAAC,CACF,CACF,EACD,CAAC,CACF,CACF,CAAC;AACJ,CAAC;AACD,IAAIsK,eAAe,GAAG,EAAE;AACxB5M,MAAM,CAAC6M,aAAa,GAAG,IAAI;AAE3B,SAAS7M,MAAM,EAAE4M,eAAe"}]}