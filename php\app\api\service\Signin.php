<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2025 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\api\service;

use app\api\model\UserCoupon;
use think\facade\Db;

use app\api\model\User as UserModel;
use app\api\model\signin\Log as SigninLogModel;
use app\api\model\user\BalanceLog as BalanceLogModel;
use app\api\model\signin\Setting as SigninSettingModel;
use app\api\model\signin\Participant as ParticipantModel;
use app\api\service\User as UserService;
use app\common\library\helper;
use app\common\service\BaseService;
use app\common\enum\user\balanceLog\Scene as SceneEnum;
use cores\exception\BaseException;

/**
 * 服务类: 每日签到
 * Class Page
 * @package app\api\service\signin
 */
class Signin extends BaseService
{
    // 签到获得的奖励内容
    private array $reward = [];

    /**
     * 立即签到事件
     * @return bool
     * @throws BaseException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function report(): bool
    {
        // 判断是否启用每日签到
        if (!SigninSettingModel::getEnabled()) {
            throwError('很抱歉，每日签到活动尚未开启~');
        }
        // 判断今日是否已签到
        if ($this->checkReport()) {
            throwError('您今日已签到过了，请明日再来~');
        }
        // 当月累计签到天数
        $currentMonthDays = $this->getCurrentMonthCount();
        // 连续签到天数
        $continuousDays = $this->getContinuousDays();
        // 获取今日签到奖励
        $this->reward = $this->getNowReward($currentMonthDays, $continuousDays);
        // 发放当前签到奖励
        Db::transaction(function () use ($continuousDays) {
            // 发放当前签到奖励
            $this->grantReward();
            // 新增签到记录
            $this->addSigninLog();
            // 更新参与用户信息
            $this->updateParticipant($continuousDays);
        });
        return true;
    }

    /**
     * 发放当前签到奖励
     * @param int $continuousDays 连续签到天数
     * @return void
     * @throws BaseException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    private function updateParticipant(int $continuousDays): void
    {
        (new ParticipantModel)->updateRecord($this->reward, $continuousDays + 1);
    }


    /**
     * 发放当前签到奖励
     * @return void
     * @throws BaseException
     */
    private function addSigninLog(): void
    {
        (new SigninLogModel)->add([
            'user_id' => UserService::getCurrentLoginUserId(),
            'points_num' => $this->reward['points'],
            'coupon_num' => $this->reward['actualCouponNum'],
            'money' => $this->reward['money'],
            'options' => $this->reward['options']
        ]);
    }

    /**
     * 发放当前签到奖励
     * @return void
     * @throws BaseException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    private function grantReward()
    {
        // 当前用户ID
        $userId = UserService::getCurrentLoginUserId();
        // 发放积分奖励
        if ($this->reward['points'] > 0) {
            $describe = '每日签到奖励：' . \date('Y-m-d');
            UserModel::setIncPoints($userId, $this->reward['points'], $describe);
        }
        // 发放金额奖励
        if ($this->reward['money'] > 0) {
            // 累积用户余额
            UserModel::setIncBalance($userId, (float)$this->reward['money']);
            // 用户余额变动明细
            BalanceLogModel::add(SceneEnum::SIGNIN, [
                'user_id' => $userId,
                'money' => $this->reward['money']
            ], ['date' => \date('Y-m-d')]);
        }
        // 发放优惠券
        if (!empty($this->reward['couponIds'])) {
            $count = (new UserCoupon)->batchReceive($this->reward['couponIds']);
            $this->reward['actualCouponNum'] = $count;
        }
    }

    /**
     * 获取签到获得的奖励内容
     * @return array
     */
    public function getReward(): array
    {
        return $this->reward;
    }

    /**
     * 今日是否已签到
     * @param bool $isLogin
     * @return bool
     * @throws BaseException
     */
    protected function checkReport(bool $isLogin = true): bool
    {
        return $isLogin && (new SigninLogModel)->checkSigninToday();
    }

    /**
     * 获取连续签到天数
     * @param bool $isLogin
     * @return int
     * @throws BaseException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    protected function getContinuousDays(bool $isLogin = true): int
    {
        $model = new ParticipantModel;
        return $isLogin ? $model->getContinuousDays() : 0;
    }

    /**
     * 获取当月累计签到数
     * @param bool $isLogin
     * @return int
     * @throws BaseException
     */
    protected function getCurrentMonthCount(bool $isLogin = true): int
    {
        $model = new SigninLogModel;
        return $isLogin ? $model->getCurrentMonthCount() : 0;
    }

    /**
     * 今日或明日的签到奖励
     * @param int $currentMonthDays 当月累计签到天数
     * @param int $continuousDays 连续签到天数
     * @return array|true[]
     * @throws BaseException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    protected function getNowReward(int $currentMonthDays, int $continuousDays): array
    {
        // 每日签到规则设置
        $options = SigninSettingModel::getItemOptions();
        // 连续签到奖励
        $continuousReward = $this->getContinuousReward($options, $continuousDays);
        // 累计签到奖励
        $cumulativeReward = $this->getCumulativeReward($options, $currentMonthDays);
        return [
            // 积分奖励(总)
            'points' => $options['fixedReward']['points'] + $continuousReward['points'] + $cumulativeReward['points'],
            // 金额奖励(总)
            'money' => helper::bcadd($continuousReward['money'], $cumulativeReward['money']),
            // 优惠券奖励(总ID集)
            'couponIds' => \array_merge($continuousReward['couponIds'], $cumulativeReward['couponIds']),
            // 实际领取优惠券的数量 (实际发放时计算)
            'actualCouponNum' => 0,
            // 奖励配置
            'options' => [
                'fixedReward' => $options['fixedReward'],
                'continuous' => $continuousReward,
                'cumulative' => $cumulativeReward
            ]
        ];
    }

    /**
     * 连续签到奖励
     * @param array $options 每日签到规则设置
     * @param int $continuousDays 连续签到天数
     * @return array
     */
    private function getContinuousReward(array $options, int $continuousDays): array
    {
        // 奖励内容
        $reward = ['points' => 0, 'money' => 0, 'couponIds' => []];
        // 未开启连续签到奖励
        if (!$options['continuous']['enabled']) {
            return $reward;
        }
        // 获取规则中最大的天数
        $daysArr = helper::getArrayColumn($options['continuous']['ruleList'], 'days');
        $maxDays = \max($daysArr);
        // 是否超出最大天数后
        $isExceed = ($continuousDays + 1) > \max($daysArr);
        // 超出最大天数后 不再赠送
        if ($isExceed && $options['continuous']['finishMax'] == 'end') {
            return $reward;
        }
        // 超出最大天数后 重新计算并赠送
        if ($isExceed && $options['continuous']['finishMax'] == 'repeat') {
            $remainder = ($continuousDays + 1) % $maxDays;
            $continuousDays = ($remainder === 0 ? $maxDays : $remainder) - 1;
        }
        // 今日是否满足 连续签到奖励
        $ruleItem = helper::arraySearch($options['continuous']['ruleList'], 'days',
            $continuousDays + 1);
        if (empty($ruleItem)) {
            return $reward;
        }
        // 积分奖励
        if ($ruleItem['points']['enabled']) {
            $reward['points'] = $ruleItem['points']['value'];
        }
        // 金额奖励
        if ($ruleItem['money']['enabled']) {
            $reward['money'] = $ruleItem['money']['value'];
        }
        // 优惠券奖励
        if ($ruleItem['coupon']['enabled']) {
            $reward['couponIds'] = $ruleItem['coupon']['couponIds'];
        }
        return $reward;
    }

    /**
     * 累计签到奖励
     * @param array $options 每日签到规则设置
     * @param int $currentMonthDays 当月累计签到天数
     * @return array
     */
    private function getCumulativeReward(array $options, int $currentMonthDays): array
    {
        // 奖励内容
        $reward = ['points' => 0, 'money' => 0, 'couponIds' => []];
        // 未开启累计签到奖励
        if (!$options['continuous']['enabled']) {
            return $reward;
        }
        // 今日是否满足 累计签到奖励
        $ruleItem = helper::arraySearch($options['cumulative']['ruleList'], 'days',
            $currentMonthDays + 1);
        if (empty($ruleItem)) {
            return $reward;
        }
        // 积分奖励
        if ($ruleItem['points']['enabled']) {
            $reward['points'] = $ruleItem['points']['value'];
        }
        // 金额奖励
        if ($ruleItem['money']['enabled']) {
            $reward['money'] = $ruleItem['money']['value'];
        }
        // 优惠券奖励
        if ($ruleItem['coupon']['enabled']) {
            $reward['couponIds'] = $ruleItem['coupon']['couponIds'];
        }
        return $reward;
    }
}