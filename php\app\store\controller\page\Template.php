<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
namespace app\store\controller\page;

use app\store\controller\Controller;
use app\store\service\page\Template as PageTemplateService;
use cores\exception\BaseException;
use think\response\Json;

/**
 * 店铺页面 - 模版市场管理
 * Class Template
 * @package app\store\controller\page
 */
class Template extends Controller
{
    /**
     * 获取页面市场模版列表
     * @return Json
     * @throws BaseException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function list(): Json
    {
        $service = new PageTemplateService;
        $list = $service->getList($this->request->param());
        return $this->renderSuccess(compact('list'));
    }

    /**
     * 获取页面市场模版详情
     * @param int $templateId 页面模版ID
     * @return Json
     * @throws BaseException
     */
    public function detail(int $templateId): Json
    {
        $service = new PageTemplateService;
        $detail = $service->getDetail($templateId);
        return $this->renderSuccess(compact('detail'));
    }
}