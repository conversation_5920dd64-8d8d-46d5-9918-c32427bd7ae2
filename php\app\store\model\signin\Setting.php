<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\store\model\signin;

use think\facade\Cache;
use cores\exception\BaseException;
use app\common\model\signin\Setting as SettingModel;

/**
 * 模型类: 积分商城-基础设置
 * Class Setting
 * @package app\store\model\signin
 */
class Setting extends SettingModel
{
    /**
     * 设置项描述
     * @var array
     */
    private array $describe = ['page' => '基础设置', 'options' => '规则设置'];

    /**
     * 更新系统设置
     * @param array $data
     * @return bool
     */
    public function edit(array $data): bool
    {
        $this->transaction(function () use ($data) {
            foreach ($data as $key => $values) {
                $this->saveValues($key, $values);
            }
            // 删除设置缓存
            Cache::delete('signin_setting_' . self::$storeId);
        });
        return true;
    }

    /**
     * 保存设置项
     * @param string $key
     * @param array $values
     * @return void
     * @throws BaseException
     */
    private function saveValues(string $key, array $values): void
    {
        // 数据验证
        if (!$this->validValues($key, $values)) {
            throwError($this->error);
        }
        $model = self::detail($key) ?: new self;
        $model->save([
            'key' => $key,
            'describe' => $this->describe[$key],
            'values' => $values,
            'update_time' => time(),
            'store_id' => self::$storeId,
        ]);
    }

    /**
     * 数据验证
     * @param string $key
     * @param array $values
     * @return bool
     */
    private function validValues(string $key, array $values): bool
    {
        // 暂无验证, 后续可能有补充
        return true;
    }
}