<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2025 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\store\model\dealer;

use cores\exception\BaseException;
use app\store\model\dealer\User as DealerUserModel;
use app\common\model\dealer\Withdraw as WithdrawModel;
use app\common\service\Order as OrderService;
use app\common\enum\payment\Method as PaymentMethodEnum;
use app\common\enum\dealer\withdraw\ApplyStatus as ApplyStatusEnum;
use app\common\library\payment\Facade as PaymentFacade;

/**
 * 分销商提现明细模型
 * Class Withdraw
 * @package app\store\model\dealer
 */
class Withdraw extends WithdrawModel
{
    /**
     * 获取器：申请时间
     * @param $value
     * @return false|string
     */
    public function getAuditTimeAttr($value)
    {
        return $value > 0 ? \format_time($value) : 0;
    }

    /**
     * 获取分销商提现列表
     * @param array $param
     * @return \think\Paginator
     * @throws \think\db\exception\DbException
     */
    public function getList(array $param = []): \think\Paginator
    {
        // 默认查询参数
        $params = $this->setQueryDefaultValue($param, [
            'dealerId' => null, // 分销商ID
            'search' => '', // 分销商ID
            'applyStatus' => -1, // 申请状态
            'payType' => -1, // 打款方式
        ]);
        // 查询条件
        $filter = [];
        $params['dealerId'] > 0 && $filter[] = ['m.user_id', '=', (int)$params['dealerId']];
        !empty($params['search']) && $filter[] = ['dealer.real_name|dealer.mobile|user.nick_name', 'like', "%{$params['search']}%"];
        $params['applyStatus'] > -1 && $filter[] = ['m.apply_status', '=', (int)$params['applyStatus']];
        $params['payType'] > -1 && $filter[] = ['m.pay_type', '=', (int)$params['payType']];
        // 获取列表数据
        return $this->alias('m')
            ->with(['user.avatar'])
            ->field('m.*, dealer.real_name, dealer.mobile')
            ->join('dealer_user dealer', 'dealer.user_id = m.user_id')
            ->join('user', 'user.user_id = m.user_id')
            ->where($filter)
            ->order(['m.create_time' => 'desc'])
            ->paginate(15);
    }

    /**
     * 分销商提现审核
     * @param array $data
     * @return bool
     */
    public function audit(array $data): bool
    {
        // 验证当前提现记录状态
        if ($this['apply_status'] != ApplyStatusEnum::WAIT) {
            $this->error = '很抱歉，当前提现记录不合法';
            return false;
        }
        // 验证驳回原因
        if ($data['apply_status'] == ApplyStatusEnum::REJECT && empty($data['reject_reason'])) {
            $this->error = '请填写驳回原因';
            return false;
        }
        $this->transaction(function () use ($data) {
            // 更新申请记录
            $data['audit_time'] = \time();
            $this->save($data);
            // 提现驳回：解冻分销商资金
            if ($data['apply_status'] == ApplyStatusEnum::REJECT) {
                DealerUserModel::backFreezeMoney($this['user_id'], $this['money']);
            }
        });
        return true;
    }

    /**
     * 分销商提现：微信商家转账到零钱
     * @return bool
     * @throws BaseException
     * @throws \cores\exception\BaseException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function transfers(): bool
    {
        // 获取分销商用户微信小程序OpenID
        $openId = $this->getWeiXinOpenId($this['user_id'], $this['platform']);
        // 生成第三方交易订单号
        $outTradeNo = OrderService::createOrderNo();
        // 获取支付方式的配置信息
        $options = static::getPaymentConfig($this['platform']);
        if ($options['mchType'] === 'provider') {
            throwError('很抱歉，微信企业付款API不支持服务商模式');
        }
        // 整理下单接口所需的附加数据
        $extra = ['openid' => $openId, 'remark' => '分销商提现付款'];
        // 构建支付模块
        $Payment = PaymentFacade::store(PaymentMethodEnum::WECHAT)->setOptions($options, $this['platform']);
        // 执行微信商家转账到零钱API
        if (!$Payment->transfers($outTradeNo, (string)$this['money'], $extra)) {
            throwError($Payment->getError() ?: '商家转账到零钱API请求失败');
        }
        // 确认已打款
        return $this->setPayed();
    }
}