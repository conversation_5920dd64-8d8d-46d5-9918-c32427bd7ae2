<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2023 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\common\model\store\shop;

use cores\BaseModel;
use think\model\relation\BelongsTo;

/**
 * 商家门店核销订单记录模型
 * Class Clerk
 * @package app\common\model\store
 */
class Order extends BaseModel
{
    // 定义表名
    protected $name = 'store_shop_order';

    // 定义主键
    protected $pk = 'id';

    protected $updateTime = false;

    /**
     * 关联门店记录
     * @return BelongsTo
     */
    public function shop(): BelongsTo
    {
        $module = static::getCalledModule();
        return $this->BelongsTo("app\\{$module}\\model\\store\\Shop", 'shop_id');
    }

    /**
     * 关联店员记录
     * @return BelongsTo
     */
    public function clerk(): BelongsTo
    {
        $module = static::getCalledModule();
        return $this->BelongsTo("app\\{$module}\\model\\store\\shop\\Clerk", 'clerk_id');
    }

    /**
     * 关联订单记录
     * @return BelongsTo
     */
    public function order(): BelongsTo
    {
        $module = static::getCalledModule();
        return $this->BelongsTo("app\\{$module}\\model\\Order", 'order_id');
    }

    /**
     * 新增核销记录
     * @param int $orderId 订单ID
     * @param int $shopId 门店ID
     * @param int $clerkId 核销员ID
     * @return mixed
     */
    public static function add(int $orderId, int $shopId, int $clerkId)
    {
        return (new static)->save([
            'order_id' => $orderId,
            'shop_id' => $shopId,
            'clerk_id' => $clerkId,
            'store_id' => static::$storeId
        ]);
    }
}
