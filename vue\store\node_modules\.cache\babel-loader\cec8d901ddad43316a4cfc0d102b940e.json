{"remainingRequest": "D:\\2025\\gaodux\\gaodux5\\vue\\store\\node_modules\\babel-loader\\lib\\index.js!D:\\2025\\gaodux\\gaodux5\\vue\\store\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--6!D:\\2025\\gaodux\\gaodux5\\vue\\store\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\2025\\gaodux\\gaodux5\\vue\\store\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025\\gaodux\\gaodux5\\vue\\store\\src\\views\\page\\modules\\editor\\modules\\HotZoneModal\\HotZoneModal.vue?vue&type=template&id=6a1f1b3a&scoped=true&", "dependencies": [{"path": "D:\\2025\\gaodux\\gaodux5\\vue\\store\\src\\views\\page\\modules\\editor\\modules\\HotZoneModal\\HotZoneModal.vue", "mtime": 1677427200000}, {"path": "D:\\2025\\gaodux\\gaodux5\\vue\\store\\babel.config.js", "mtime": 1656432000000}, {"path": "D:\\2025\\gaodux\\gaodux5\\vue\\store\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751804606056}, {"path": "D:\\2025\\gaodux\\gaodux5\\vue\\store\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751804606056}, {"path": "D:\\2025\\gaodux\\gaodux5\\vue\\store\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751804605661}, {"path": "D:\\2025\\gaodux\\gaodux5\\vue\\store\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1751804675994}, {"path": "D:\\2025\\gaodux\\gaodux5\\vue\\store\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751804606056}, {"path": "D:\\2025\\gaodux\\gaodux5\\vue\\store\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751804675994}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "attrs", "title", "width", "visible", "isLoading", "maskClosable", "destroyOnClose", "on", "cancel", "handleCancel", "style", "height", "imageH", "ref", "src", "imageSrc", "alt", "load", "imageLoad", "imageW", "_l", "maps", "item", "index", "key", "min<PERSON><PERSON><PERSON>", "minHeight", "x", "left", "y", "top", "w", "h", "parent", "dragstop", "resizestop", "_v", "_s", "confirm", "$event", "handleDelZone", "href", "model", "value", "link", "callback", "$$v", "$set", "expression", "_e", "slot", "click", "handleAddZone", "type", "handleSubmit", "staticRenderFns", "_withStripped"], "sources": ["D:/2025/gaodux/gaodux5/vue/store/src/views/page/modules/editor/modules/HotZoneModal/HotZoneModal.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"a-modal\",\n    {\n      staticClass: \"noborder\",\n      attrs: {\n        title: _vm.title,\n        width: 804,\n        visible: _vm.visible,\n        isLoading: _vm.isLoading,\n        maskClosable: false,\n        destroyOnClose: true,\n      },\n      on: { cancel: _vm.handleCancel },\n    },\n    [\n      _c(\"div\", { staticClass: \"scroll-view\" }, [\n        _c(\n          \"div\",\n          { staticClass: \"zone-body\", style: { height: `${_vm.imageH}px` } },\n          [\n            _c(\"div\", { staticClass: \"bg-image\" }, [\n              _c(\"img\", {\n                ref: \"image\",\n                staticClass: \"image\",\n                attrs: { src: _vm.imageSrc, alt: \"\" },\n                on: { load: _vm.imageLoad },\n              }),\n            ]),\n            _vm.imageW > 0 && _vm.imageH > 0\n              ? _vm._l(_vm.maps, function (item, index) {\n                  return _c(\n                    \"vue-draggable-resizable\",\n                    {\n                      key: item.key,\n                      attrs: {\n                        \"class-name\": \"my-vdr\",\n                        \"class-name-handle\": \"my-handle\",\n                        minWidth: 60,\n                        minHeight: 20,\n                        x: item.left,\n                        y: item.top,\n                        w: item.width,\n                        h: item.height,\n                        parent: true,\n                      },\n                      on: {\n                        dragstop: (left, top) => {\n                          item.left = left\n                          item.top = top\n                        },\n                        resizestop: (left, top, width, height) => {\n                          item.left = left\n                          item.top = top\n                          item.width = width\n                          item.height = height\n                        },\n                      },\n                    },\n                    [\n                      _c(\"div\", { staticClass: \"title\" }, [\n                        _vm._v(\"热区\" + _vm._s(index + 1)),\n                      ]),\n                      _c(\n                        \"div\",\n                        { staticClass: \"actions\" },\n                        [\n                          _c(\n                            \"a-popconfirm\",\n                            {\n                              attrs: { title: \"您确定要删除该热区吗？\" },\n                              on: {\n                                confirm: function ($event) {\n                                  return _vm.handleDelZone(index)\n                                },\n                              },\n                            },\n                            [\n                              _c(\"a\", { attrs: { href: \"javascript:;\" } }, [\n                                _vm._v(\"删除\"),\n                              ]),\n                            ]\n                          ),\n                          _c(\"SLink2\", {\n                            model: {\n                              value: item.link,\n                              callback: function ($$v) {\n                                _vm.$set(item, \"link\", $$v)\n                              },\n                              expression: \"item.link\",\n                            },\n                          }),\n                        ],\n                        1\n                      ),\n                    ]\n                  )\n                })\n              : _vm._e(),\n          ],\n          2\n        ),\n      ]),\n      _c(\n        \"template\",\n        { slot: \"footer\" },\n        [\n          _c(\"a-button\", { key: \"back\", on: { click: _vm.handleAddZone } }, [\n            _vm._v(\"添加新热区\"),\n          ]),\n          _c(\n            \"a-button\",\n            {\n              key: \"submit\",\n              attrs: { type: \"primary\" },\n              on: { click: _vm.handleSubmit },\n            },\n            [_vm._v(\"保存\")]\n          ),\n        ],\n        1\n      ),\n    ],\n    2\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": ";AAAA,IAAIA,MAAM,GAAG,SAASA,MAAM,GAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,SAAS,EACT;IACEE,WAAW,EAAE,UAAU;IACvBC,KAAK,EAAE;MACLC,KAAK,EAAEL,GAAG,CAACK,KAAK;MAChBC,KAAK,EAAE,GAAG;MACVC,OAAO,EAAEP,GAAG,CAACO,OAAO;MACpBC,SAAS,EAAER,GAAG,CAACQ,SAAS;MACxBC,YAAY,EAAE,KAAK;MACnBC,cAAc,EAAE;IAClB,CAAC;IACDC,EAAE,EAAE;MAAEC,MAAM,EAAEZ,GAAG,CAACa;IAAa;EACjC,CAAC,EACD,CACEZ,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CACxCF,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE,WAAW;IAAEW,KAAK,EAAE;MAAEC,MAAM,YAAKf,GAAG,CAACgB,MAAM;IAAK;EAAE,CAAC,EAClE,CACEf,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAW,CAAC,EAAE,CACrCF,EAAE,CAAC,KAAK,EAAE;IACRgB,GAAG,EAAE,OAAO;IACZd,WAAW,EAAE,OAAO;IACpBC,KAAK,EAAE;MAAEc,GAAG,EAAElB,GAAG,CAACmB,QAAQ;MAAEC,GAAG,EAAE;IAAG,CAAC;IACrCT,EAAE,EAAE;MAAEU,IAAI,EAAErB,GAAG,CAACsB;IAAU;EAC5B,CAAC,CAAC,CACH,CAAC,EACFtB,GAAG,CAACuB,MAAM,GAAG,CAAC,IAAIvB,GAAG,CAACgB,MAAM,GAAG,CAAC,GAC5BhB,GAAG,CAACwB,EAAE,CAACxB,GAAG,CAACyB,IAAI,EAAE,UAAUC,IAAI,EAAEC,KAAK,EAAE;IACtC,OAAO1B,EAAE,CACP,yBAAyB,EACzB;MACE2B,GAAG,EAAEF,IAAI,CAACE,GAAG;MACbxB,KAAK,EAAE;QACL,YAAY,EAAE,QAAQ;QACtB,mBAAmB,EAAE,WAAW;QAChCyB,QAAQ,EAAE,EAAE;QACZC,SAAS,EAAE,EAAE;QACbC,CAAC,EAAEL,IAAI,CAACM,IAAI;QACZC,CAAC,EAAEP,IAAI,CAACQ,GAAG;QACXC,CAAC,EAAET,IAAI,CAACpB,KAAK;QACb8B,CAAC,EAAEV,IAAI,CAACX,MAAM;QACdsB,MAAM,EAAE;MACV,CAAC;MACD1B,EAAE,EAAE;QACF2B,QAAQ,EAAE,kBAACN,IAAI,EAAEE,GAAG,EAAK;UACvBR,IAAI,CAACM,IAAI,GAAGA,IAAI;UAChBN,IAAI,CAACQ,GAAG,GAAGA,GAAG;QAChB,CAAC;QACDK,UAAU,EAAE,oBAACP,IAAI,EAAEE,GAAG,EAAE5B,KAAK,EAAES,MAAM,EAAK;UACxCW,IAAI,CAACM,IAAI,GAAGA,IAAI;UAChBN,IAAI,CAACQ,GAAG,GAAGA,GAAG;UACdR,IAAI,CAACpB,KAAK,GAAGA,KAAK;UAClBoB,IAAI,CAACX,MAAM,GAAGA,MAAM;QACtB;MACF;IACF,CAAC,EACD,CACEd,EAAE,CAAC,KAAK,EAAE;MAAEE,WAAW,EAAE;IAAQ,CAAC,EAAE,CAClCH,GAAG,CAACwC,EAAE,CAAC,IAAI,GAAGxC,GAAG,CAACyC,EAAE,CAACd,KAAK,GAAG,CAAC,CAAC,CAAC,CACjC,CAAC,EACF1B,EAAE,CACA,KAAK,EACL;MAAEE,WAAW,EAAE;IAAU,CAAC,EAC1B,CACEF,EAAE,CACA,cAAc,EACd;MACEG,KAAK,EAAE;QAAEC,KAAK,EAAE;MAAc,CAAC;MAC/BM,EAAE,EAAE;QACF+B,OAAO,EAAE,iBAAUC,MAAM,EAAE;UACzB,OAAO3C,GAAG,CAAC4C,aAAa,CAACjB,KAAK,CAAC;QACjC;MACF;IACF,CAAC,EACD,CACE1B,EAAE,CAAC,GAAG,EAAE;MAAEG,KAAK,EAAE;QAAEyC,IAAI,EAAE;MAAe;IAAE,CAAC,EAAE,CAC3C7C,GAAG,CAACwC,EAAE,CAAC,IAAI,CAAC,CACb,CAAC,CACH,CACF,EACDvC,EAAE,CAAC,QAAQ,EAAE;MACX6C,KAAK,EAAE;QACLC,KAAK,EAAErB,IAAI,CAACsB,IAAI;QAChBC,QAAQ,EAAE,kBAAUC,GAAG,EAAE;UACvBlD,GAAG,CAACmD,IAAI,CAACzB,IAAI,EAAE,MAAM,EAAEwB,GAAG,CAAC;QAC7B,CAAC;QACDE,UAAU,EAAE;MACd;IACF,CAAC,CAAC,CACH,EACD,CAAC,CACF,CACF,CACF;EACH,CAAC,CAAC,GACFpD,GAAG,CAACqD,EAAE,EAAE,CACb,EACD,CAAC,CACF,CACF,CAAC,EACFpD,EAAE,CACA,UAAU,EACV;IAAEqD,IAAI,EAAE;EAAS,CAAC,EAClB,CACErD,EAAE,CAAC,UAAU,EAAE;IAAE2B,GAAG,EAAE,MAAM;IAAEjB,EAAE,EAAE;MAAE4C,KAAK,EAAEvD,GAAG,CAACwD;IAAc;EAAE,CAAC,EAAE,CAChExD,GAAG,CAACwC,EAAE,CAAC,OAAO,CAAC,CAChB,CAAC,EACFvC,EAAE,CACA,UAAU,EACV;IACE2B,GAAG,EAAE,QAAQ;IACbxB,KAAK,EAAE;MAAEqD,IAAI,EAAE;IAAU,CAAC;IAC1B9C,EAAE,EAAE;MAAE4C,KAAK,EAAEvD,GAAG,CAAC0D;IAAa;EAChC,CAAC,EACD,CAAC1D,GAAG,CAACwC,EAAE,CAAC,IAAI,CAAC,CAAC,CACf,CACF,EACD,CAAC,CACF,CACF,EACD,CAAC,CACF;AACH,CAAC;AACD,IAAImB,eAAe,GAAG,EAAE;AACxB5D,MAAM,CAAC6D,aAAa,GAAG,IAAI;AAE3B,SAAS7D,MAAM,EAAE4D,eAAe"}]}