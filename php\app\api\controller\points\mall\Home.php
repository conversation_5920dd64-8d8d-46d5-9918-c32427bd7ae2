<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2025 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\api\controller\points\mall;

use think\response\Json;
use app\api\controller\Controller;
use app\api\service\points\Mall as PointsMallService;

/**
 * 控制器: 积分商城首页
 * Class Home
 * @package app\api\controller\points\mall
 */
class Home extends Controller
{
    /**
     * 积分商城首页
     * @return Json
     * @throws \cores\exception\BaseException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function data(): Json
    {
        $service = new PointsMallService;
        $data = $service->getPageData();
        return $this->renderSuccess($data);
    }
}