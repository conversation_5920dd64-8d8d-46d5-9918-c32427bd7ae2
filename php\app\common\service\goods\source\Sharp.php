<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\common\service\goods\source;

use app\api\model\sharp\Goods as SharpGoodsModel;
use app\api\model\sharp\GoodsSku as GoodsSkuModel;
use app\common\enum\goods\DeductStockType as DeductStockTypeEnum;

/**
 * 商品来源-秒杀商品扩展类
 * Class Sharp
 * @package app\common\service\stock
 */
class Sharp extends Basics
{
    /**
     * 更新商品库存 (针对下单减库存的商品)
     * @param $goodsList
     * @return bool
     */
    public function updateGoodsStock($goodsList): bool
    {
        $goodsData = [];
        $goodsSkuData = [];
        foreach ($goodsList as $goods) {
            // 下单减库存
            if ($goods['deduct_stock_type'] == DeductStockTypeEnum::CREATE) {
                // 记录商品总库存
                $goodsData[] = [
                    'where' => [
                        'sharp_goods_id' => $goods['sharp_goods_id']
                    ],
                    'data' => ['seckill_stock' => ['dec', $goods['total_num']]]
                ];
                // 记录商品sku库存
                $goodsSkuData[] = [
                    'where' => [
                        'sharp_goods_id' => $goods['sharp_goods_id'],
                        'goods_sku_id' => $goods['goods_sku_id'],
                    ],
                    'data' => ['seckill_stock' => ['dec', $goods['total_num']]],
                ];
            }
        }
        // 更新商品总销量
        !empty($goodsData) && $this->updateGoods($goodsData);
        return !empty($goodsSkuData) && $this->updateGoodsSku($goodsSkuData);
    }

    /**
     * 更新商品库存销量（订单付款后）
     * @param $goodsList
     * @return bool
     */
    public function updateStockSales($goodsList): bool
    {
        $goodsData = [];
        $goodsSkuData = [];
        foreach ($goodsList as $goods) {
            // 商品id
            $sharpGoodsId = $goods['goods_source_id'];
            // 记录商品总销量
            $goodsItem = [
                'where' => ['sharp_goods_id' => $sharpGoodsId],
                'data' => ['total_sales' => ['inc', $goods['total_num']]]
            ];
            // 付款减库存
            if ($goods['deduct_stock_type'] == DeductStockTypeEnum::PAYMENT) {
                // 记录商品总库存
                $goodsItem['data']['seckill_stock'] = ['dec', $goods['total_num']];
                // 记录商品sku库存
                $goodsSkuData[] = [
                    'data' => ['seckill_stock' => ['dec', $goods['total_num']]],
                    'where' => [
                        'sharp_goods_id' => $sharpGoodsId,
                        'goods_sku_id' => $goods['goods_sku_id'],
                    ],
                ];
            }
            $goodsData[] = $goodsItem;
        }
        // 更新商品库存销量
        !empty($goodsData) && $this->updateGoods($goodsData);
        return !empty($goodsSkuData) && $this->updateGoodsSku($goodsSkuData);
    }

    /**
     * 回退商品库存
     * @param $goodsList
     * @param $isPayOrder
     * @return bool
     */
    public function backGoodsStock($goodsList, $isPayOrder = false): bool
    {
        $goodsData = [];
        $goodsSkuData = [];
        foreach ($goodsList as $goods) {
            // 商品id
            $sharpGoodsId = $goods['goods_source_id'];
            $goodsItem = [
                'where' => ['sharp_goods_id' => $sharpGoodsId],
                'data' => ['seckill_stock' => ['inc', $goods['total_num']]]
            ];
            $goodsSkuItem = [
                'where' => [
                    'sharp_goods_id' => $sharpGoodsId,
                    'goods_sku_id' => $goods['goods_sku_id'],
                ],
                'data' => ['seckill_stock' => ['inc', $goods['total_num']]],
            ];
            // 付款订单全部库存
            if ($isPayOrder) {
                $goodsData[] = $goodsItem;
                $goodsSkuData[] = $goodsSkuItem;
            }
            // 未付款订单，判断必须为下单减库存时才回退
            if (!$isPayOrder && $goods['deduct_stock_type'] == DeductStockTypeEnum::CREATE) {
                $goodsData[] = $goodsItem;
                $goodsSkuData[] = $goodsSkuItem;
            }
        }
        // 更新商品总库存
        !empty($goodsData) && $this->updateGoods($goodsData);
        // 更新商品sku库存
        return !empty($goodsSkuData) && $this->updateGoodsSku($goodsSkuData);
    }

    /**
     * 更新商品信息
     * @param $data
     * @return array|false
     */
    private function updateGoods($data)
    {
        return (new SharpGoodsModel)->updateAll($data);
    }

    /**
     * 更新商品sku信息
     * @param $data
     * @return array|false
     */
    private function updateGoodsSku($data)
    {
        return (new GoodsSkuModel)->updateAll($data);
    }
}