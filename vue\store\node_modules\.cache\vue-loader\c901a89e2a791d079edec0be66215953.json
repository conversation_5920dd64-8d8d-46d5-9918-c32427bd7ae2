{"remainingRequest": "D:\\2025\\gaodux\\gaodux5\\vue\\store\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025\\gaodux\\gaodux5\\vue\\store\\src\\views\\page\\modules\\phone\\modules\\UTag.vue?vue&type=style&index=0&id=15d8886c&lang=less&scoped=true&", "dependencies": [{"path": "D:\\2025\\gaodux\\gaodux5\\vue\\store\\src\\views\\page\\modules\\phone\\modules\\UTag.vue", "mtime": 1656432000000}, {"path": "D:\\2025\\gaodux\\gaodux5\\vue\\store\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1751804674603}, {"path": "D:\\2025\\gaodux\\gaodux5\\vue\\store\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1751804675994}, {"path": "D:\\2025\\gaodux\\gaodux5\\vue\\store\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1751804675777}, {"path": "D:\\2025\\gaodux\\gaodux5\\vue\\store\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1751804709616}, {"path": "D:\\2025\\gaodux\\gaodux5\\vue\\store\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751804606056}, {"path": "D:\\2025\\gaodux\\gaodux5\\vue\\store\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751804675994}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQoudS10YWcgew0KICBib3gtc2l6aW5nOiBib3JkZXItYm94Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBib3JkZXItcmFkaXVzOiAzcHg7DQogIGRpc3BsYXk6IGlubGluZS1ibG9jazsNCiAgbGluZS1oZWlnaHQ6IDE7DQp9DQoudS1zaXplLW1pbmkgew0KICBmb250LXNpemU6IDEwcHg7DQogIHBhZGRpbmc6IDNweCA2cHg7DQp9DQoudS1tb2RlLXBsYWluLWVycm9yIHsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZmZmZjsNCiAgY29sb3I6ICNmYTM1MzQ7DQogIGJvcmRlcjogMXB4IHNvbGlkICNmYTM1MzQ7DQp9DQoudS1tb2RlLWxpZ2h0LWVycm9yIHsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2ZlZjBmMDsNCiAgY29sb3I6ICNmYTM1MzQ7DQogIGJvcmRlcjogMXB4IHNvbGlkICNmYWI2YjY7DQp9DQo="}, {"version": 3, "sources": ["UTag.vue"], "names": [], "mappings": ";AA+BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "UTag.vue", "sourceRoot": "src/views/page/modules/phone/modules", "sourcesContent": ["<template>\r\n  <div class=\"u-tag\" :class=\"[`u-size-${size}`, `u-mode-${mode}-error`]\">\r\n    <span>{{ text }}</span>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport PropTypes from 'ant-design-vue/es/_util/vue-types'\r\n\r\n// u-tag组件\r\nexport default {\r\n  name: 'UTag',\r\n  components: {\r\n  },\r\n  props: {\r\n    // 标签文字\r\n    text: PropTypes.string.def(''),\r\n    // 标签尺寸  default mini\r\n    size: PropTypes.string.def('default'),\r\n    // 主题类型  primary / success / info / warning / error\r\n    type: PropTypes.string.def('primary'),\r\n    // 模式选择  dark(深色背景)、light(浅色背景)、plain(白色背景)\r\n    mode: PropTypes.string.def('light'),\r\n  },\r\n  data () {\r\n    return {}\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.u-tag {\r\n  box-sizing: border-box;\r\n  align-items: center;\r\n  border-radius: 3px;\r\n  display: inline-block;\r\n  line-height: 1;\r\n}\r\n.u-size-mini {\r\n  font-size: 10px;\r\n  padding: 3px 6px;\r\n}\r\n.u-mode-plain-error {\r\n  background-color: #ffffff;\r\n  color: #fa3534;\r\n  border: 1px solid #fa3534;\r\n}\r\n.u-mode-light-error {\r\n  background-color: #fef0f0;\r\n  color: #fa3534;\r\n  border: 1px solid #fab6b6;\r\n}\r\n</style>\r\n"]}]}