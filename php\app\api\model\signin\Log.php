<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\api\model\signin;

use app\api\service\User as UserService;
use app\common\model\signin\Log as LogModel;
use cores\exception\BaseException;

/**
 * 模型类: 每日签到日志记录
 * Class Log
 * @package app\api\model\signin
 */
class Log extends LogModel
{
    /**
     * 隐藏字段
     * @var array
     */
    protected $hidden = [
        'store_id'
    ];

    /**
     * 获取近期的签到记录 (上个月到现在)
     * @return array
     * @throws BaseException
     */
    public function getLastMonthDateList(): array
    {
        return $this->getDateList($this->getLastMonthTime());
    }

    /**
     * 获取近期的签到记录 (本月到现在)
     * @return array
     * @throws BaseException
     */
    public function getCurrentMonthDateList(): array
    {
        return $this->getDateList($this->getCurrentMonthTime());
    }

    /**
     * 获取指定开始时间的签到记录 (仅返回日期)
     * @param int $startTime
     * @return array
     * @throws BaseException
     */
    private function getDateList(int $startTime): array
    {
        // 当前用户ID
        $userId = UserService::getCurrentLoginUserId();
        // 获取列表数据
        $dateArr = $this->where('user_id', '=', $userId)
            ->where('create_time', '>=', $startTime)
            ->order(['create_time' => 'asc'])
            ->column('create_time');
        // 格式化日期
        return \array_map(static function ($value) {
            return \date('Ymd', $value);
        }, $dateArr);
    }

//    /**
//     * 获取当前用户昨日是否签到
//     * @return bool
//     * @throws BaseException
//     */
//    public function checkSigninYesterday(): bool
//    {
//        return $this->checkSigninByTime($this->getYesterdayTime(), $this->getTodayTime());
//    }

    /**
     * 获取当前用户今日是否签到
     * @return bool
     * @throws BaseException
     */
    public function checkSigninToday(): bool
    {
        return $this->checkSigninByTime($this->getTodayTime(), $this->getTomorrowTime());
    }

    /**
     * 获取当前用户昨日和今日是否签到
     * @return bool
     * @throws BaseException
     */
    public function checkSigninYesterdayAndToday(): bool
    {
        return $this->checkSigninByTime($this->getYesterdayTime(), $this->getTomorrowTime());
    }

    /**
     * 获取当前用户指定时间是否签到
     * @param int $startTime
     * @param int $endTime
     * @return bool
     * @throws BaseException
     */
    private function checkSigninByTime(int $startTime, int $endTime): bool
    {
        // 当前用户ID
        $userId = UserService::getCurrentLoginUserId();
        // 查询是否存在
        return (bool)$this->where('user_id', '=', $userId)
            ->where('create_time', '>=', $startTime)
            ->where('create_time', '<', $endTime)
            ->value($this->getPk());
    }

    /**
     * 获取当月累计签到数
     * @return int
     * @throws BaseException
     */
    public function getCurrentMonthCount(): int
    {
        // 当前用户ID
        $userId = UserService::getCurrentLoginUserId();
        // 获取本月签到总数
        return $this->where('user_id', '=', $userId)
            ->where('create_time', '>=', $this->getCurrentMonthTime())
            ->count();
    }

    /**
     * 获取今日0点时间戳
     * @return false|int
     */
    public function getTodayTime()
    {
        return \strtotime(\date('Y-m-d'));
    }

    /**
     * 获取昨日0点时间戳
     * @return false|int
     */
    public function getYesterdayTime()
    {
        return \strtotime(\date('Y-m-d', \strtotime('-1 day')));
    }

    /**
     * 获取明日0点时间戳
     * @return false|int
     */
    public function getTomorrowTime()
    {
        return \strtotime(\date('Y-m-d', \strtotime('+1 day')));
    }

    /**
     * 获取当月1号的时间戳
     * @return false|int
     */
    private function getCurrentMonthTime()
    {
        return \strtotime(\date('Y-m-01'));
    }

    /**
     * 获取上个月1号的时间戳
     * @return false|int
     */
    private function getLastMonthTime()
    {
        return \strtotime(\date('Y-m-01', \strtotime('last month')));
    }

    /**
     * 获取日志明细列表
     * @return \think\Paginator
     * @throws BaseException
     * @throws \think\db\exception\DbException
     */
    public function getList(): \think\Paginator
    {
        // 当前用户ID
        $userId = UserService::getCurrentLoginUserId();
        // 获取列表数据
        return $this->where('user_id', '=', $userId)
            ->order(['create_time' => 'desc'])
            ->paginate(15);
    }
}