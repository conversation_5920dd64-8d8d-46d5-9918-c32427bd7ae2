<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2023 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\common\model\bargain;

use cores\BaseModel;
use think\model\relation\BelongsTo;

/**
 * 砍价任务助力记录模型
 * Class TaskHelp
 * @package app\common\model\bargain
 */
class TaskHelp extends BaseModel
{
    // 定义表名
    protected $name = 'bargain_task_help';

    // 定义主键
    protected $pk = 'help_id';

    protected $updateTime = false;

    /**
     * 关联用户表
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        $module = self::getCalledModule();
        return $this->BelongsTo("app\\{$module}\\model\\User")->field(['user_id', 'nick_name', 'avatar_id']);
    }
}