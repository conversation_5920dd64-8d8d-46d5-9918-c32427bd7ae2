<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\store\controller\points\mall;

use think\response\Json;
use app\store\controller\Controller;
use app\store\model\points\mall\Range as RangeModel;

/**
 * 控制器: 积分商城-积分范围管理
 * Class Range
 * @package app\store\controller\points\mall
 */
class Range extends Controller
{
    /**
     * 积分范围列表
     * @return Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function list(): Json
    {
        $model = new RangeModel;
        $list = $model->getList();
        return $this->renderSuccess(compact('list'));
    }

    /**
     * 添加积分范围
     * @return Json
     */
    public function add(): Json
    {
        // 新增记录
        $model = new RangeModel;
        if ($model->add($this->postForm())) {
            return $this->renderSuccess('添加成功');
        }
        return $this->renderError($model->getError() ?: '添加失败');
    }

    /**
     * 编辑积分范围
     * @param int $rangeId 范围ID
     * @return Json
     */
    public function edit(int $rangeId): Json
    {
        // 记录详情
        $model = RangeModel::detail($rangeId);
        // 更新记录
        if ($model->edit($this->postForm())) {
            return $this->renderSuccess('更新成功');
        }
        return $this->renderError($model->getError() ?: '更新失败');
    }

    /**
     * 删除积分范围
     * @param int $rangeId 范围ID
     * @return Json
     */
    public function delete(int $rangeId): Json
    {
        $model = RangeModel::detail($rangeId);
        if (!$model->setDelete()) {
            return $this->renderError($model->getError() ?: '删除失败');
        }
        return $this->renderSuccess('删除成功');
    }
}
