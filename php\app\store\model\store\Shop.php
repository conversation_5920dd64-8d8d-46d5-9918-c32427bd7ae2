<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2023 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\store\model\store;

use Lvht\GeoHash;
use app\common\model\store\Shop as ShopModel;

/**
 * 商家门店模型
 * Class Shop
 * @package app\store\model\store
 */
class Shop extends ShopModel
{
    /**
     * 获取列表数据
     * @param array $param
     * @return \think\Paginator
     * @throws \think\db\exception\DbException
     */
    public function getList(array $param = []): \think\Paginator
    {
        return $this->with(['logoImage'])
            ->where($this->getFilter($param))
            ->where('is_delete', '=', 0)
            ->order(['sort' => 'asc', $this->getPk()])
            ->paginate(15);
    }

    /**
     * 获取所有门店列表
     * @param array $param
     * @return \think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function getAll(array $param = []): \think\Collection
    {
        $model = new static;
        return $model->with(['logoImage'])
            ->where($model->getFilter($param))
            ->where('is_delete', '=', 0)
            ->select();
    }

    /**
     * 设置列表查询条件
     * @param array $param
     * @return array
     */
    private function getFilter(array $param = []): array
    {
        // 默认查询参数
        $params = $this->setQueryDefaultValue($param, [
            'search' => '',     // 搜索关键词: 门店名称/联系人/电话
            'isCheck' => -1,   // 是否支持自提核销(0否 1支持)
            'status' => '',     // 门店状态
        ]);
        // 检索查询条件
        $filter = [];
        // 是否支持自提核销
        $params['isCheck'] > -1 && $filter[] = ['is_check', '=', (int)$params['isCheck']];
        // 搜索关键词
        !empty($params['search']) && $filter[] = ['shop_name|linkman|phone', 'like', "%{$params['search']}%"];
        // 门店状态
        is_numeric($params['status']) && $filter[] = ['status', '=', (int)$params['status']];
        return $filter;
    }

    /**
     * 新增记录
     * @param array $data
     * @return bool
     */
    public function add(array $data): bool
    {
        if (!$this->validateForm($data)) {
            return false;
        }
        return $this->save($this->createData($data));
    }

    /**
     * 编辑记录
     * @param array $data
     * @return bool
     */
    public function edit(array $data): bool
    {
        if (!$this->validateForm($data)) {
            return false;
        }
        return $this->save($this->createData($data)) !== false;
    }

    /**
     * 软删除
     * @return bool
     */
    public function setDelete(): bool
    {
        return $this->save(['is_delete' => 1]);
    }

    /**
     * 创建数据
     * @param array $data
     * @return array
     */
    private function createData(array $data): array
    {
        // 格式化坐标信息
        $coordinate = explode(',', $data['coordinate']);
        $data['latitude'] = $coordinate[0];
        $data['longitude'] = $coordinate[1];
        // 生成geohash
        $data['geohash'] = (new Geohash)->encode($data['longitude'], $data['latitude']);
        // 省市区ID
        [$data['province_id'], $data['city_id'], $data['region_id']] = $data['cascader'];
        // 当前商城ID
        $data['store_id'] = self::$storeId;
        return $data;
    }

    /**
     * 表单验证
     * @param array $data
     * @return bool
     */
    private function validateForm(array $data): bool
    {
        if (empty($data['logo_image_id'])) {
            $this->error = '请选择门店logo';
            return false;
        }
        return true;
    }
}
