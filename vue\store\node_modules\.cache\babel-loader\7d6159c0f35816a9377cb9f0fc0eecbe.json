{"remainingRequest": "D:\\2025\\gaodux\\gaodux5\\vue\\store\\node_modules\\babel-loader\\lib\\index.js!D:\\2025\\gaodux\\gaodux5\\vue\\store\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--6!D:\\2025\\gaodux\\gaodux5\\vue\\store\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\2025\\gaodux\\gaodux5\\vue\\store\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025\\gaodux\\gaodux5\\vue\\store\\src\\views\\page\\modules\\phone\\modules\\UTag.vue?vue&type=template&id=15d8886c&scoped=true&", "dependencies": [{"path": "D:\\2025\\gaodux\\gaodux5\\vue\\store\\src\\views\\page\\modules\\phone\\modules\\UTag.vue", "mtime": 1656432000000}, {"path": "D:\\2025\\gaodux\\gaodux5\\vue\\store\\babel.config.js", "mtime": 1656432000000}, {"path": "D:\\2025\\gaodux\\gaodux5\\vue\\store\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751804606056}, {"path": "D:\\2025\\gaodux\\gaodux5\\vue\\store\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751804606056}, {"path": "D:\\2025\\gaodux\\gaodux5\\vue\\store\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751804605661}, {"path": "D:\\2025\\gaodux\\gaodux5\\vue\\store\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1751804675994}, {"path": "D:\\2025\\gaodux\\gaodux5\\vue\\store\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751804606056}, {"path": "D:\\2025\\gaodux\\gaodux5\\vue\\store\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751804675994}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:dmFyIHJlbmRlciA9IGZ1bmN0aW9uIHJlbmRlcigpIHsKICB2YXIgX3ZtID0gdGhpcywKICAgIF9jID0gX3ZtLl9zZWxmLl9jOwogIHJldHVybiBfYygiZGl2IiwgewogICAgc3RhdGljQ2xhc3M6ICJ1LXRhZyIsCiAgICBjbGFzczogWyJ1LXNpemUtIi5jb25jYXQoX3ZtLnNpemUpLCAidS1tb2RlLSIuY29uY2F0KF92bS5tb2RlLCAiLWVycm9yIildCiAgfSwgW19jKCJzcGFuIiwgW192bS5fdihfdm0uX3MoX3ZtLnRleHQpKV0pXSk7Cn07CnZhciBzdGF0aWNSZW5kZXJGbnMgPSBbXTsKcmVuZGVyLl93aXRoU3RyaXBwZWQgPSB0cnVlOwpleHBvcnQgeyByZW5kZXIsIHN0YXRpY1JlbmRlckZucyB9Ow=="}, {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "class", "size", "mode", "_v", "_s", "text", "staticRenderFns", "_withStripped"], "sources": ["D:/2025/gaodux/gaodux5/vue/store/src/views/page/modules/phone/modules/UTag.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"div\",\n    {\n      staticClass: \"u-tag\",\n      class: [`u-size-${_vm.size}`, `u-mode-${_vm.mode}-error`],\n    },\n    [_c(\"span\", [_vm._v(_vm._s(_vm.text))])]\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAM,GAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,KAAK,EACL;IACEE,WAAW,EAAE,OAAO;IACpBC,KAAK,EAAE,kBAAWJ,GAAG,CAACK,IAAI,oBAAcL,GAAG,CAACM,IAAI;EAClD,CAAC,EACD,CAACL,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACO,EAAE,CAACP,GAAG,CAACQ,EAAE,CAACR,GAAG,CAACS,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CACzC;AACH,CAAC;AACD,IAAIC,eAAe,GAAG,EAAE;AACxBX,MAAM,CAACY,aAAa,GAAG,IAAI;AAE3B,SAASZ,MAAM,EAAEW,eAAe"}]}