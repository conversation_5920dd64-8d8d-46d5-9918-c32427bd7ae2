<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2023 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\common\library\eOrder\provider;

use cores\exception\BaseException;
use cores\traits\ErrorTrait;

/**
 * 电子面单驱动基类
 * Class Driver
 * @package app\common\library\eOrder\provider\driver
 */
abstract class Driver
{
    use ErrorTrait;

    /**
     * 驱动句柄
     * @var Driver
     */
    protected $handler = null;

    /**
     * api配置参数
     * @var array
     */
    protected array $options = [
        'kdniao' => [
            'eBusinessID' => '',
            'apiKey' => ''
        ],
        'kd100' => [
            'key' => '',
            'secret' => '',
        ]
    ];

    /**
     * 电子面单模板信息
     * @var array
     */
    protected array $eleTemplate = [
        // 快递公司编码详细编码参考《快递鸟接口支持快递公司编码.xlsx》
        'shipperCode' => 'EMS',
        // 电子面单客户号
        'customerName' => '',
        // 电子面单密码
        'customerPwd' => '',
        // 网点编码
        'sendSite' => '',
        // 月结号(选填)
        'monthCode' => '',
        // 邮费支付方式
        'payType' => 'SHIPPER',
    ];

    /**
     * 发货订单信息
     * @var array
     */
    protected array $deliverOrder = [
        // 订单编号,不可重复提交
        'orderCode' => '',
        // 包裹总重量kg
        'weight' => 1.0,
        // 包裹总体积m3
        // 'volume' => 0.0,
        // 包裹数
        'quantity' => 1,
        //是 否通知快递员上门揽件  0通知 1不通知
        'isNotice' => 1,
        // 商品信息
        'commodity' => [
            [
                'goodsName' => '鞋子',   // 商品名称
                'goodsQuantity' => 1,    // 商品数量
                'goodsWeight' => 1.0     // 商品重量kg
            ]
        ],
    ];

    /**
     * 发件人信息
     * @var array
     */
    protected array $sender = [
        'name' => '发件人姓名',
        'mobile' => '1350000000',
        'provinceName' => '江苏省',
        'cityName' => '南京市',
        'expAreaName' => '秦淮区',
        'address' => '秦虹南路176号'
    ];

    /**
     * 收件人信息
     * @var array
     */
    protected array $receiver = [
        'name' => '陈翔',
        'mobile' => '1350000000',
        'provinceName' => '江苏省',
        'cityName' => '南京市',
        'expAreaName' => '秦淮区',
        'address' => '秦虹南路176号'
    ];

    /**
     * 获取电子面单内容 (HTML格式)
     * @return array
     */
    abstract function handle(): array;

    /**
     * 设置api配置参数
     * @param array $options 配置信息
     * @return static|null
     */
    public function setOptions(array $options): ?Driver
    {
        $this->options = $options;
        return $this;
    }

    /**
     * 设置电子面单模板信息
     * @param array $data 配置信息
     * @return static|null
     */
    public function setEleTemplate(array $data): ?Driver
    {
        $this->eleTemplate = $data;
        return $this;
    }

    /**
     * 设置发货单信息
     * @param array $data 配置信息
     * @return static|null
     */
    public function setDeliverOrder(array $data): ?Driver
    {
        $this->deliverOrder = $data;
        return $this;
    }

    /**
     * 设置发件人信息
     * @param array $data 配置信息
     * @return static|null
     */
    public function setSender(array $data): ?Driver
    {
        $this->sender = $data;
        return $this;
    }

    /**
     * 设置收件人信息
     * @param array $data 配置信息
     * @return static|null
     */
    public function setReceiver(array $data): ?Driver
    {
        $this->receiver = $data;
        return $this;
    }

    /**
     * post请求
     * @param string $url
     * @param array $data
     * @return bool|string
     * @throws BaseException
     */
    protected function curlPost(string $url, array $data)
    {
        $header = ['Content-type:application/x-www-form-urlencoded'];
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        $content = curl_exec($ch);
        if (curl_errno($ch)) {
            throwError(curl_error($ch));
        }
        curl_close($ch);
        return $content;
    }
}