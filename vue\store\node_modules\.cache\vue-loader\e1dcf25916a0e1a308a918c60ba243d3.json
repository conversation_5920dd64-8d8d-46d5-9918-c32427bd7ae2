{"remainingRequest": "D:\\2025\\gaodux\\gaodux5\\vue\\store\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025\\gaodux\\gaodux5\\vue\\store\\src\\views\\page\\modules\\editor\\modules\\HotZoneModal\\HotZoneModal.vue?vue&type=style&index=0&id=6a1f1b3a&lang=less&scoped=true&", "dependencies": [{"path": "D:\\2025\\gaodux\\gaodux5\\vue\\store\\src\\views\\page\\modules\\editor\\modules\\HotZoneModal\\HotZoneModal.vue", "mtime": 1677427200000}, {"path": "D:\\2025\\gaodux\\gaodux5\\vue\\store\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1751804674603}, {"path": "D:\\2025\\gaodux\\gaodux5\\vue\\store\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1751804675994}, {"path": "D:\\2025\\gaodux\\gaodux5\\vue\\store\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1751804675777}, {"path": "D:\\2025\\gaodux\\gaodux5\\vue\\store\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1751804709616}, {"path": "D:\\2025\\gaodux\\gaodux5\\vue\\store\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751804606056}, {"path": "D:\\2025\\gaodux\\gaodux5\\vue\\store\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751804675994}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["HotZoneModal.vue"], "names": [], "mappings": ";AAkIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "HotZoneModal.vue", "sourceRoot": "src/views/page/modules/editor/modules/HotZoneModal", "sourcesContent": ["<template>\r\n  <a-modal\r\n    class=\"noborder\"\r\n    :title=\"title\"\r\n    :width=\"804\"\r\n    :visible=\"visible\"\r\n    :isLoading=\"isLoading\"\r\n    :maskClosable=\"false\"\r\n    :destroyOnClose=\"true\"\r\n    @cancel=\"handleCancel\"\r\n  >\r\n    <div class=\"scroll-view\">\r\n      <div class=\"zone-body\" :style=\"{ height: `${imageH}px` }\">\r\n        <div class=\"bg-image\">\r\n          <img ref=\"image\" class=\"image\" :src=\"imageSrc\" @load=\"imageLoad\" alt />\r\n        </div>\r\n        <template v-if=\"imageW > 0 && imageH > 0\">\r\n          <vue-draggable-resizable\r\n            v-for=\"(item, index) in maps\"\r\n            :key=\"item.key\"\r\n            class-name=\"my-vdr\"\r\n            class-name-handle=\"my-handle\"\r\n            :minWidth=\"60\"\r\n            :minHeight=\"20\"\r\n            :x=\"item.left\"\r\n            :y=\"item.top\"\r\n            :w=\"item.width\"\r\n            :h=\"item.height\"\r\n            :parent=\"true\"\r\n            @dragstop=\"(left, top) => { item.left = left; item.top = top }\"\r\n            @resizestop=\"(left, top, width, height) => { item.left = left; item.top = top; item.width = width; item.height = height }\"\r\n          >\r\n            <div class=\"title\">热区{{ index + 1 }}</div>\r\n            <div class=\"actions\">\r\n              <a-popconfirm title=\"您确定要删除该热区吗？\" @confirm=\"handleDelZone(index)\">\r\n                <a href=\"javascript:;\">删除</a>\r\n              </a-popconfirm>\r\n              <SLink2 v-model=\"item.link\" />\r\n            </div>\r\n          </vue-draggable-resizable>\r\n        </template>\r\n      </div>\r\n    </div>\r\n    <template slot=\"footer\">\r\n      <a-button key=\"back\" @click=\"handleAddZone\">添加新热区</a-button>\r\n      <a-button key=\"submit\" type=\"primary\" @click=\"handleSubmit\">保存</a-button>\r\n    </template>\r\n  </a-modal>\r\n</template>\r\n\r\n<script>\r\nimport _ from 'lodash'\r\nimport VueDraggableResizable from 'vue-draggable-resizable'\r\nimport { SLink2 } from '../'\r\n\r\n// 绘制热区组件\r\nexport default {\r\n  name: 'HotZoneModal',\r\n  model: {\r\n    prop: 'value',\r\n    event: 'change'\r\n  },\r\n  components: {\r\n    VueDraggableResizable,\r\n    SLink2\r\n  },\r\n  data () {\r\n    return {\r\n      // 对话框标题\r\n      title: '绘制热区',\r\n      // modal(对话框)是否可见\r\n      visible: false,\r\n      // loading状态\r\n      isLoading: false,\r\n      // 热区数据\r\n      maps: [],\r\n      // 背景图片地址\r\n      imageSrc: '',\r\n      // 背景图宽度\r\n      imageW: 0,\r\n      // 背景图高度\r\n      imageH: 0\r\n    }\r\n  },\r\n\r\n  methods: {\r\n\r\n    // 显示对话框\r\n    handle (maps, image) {\r\n      this.visible = true\r\n      this.maps = _.cloneDeep(maps)\r\n      this.imageSrc = image\r\n    },\r\n\r\n    // 图片加载事件\r\n    imageLoad (e) {\r\n      this.imageW = this.$refs.image.offsetWidth\r\n      this.imageH = this.$refs.image.offsetHeight\r\n    },\r\n\r\n    // 新增热区\r\n    handleAddZone () {\r\n      this.maps.push({ width: 100, height: 100, left: 0, top: 0, link: null, key: new Date().getTime() })\r\n    },\r\n\r\n    // 删除热区\r\n    handleDelZone (index) {\r\n      this.maps.splice(index, 1)\r\n    },\r\n\r\n    // 确认按钮\r\n    handleSubmit (e) {\r\n      this.$emit('handleSubmit', this.maps)\r\n      this.handleCancel()\r\n    },\r\n\r\n    // 关闭对话框事件\r\n    handleCancel () {\r\n      this.visible = false\r\n      this.imageSrc = ''\r\n      this.imageW = 0\r\n      this.imageH = 0\r\n      this.maps = []\r\n    },\r\n\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n@import '~ant-design-vue/es/style/themes/default.less';\r\n.ant-modal-root {\r\n  background: #ccc;\r\n  /deep/.ant-modal-body {\r\n    padding: 0 24px 20px 24px;\r\n  }\r\n  /deep/.ant-modal-footer {\r\n    padding-top: 0;\r\n  }\r\n}\r\n\r\n/deep/.ant-collapse-header {\r\n  padding: 14px 16px;\r\n  font-size: @font-size-base;\r\n  font-weight: 700;\r\n  color: #595961;\r\n}\r\n\r\n/deep/.ant-collapse-content-box {\r\n  padding-top: 0 !important;\r\n}\r\n\r\n/deep/.ant-collapse > .ant-collapse-item {\r\n  border-bottom: none;\r\n  margin-bottom: 15px;\r\n  &:last-child {\r\n    margin-bottom: 0;\r\n  }\r\n}\r\n\r\n.scroll-view {\r\n  max-height: 500px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.zone-body {\r\n  position: relative;\r\n  overflow: hidden;\r\n  width: 750px;\r\n  background: #ccc;\r\n  .bg-image {\r\n    position: absolute;\r\n    top: 0;\r\n    bottom: 0;\r\n    user-select: none;\r\n    width: 100%;\r\n    .image {\r\n      display: block;\r\n      width: 100%;\r\n    }\r\n  }\r\n}\r\n\r\n.my-vdr {\r\n  border: 1px solid red;\r\n  background: rgba(45, 140, 240, 0.6);\r\n  border: 1px solid #479bf4;\r\n  cursor: all-scroll;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  position: absolute;\r\n\r\n  &.active {\r\n    .title {\r\n      display: none;\r\n    }\r\n\r\n    .actions {\r\n      display: block;\r\n    }\r\n  }\r\n\r\n  .title {\r\n    display: block;\r\n    color: #fff;\r\n    font-size: 12px;\r\n    overflow: hidden;\r\n    white-space: nowrap;\r\n    user-select: none;\r\n  }\r\n\r\n  .actions {\r\n    display: none;\r\n    color: #fff;\r\n    font-size: 12px;\r\n    user-select: none;\r\n    a {\r\n      margin-right: 6px;\r\n      color: #fff;\r\n    }\r\n\r\n    a:last-child {\r\n      margin-right: 0;\r\n    }\r\n  }\r\n}\r\n\r\n/deep/.my-handle {\r\n  position: absolute;\r\n  height: 10px;\r\n  width: 10px;\r\n  transition: all 300ms linear;\r\n  background: #fff;\r\n  border: 1px solid #479bf4;\r\n  border-radius: 50%;\r\n}\r\n\r\n/deep/.my-handle-tl {\r\n  top: -6px;\r\n  left: -6px;\r\n  cursor: nw-resize;\r\n}\r\n\r\n/deep/.my-handle-tm {\r\n  top: -6px;\r\n  left: 50%;\r\n  margin-left: -7px;\r\n  cursor: n-resize;\r\n}\r\n\r\n/deep/.my-handle-tr {\r\n  top: -6px;\r\n  right: -6px;\r\n  cursor: ne-resize;\r\n}\r\n\r\n/deep/.my-handle-ml {\r\n  top: 50%;\r\n  margin-top: -7px;\r\n  left: -6px;\r\n  cursor: w-resize;\r\n}\r\n\r\n/deep/.my-handle-mr {\r\n  top: 50%;\r\n  margin-top: -7px;\r\n  right: -6px;\r\n  cursor: e-resize;\r\n}\r\n\r\n/deep/.my-handle-bl {\r\n  bottom: -6px;\r\n  left: -6px;\r\n  cursor: sw-resize;\r\n}\r\n\r\n/deep/.my-handle-bm {\r\n  bottom: -6px;\r\n  left: 50%;\r\n  margin-left: -7px;\r\n  cursor: s-resize;\r\n}\r\n\r\n/deep/.my-handle-br {\r\n  bottom: -6px;\r\n  right: -6px;\r\n  cursor: se-resize;\r\n}\r\n</style>\r\n"]}]}