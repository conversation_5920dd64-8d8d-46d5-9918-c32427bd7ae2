<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2023 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\api\model\store;

use app\common\model\store\Module as ModuleModel;

/**
 * 商家功能模块模型
 * Class Module
 * @package app\api\model\store
 */
class Module extends ModuleModel
{
    // 当前商城开启的功能模块
    private static array $modules = [];

    /**
     * 获取当前商城开启的功能模块
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function getCurrentModules(): array
    {
        empty(self::$modules) && self::$modules = parent::getModules();
        return self::$modules;
    }

    /**
     * 验证是否开启功能模块权限
     * @param string $moduleKey
     * @return bool
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function checkModuleKey(string $moduleKey): bool
    {
        return \in_array($moduleKey, self::getCurrentModules());
    }
}
