<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\common\enum\order;

use app\common\enum\EnumBasics;

/**
 * 枚举类：订单来源
 * Class OrderSource
 * @package app\common\enum\order
 */
class OrderSource extends EnumBasics
{
    // 普通订单
    const MAIN = 10;

    // 砍价订单
    const BARGAIN = 20;

    // 秒杀订单
    const SHARP = 30;

    // 拼团订单
    const GROUPON = 40;

    // 积分商城订单
    const POINTS_MALL = 50;

    /**
     * 获取枚举数据
     * @return array
     */
    public static function data(): array
    {
        return [
            self::MAIN => [
                'name' => '普通订单',
                'value' => self::MAIN
            ],
            self::BARGAIN => [
                'name' => '砍价订单',
                'value' => self::BARGAIN
            ],
            self::SHARP => [
                'name' => '秒杀订单',
                'value' => self::SHARP
            ],
            self::GROUPON => [
                'name' => '拼团订单',
                'value' => self::GROUPON
            ],
            self::POINTS_MALL => [
                'name' => '积分商城订单',
                'value' => self::POINTS_MALL
            ]
        ];
    }
}