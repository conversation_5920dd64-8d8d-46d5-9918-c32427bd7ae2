<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2025 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\common\model\signin;

use think\facade\Cache;
use app\common\library\helper;
use cores\BaseModel;

/**
 * 模型类: 每日签到设置表
 * Class Setting
 * @package app\common\model\signin
 */
class Setting extends BaseModel
{
    // 定义表名
    protected $name = 'signin_setting';

    protected $createTime = false;

    /**
     * 获取器: 转义数组格式
     * @param $value
     * @return array
     */
    public function getValuesAttr($value): array
    {
        return helper::jsonDecode($value);
    }

    /**
     * 修改器: 转义成json格式
     * @param $value
     * @return string
     */
    public function setValuesAttr($value): string
    {
        return helper::jsonEncode($value);
    }

    /**
     * 获取指定项设置
     * @param string $key
     * @param int|null $storeId
     * @return array|mixed
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function getItem(string $key, int $storeId = null)
    {
        $data = static::getAll($storeId);
        return $data[$key] ?? [];
    }

    /**
     * 获取指定项设置: 规则设置
     * @param int|null $storeId
     * @return array|mixed
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function getItemOptions(int $storeId = null)
    {
        return static::getItem('options', $storeId);
    }

    /**
     * 获取指定项设置: 是否开启每日签到活动
     * @param int|null $storeId
     * @return array|mixed
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function getEnabled(int $storeId = null)
    {
        return static::getItem('options', $storeId)['enabled'];
    }

    /**
     * 获取全部设置
     * @param int|null $storeId
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function getAll(int $storeId = null): array
    {
        $model = new static;
        is_null($storeId) && $storeId = $model::$storeId;
        if (!$data = Cache::get("signin_setting_{$storeId}")) {
            // 获取全部设置
            $setting = $model->getList($storeId);
            $data = $setting->isEmpty() ? [] : helper::arrayColumn2Key($setting->toArray(), 'key');
            // 写入缓存中
            Cache::tag('cache')->set("signin_setting_{$storeId}", $data);
        }
        // 重组setting缓存数据 (多维)
        $mixed = static::reorganize($model->defaultData(), $data, $type = 'cache');
        return static::getValues($mixed, true);
    }

    /**
     * 获取设置列表
     * @param int $storeId
     * @return \think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    private function getList(int $storeId): \think\Collection
    {
        return $this->where('store_id', '=', $storeId)->select();
    }

    /**
     * 获取设置项信息
     * @param string $key
     * @return static|array|null
     */
    public static function detail(string $key)
    {
        return static::get(compact('key'));
    }

    /**
     * 默认配置
     * @return array
     */
    public function defaultData(): array
    {
        return [
            'page' => [
                'key' => 'page',
                'describe' => '页面设置',
                'values' => [
                    // 页面头部背景图
                    'bgImage' => \base_url() . 'assets/store/img/signin/backdrop.png',
                    // 页面背景颜色
                    'bgColor' => '#ff615e',
                    // 分享标题
                    'shareTitle' => '签到送好礼',
                    // 分享描述
                    'shareDescribe' => '天天签到享好礼',
                ]
            ],
            'options' => [
                'key' => 'options',
                'describe' => '规则设置',
                'values' => [
                    // 是否开启每日签到活动
                    'enabled' => true,
                    // 每日签到固定奖励
                    'fixedReward' => ['points' => 1],
                    //    // 递增签到额外奖励
                    //    'incremental' => [
                    //        'enabled' => false,
                    //        'points' => ['enabled' => true, 'value' => 2, 'endDays' => 5]
                    //    ],
                    // 连续签到额外奖励 (满足一定天数)
                    'continuous' => [
                        'enabled' => false,
                        'ruleList' => [
                            [
                                'days' => 3,
                                'points' => ['enabled' => true, 'value' => 2],
                                'money' => ['enabled' => false, 'value' => 0.1],
                                'coupon' => ['enabled' => false, 'couponIds' => []],
                            ],
                            [
                                'days' => 7,
                                'points' => ['enabled' => true, 'value' => 3],
                                'money' => ['enabled' => false, 'value' => 0.1],
                                'coupon' => ['enabled' => false, 'couponIds' => []],
                            ]
                        ],
                        // 连续签到最大天数后 repeat重新计算  end不再赠送
                        'finishMax' => 'repeat'
                    ],
                    // 累计签到奖励 (每月)
                    'cumulative' => [
                        'enabled' => false,
                        'dateUnit' => 'month',
                        'ruleList' => [
                            [
                                'days' => 10,
                                'points' => ['enabled' => true, 'value' => 5],
                                'money' => ['enabled' => false, 'value' => 0.1],
                                'coupon' => ['enabled' => false, 'couponIds' => []]
                            ],
                            [
                                'days' => 20,
                                'points' => ['enabled' => true, 'value' => 5],
                                'money' => ['enabled' => false, 'value' => 0.1],
                                'coupon' => ['enabled' => false, 'couponIds' => []]
                            ]
                        ]
                    ],
                    // 签到规则
                    'rulesDesc' => " 1. 每日固定签到奖励：赠送x积分
 2. 连续签到奖励：连续签到3天额外赠送x积分、连续签到7天额外赠送x积分，若断签则重新计算连续签到天数
 3. 累计签到奖励(每月)： 累计签到10天额外赠送x积分、累计签到20天额外赠送x积分
 4. 具体每日签到福利可详见活动主页提示
 "
                ]
            ]
        ];
    }
}