<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2025 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\store\controller\points\mall;

use think\response\Json;
use app\store\controller\Controller;
use app\store\model\points\mall\Goods as PMGoodsModel;
use cores\exception\BaseException;

/**
 * 控制器: 积分商城-积分商品管理
 * Class Goods
 * @package app\store\controller\points\mall
 */
class Goods extends Controller
{
    /**
     * 积分商品列表
     * @return Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function list(): Json
    {
        $model = new PMGoodsModel;
        $list = $model->getList($this->request->param());
        return $this->renderSuccess(compact('list'));
    }

    /**
     * 积分商品详情
     * @param int $pmGoodsId 积分商品ID
     * @return Json
     * @throws BaseException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function detail(int $pmGoodsId): Json
    {
        // 获取商品详情
        $model = new PMGoodsModel;
        $detail = $model->getDetail($pmGoodsId);
        return $this->renderSuccess(compact('detail'));
    }

    /**
     * 一键添加积分商品
     * @return Json
     * @throws BaseException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function add(): Json
    {
        $model = new PMGoodsModel;
        if (!$model->add($this->postForm())) {
            return $this->renderError($model->getError());
        }
        return $this->renderSuccess('添加成功，请在编辑页设置积分商品价格');
    }

    /**
     * 编辑积分商品
     * @param int $pmGoodsId 积分商品ID
     * @return Json
     * @throws BaseException
     */
    public function edit(int $pmGoodsId): Json
    {
        $model = PMGoodsModel::getFl($pmGoodsId,'points-mall-goods1');
        if ($model->edit($this->postForm())) {
            return $this->renderSuccess('更新成功');
        }
        return $this->renderError($model->getError() ?: '更新失败');
    }

    /**
     * 删除积分商品
     * @param int $pmGoodsId 积分商品ID
     * @return Json
     * @throws BaseException
     */
    public function delete(int $pmGoodsId): Json
    {
        $model = PMGoodsModel::getFl($pmGoodsId,'points-mall-goods1');
        if (!$model->setDelete()) {
            return $this->renderError($model->getError() ?: '删除失败');
        }
        return $this->renderSuccess('删除成功');
    }
}