<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2025 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\store\model\points\mall;

use app\store\model\Goods as GoodsModel;
use app\store\model\GoodsSpecRel as GoodsSpecRelModel;
use app\store\model\points\mall\GoodsSku as PMGoodsSkuModel;
use app\common\model\points\mall\Goods as PMGoodsModel;
use app\common\enum\goods\SpecType as SpecTypeEnum;
use app\common\enum\points\mall\Status as PMGoodsStatusEnum;
use app\common\library\helper;
use cores\exception\BaseException;

/**
 * 模型类: 积分商城-积分商品
 * Class Goods
 * @package app\store\model\points\mall
 */
class Goods extends PMGoodsModel
{
    const EVENT_ADD = 'add';
    const EVENT_EDIT = 'edit';

    /**
     * 获取列表数据
     * @param array $param
     * @return mixed
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getList(array $param = [])
    {
        // 查询参数
        $params = $this->setQueryDefaultValue($param, [
            'search' => '',       // 商品名称
            'categoryId' => 0,    // 商品分类ID
        ]);
        // 检索查询条件
        $filter = [];
        // 商品名称
        !empty($params['search']) && $filter[] = ['goods.goods_name', 'like', "%{$params['search']}%"];
        // 商品分类ID
        $params['categoryId'] > 0 && $filter[] = ["{$this->alias}.category_id", '=', $params['categoryId']];
        // 获取商品列表
        $list = $this->setBaseQuery($this->alias, [['goods', 'goods_id']])
            ->where($filter)
            ->where("{$this->alias}.is_delete", '=', 0)
            ->order(["{$this->alias}.sort" => 'asc', "{$this->alias}.create_time" => 'desc'])
            ->paginate(15);
        // 设置商品数据
        return !$list->isEmpty() ? $this->setGoodsListData($list, true) : $list;
    }

    /**
     * 获取积分商品详情
     * @param int $pmGoodsId 积分商品ID
     * @return Goods|array|null
     * @throws BaseException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getDetail(int $pmGoodsId)
    {
        // 获取商品记录
        $goodsInfo = static::getFl($pmGoodsId,'points-mall-goods2');
        // 商品多规格处理
        if ($goodsInfo['spec_type'] == SpecTypeEnum::MULTI) {
            // 积分商品SKU填充主商品参数(商品价格、商品sku编码)
            $goodsInfo['skuList'] = PMGoodsSkuModel::getCommonSkuList($goodsInfo['skuList'], $goodsInfo['goods_id']);
            // 商品规格属性列表
            $goodsInfo['specList'] = GoodsSpecRelModel::getSpecList($goodsInfo['goods_id']);
        }
        return $goodsInfo;
    }

    /**
     * 添加积分商品
     * @param array $data
     * @return bool
     * @throws BaseException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function add(array $data): bool
    {
        // 验证主商品能否被添加为积分商品
        if (!$this->validateGoodsId((int)$data['goods_id'])) {
            return false;
        }
        // 获取主商品基本信息
        $goodsInfo = (new GoodsModel)->getBasic((int)$data['goods_id']);
        // 记录商品ID、规格类型、积分商城价格
        $data['goods_id'] = $goodsInfo['goods_id'];
        $data['spec_type'] = $goodsInfo['spec_type'];
        $data['points_price'] = 0;
        $data['paid_price'] = 0;
        $data['status'] = PMGoodsStatusEnum::OFF_SALE; // 因未设置价格, 所以新增时默认是下架
        // 创建积分商品数据
        $data = $this->createData($data, self::EVENT_ADD);
        // 事务处理
        $this->transaction(function () use ($data) {
            // 添加商品
            $this->save($data);
            // 更新商品sku信息
            PMGoodsSkuModel::add((int)$this['pm_goods_id'], $this['spec_type'], $data['newSkuList']);
        });
        return true;
    }

    /**
     * 编辑商品
     * @param array $data
     * @return bool
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function edit(array $data): bool
    {
        // 记录商品ID和规格类型
        $data['goods_id'] = $this['goods_id'];
        $data['spec_type'] = $this['spec_type'];
        // 创建商品数据
        $data = $this->createData($data, self::EVENT_EDIT);
        // 事务处理
        $this->transaction(function () use ($data) {
            // 更新商品信息
            $this->save($data);
            // 更新商品sku信息
            PMGoodsSkuModel::edit((int)$this['pm_goods_id'], $this['spec_type'], $data['newSkuList']);
        });
        return true;
    }

    /**
     * 获取当前商品总数
     * @param array $where
     * @return int
     */
    public static function getGoodsTotal(array $where = []): int
    {
        return (new static)->where($where)->where('is_delete', '=', 0)->count();
    }

    /**
     * 创建商品数据
     * @param array $data
     * @param string $event
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    private function createData(array $data, string $event = self::EVENT_ADD): array
    {
        // 默认数据
        $data = \array_merge($data, [
            'newSkuList' => [],
            'store_id' => self::$storeId,
        ]);
        // 规格和sku数据处理
        $data['newSkuList'] = $this->getNewSkuList($data, $event);
        // 商品积分价格 最低和最高
        if ($data['spec_type'] == SpecTypeEnum::MULTI) {
            [$minItem, $maxItem] = PMGoodsSkuModel::getMinMaxSkuItem($data['newSkuList']);
            $data['points_price_min'] = $minItem['points_price'];
            $data['points_price_max'] = $maxItem['points_price'];
            $data['paid_price_min'] = $minItem['paid_price'] ?: 0;
            $data['paid_price_max'] = $maxItem['paid_price'] ?: 0;
        } elseif ($data['spec_type'] == SpecTypeEnum::SINGLE) {
            $data['points_price_min'] = $data['points_price_max'] = $data['points_price'];
            $data['paid_price_min'] = $data['paid_price_max'] = $data['paid_price'] ?? 0;
        }
        return $data;
    }

    /**
     * 生成新的SKU列表数据
     * @param array $data
     * @param string $event
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    private function getNewSkuList(array $data, string $event = self::EVENT_ADD): array
    {
        if ($event === self::EVENT_ADD) {
            // 生成默认的skuList
            if ($data['spec_type'] == SpecTypeEnum::MULTI) {
                return PMGoodsSkuModel::getDefaultSkuList($data['goods_id']);
            }
            return helper::pick($data, ['points_price', 'paid_price']);
        }
        return $this->getNewSkuListByEdit($data, $event);
    }

    /**
     * 生成新的SKU列表数据(用于编辑事件)
     * @param array $data
     * @param string $event
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    private function getNewSkuListByEdit(array $data, string $event = self::EVENT_ADD): array
    {
        // 规格和sku数据处理
        if ($data['spec_type'] == SpecTypeEnum::MULTI) {
            // 生成skuList ( 携带goods_sku_id )
            return PMGoodsSkuModel::getNewSkuList($data['goods_id'], $data['specData']['skuList']);
        } elseif ($data['spec_type'] == SpecTypeEnum::SINGLE) {
            // 生成skuItem
            return helper::pick($data, ['points_price', 'paid_price']);
        }
        return [];
    }

    /**
     * 验证主商品能否被添加为积分商品
     * @param int $goodsId 主商品ID
     * @return bool
     */
    private function validateGoodsId(int $goodsId): bool
    {
        if (empty($goodsId)) {
            $this->error = '很抱歉，您还没有选择商品';
            return false;
        }
        // 验证是否存在积分商品
        if ($this->isExistGoodsId($goodsId)) {
            $this->error = '很抱歉，该商品已存在，无需重复添加';
            return false;
        }
        return true;
    }

    /**
     * 验证指定商品是否已添加积分商品
     * @param int $goodsId 主商品ID
     * @return bool
     */
    public static function isExistGoodsId(int $goodsId): bool
    {
        return (bool)(new static)->where('goods_id', '=', $goodsId)
            ->where('is_delete', '=', 0)
            ->value('pm_goods_id');
    }

    /**
     * 软删除
     * @return mixed
     */
    public function setDelete()
    {
        return $this->transaction(function () {
            // 删除所有的sku记录
            PMGoodsSkuModel::deleteAll(['pm_goods_id' => $this['pm_goods_id']]);
            // 标记当前积分商品已删除
            return $this->save(['is_delete' => 1]);
        });
    }
}