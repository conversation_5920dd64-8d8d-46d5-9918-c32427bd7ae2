<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2025 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\api\model\signin;

use app\api\service\User as UserService;
use app\api\model\signin\Log as SigninLogModel;
use app\common\library\helper;
use app\common\model\signin\Participant as ParticipantModel;
use cores\exception\BaseException;

/**
 * 模型类: 每日签到参与用户
 * Class Participant
 * @package app\api\model\signin
 */
class Participant extends ParticipantModel
{
    /**
     * 隐藏字段
     * @var array
     */
    protected $hidden = [
        'id',
        'store_id',
        'create_time',
        'update_time'
    ];

    /**
     * 获取参与用户信息
     * @return Participant|array|mixed|\think\Model|null
     * @throws BaseException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getDetailByUser()
    {
        $userId = UserService::getCurrentLoginUserId();
        return $this->where('user_id', '=', $userId)->find();
    }

    /**
     * 获取当前连续签到天数
     * @return int|mixed
     * @throws BaseException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getContinuousDays()
    {
        // 获取参与用户信息
        $detail = $this->getDetailByUser();
        if (empty($detail)) {
            return 0;
        }
        // 获取当前用户昨日是否签到
        $model = new SigninLogModel;
        if (!$model->checkSigninYesterdayAndToday()) {
            // 更新参与用户信息中的当前连续签到为0天
            static::updateContinuousDays(0);
            return 0;
        }
        // 获取当前连续签到天数
        return $detail['continuous_days'];
    }

    /**
     * 更新当前用户连续签到天数
     * @param int $days 指定天数
     * @return bool
     * @throws BaseException
     */
    public static function updateContinuousDays(int $days): bool
    {
        $userId = UserService::getCurrentLoginUserId();
        return static::updateOne(['continuous_days' => $days], ['user_id' => $userId]);
    }

    /**
     * 更新参与用户信息
     * @param array $reward 奖励内容 points actualCouponNum money
     * @param int $continuousDays 连续签到天数
     * @return bool
     * @throws BaseException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function updateRecord(array $reward, int $continuousDays): bool
    {
        // 获取参与用户信息
        $detail = $this->getDetailByUser();
        // 不存在则为新增
        $isAdded = empty($detail);
        // 更新或新增记录
        return ($isAdded ? (new static) : $detail)->save([
            'user_id' => UserService::getCurrentLoginUserId(),
            'first_time' => !$isAdded ? $detail->getData('first_time') : \time(),
            'last_time' => \time(),
            'total_days' => !$isAdded ? $detail['total_days'] + 1 : 1,
            'continuous_days' => $continuousDays,
            'max_continuous_days' => !$isAdded ? \max($detail['max_continuous_days'], $continuousDays) : $continuousDays,
            'points_num' => !$isAdded ? $detail['points_num'] + $reward['points'] : $reward['points'],
            'coupon_num' => !$isAdded ? $detail['coupon_num'] + $reward['actualCouponNum'] : $reward['actualCouponNum'],
            'money' => !$isAdded ? helper::bcadd($detail['money'], $reward['money']) : $reward['money'],
            'store_id' => static::$storeId,
        ]);
    }
}