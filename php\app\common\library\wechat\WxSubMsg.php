<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2023 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
namespace app\common\library\wechat;

use cores\exception\BaseException;

/**
 * 小程序订阅消息
 * Class WxSubMsg
 * @package app\common\library\wechat
 */
class WxSubMsg extends WxBase
{
    /**
     * 发送订阅消息
     * @param $param
     * @return bool
     * @throws \cores\exception\BaseException
     */
    public function sendTemplateMessage($param): bool
    {
        // 微信接口url
        $accessToken = $this->getAccessToken();
        $url = "https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token={$accessToken}";
        // 构建请求
        $params = [
            'touser' => $param['touser'],
            'template_id' => $param['template_id'],
            'page' => $param['page'],
            'data' => $param['data'],
        ];
        $result = $this->post($url, $this->jsonEncode($params));
        // 记录日志
        $describe = '发送订阅消息';
        log_record(compact('describe', 'url', 'params', 'result'));
        // 返回结果
        $response = $this->jsonDecode($result);
        if (!isset($response['errcode'])) {
            $this->error = 'not found errcode';
            return false;
        }
        if ($response['errcode'] != 0) {
            $this->error = $response['errmsg'];
            return false;
        }
        return true;
    }

    /**
     * 获取当前帐号下的模板列表
     * @throws \cores\exception\BaseException
     */
    public function getTemplateList()
    {
        // 微信接口url
        $accessToken = $this->getAccessToken();
        $url = "https://api.weixin.qq.com/wxaapi/newtmpl/gettemplate?access_token={$accessToken}";
        // 执行post请求
        $result = $this->get($url);
        // 记录日志
        log_record(['name' => '获取当前帐号下的订阅消息模板列表', 'url' => $url, 'result' => $result]);
        // 处理返回结果
        $response = $this->jsonDecode($result);
        if (!isset($response['errcode'])) {
            $this->error = 'not found errcode';
            return false;
        }
        if ($response['errcode'] != 0) {
            $this->error = $response['errmsg'];
            return false;
        }
        return $response;
    }

    /**
     * 添加订阅消息模板
     * [addTemplates 组合模板并添加至帐号下的个人模板库](订阅消息)
     * @param int $tid 模板标题id
     * @param array $kidList 模板关键词列表
     * @param string $sceneDesc 服务场景描述
     * @return bool
     * @throws \cores\exception\BaseException
     */
    public function addTemplate(int $tid, array $kidList, string $sceneDesc): bool
    {
        // 微信接口url
        $accessToken = $this->getAccessToken();
        $url = "https://api.weixin.qq.com/wxaapi/newtmpl/addtemplate?access_token={$accessToken}";
        // 构建请求
        $params = [
            'tid' => $tid,
            'kidList' => $kidList,
            'sceneDesc' => $sceneDesc,
        ];
        // 执行post请求
        $result = $this->post2($url, $params);
        // 记录日志
        log_record(['name' => '添加订阅消息模板', 'url' => $url, 'params' => $params, 'result' => $result]);
        // 处理返回结果
        $response = $this->jsonDecode($result);
        if (!isset($response['errcode'])) {
            $this->error = 'not found errcode';
            return false;
        }
        if ($response['errcode'] != 0) {
            $this->error = $response['errmsg'];
            return false;
        }
        return $response;
    }
}