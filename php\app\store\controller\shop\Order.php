<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2023 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\store\controller\shop;

use think\response\Json;
use app\store\controller\Controller;
use app\store\model\store\shop\Order as OrderModel;

/**
 * 订单核销记录
 * Class Order
 * @package app\store\controller\shop
 */
class Order extends Controller
{
    /**
     * 订单核销记录列表
     * @return Json
     */
    public function list(): Json
    {
        // 核销记录列表
        $model = new OrderModel;
        $list = $model->getList($this->request->param());
        return $this->renderSuccess(compact('list'));
    }
}
