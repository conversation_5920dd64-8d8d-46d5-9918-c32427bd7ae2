<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2025 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\common\model;

use cores\BaseModel;
use app\common\library\helper;
use think\model\relation\HasMany;
use think\model\relation\BelongsTo;

/**
 * 开屏推广活动模型
 * Class Promote
 * @package app\common\model
 */
class Promote extends BaseModel
{
    // 定义表名
    protected $name = 'promote';

    // 定义主键
    protected $pk = 'promote_id';

    /**
     * 关联评价图片表
     * @return HasMany
     */
    public function images(): HasMany
    {
        return $this->hasMany('PromoteImage')->order(['id']);
    }

    /**
     * 详情记录
     * @param int $promoteId
     * @param array $with
     * @return static|array|null
     */
    public static function detail(int $promoteId, array $with = [])
    {
        return static::get($promoteId, $with);
    }

    /**
     * 获取器：活动开始时间
     * @param $value
     * @return false|string
     */
    public function getStartTimeAttr($value)
    {
        return \format_time($value);
    }

    /**
     * 获取器：活动结束时间
     * @param $value
     * @return false|string
     */
    public function getEndTimeAttr($value)
    {
        return \format_time($value);
    }

    /**
     * 获取器：内容配置
     * @param $value
     * @return array
     */
    public function getContentConfigAttr($value): array
    {
        return helper::jsonDecode($value);
    }

    /**
     * 获取器：投放的页面
     * @param $value
     * @return array
     */
    public function getPagesAttr($value): array
    {
        return helper::jsonDecode($value);
    }

    /**
     * 修改器：内容配置
     * @param $value
     * @return string
     */
    public function setContentConfigAttr($value): string
    {
        return helper::jsonEncode($value);
    }

    /**
     * 修改器：投放的页面
     * @param $value
     * @return string
     */
    public function setPagesAttr($value): string
    {
        return helper::jsonEncode($value);
    }
}
