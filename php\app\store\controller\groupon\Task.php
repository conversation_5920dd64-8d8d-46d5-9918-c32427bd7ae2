<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2023 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\store\controller\groupon;

use think\response\Json;
use app\store\controller\Controller;
use app\store\model\groupon\Task as TaskModel;
use app\store\model\groupon\TaskUsers as TaskUsersModel;

/**
 * 拼团拼单管理
 * Class Task
 * @package app\store\controller\groupon
 */
class Task extends Controller
{
    /**
     * 获取列表记录
     * @return Json
     * @throws \think\db\exception\DbException
     */
    public function list(): Json
    {
        $model = new TaskModel;
        $list = $model->getList($this->request->param());
        return $this->renderSuccess(compact('list'));
    }

    /**
     * 拼单成员列表
     * @param int $taskId 拼单ID
     * @return Json
     * @throws \think\db\exception\DbException
     */
    public function users(int $taskId): Json
    {
        $model = new TaskUsersModel;
        $list = $model->getList($taskId);
        return $this->renderSuccess(compact('list'));
    }
}
