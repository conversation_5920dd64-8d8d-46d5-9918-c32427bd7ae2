<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2023 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\api\controller\bargain;

use think\response\Json;
use app\api\controller\Controller;
use app\api\model\Goods as GoodsModel;
use app\api\model\bargain\Active as ActiveModel;
use app\api\model\bargain\Setting as SettingModel;
use app\api\service\User as UserService;
use app\common\service\qrcode\bargain\Goods as GoodsPoster;

/**
 * 砍价活动管理
 * Class Active
 * @package app\api\controller\bargain
 */
class Active extends Controller
{
    /**
     * 砍价活动会场列表
     * @return Json
     * @throws \think\Exception
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function list(): Json
    {
        // 获取砍价活动会场列表
        $model = new ActiveModel;
        $list = $model->getHallList();
        return $this->renderSuccess(compact('list'));
    }

    /**
     * 砍价活动详情
     * @param int $activeId
     * @return Json
     * @throws \cores\exception\BaseException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function detail(int $activeId): Json
    {
        // 获取砍价活动详情
        $model = new ActiveModel;
        $active = $model->getDetail($activeId);
        // 查询当前用户是否参与, 并且返回砍价任务ID
        $taskId = $model->getWhetherPartake($activeId);
        // 标记当前用户是否正在参与
        $isPartake = $taskId > 0;
        // 砍价规则
        $setting = SettingModel::getBasic();
        return $this->renderSuccess(compact('active', 'setting', 'isPartake', 'taskId'));
    }

    /**
     * 生成商品海报
     * @param int $activeId 砍价活动ID
     * @param string $channel 二维码渠道(小程序码、h5码)
     * @return Json
     * @throws \cores\exception\BaseException
     * @throws \cores\exception\BaseException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function poster(int $activeId, string $channel = 'H5'): Json
    {
        // 获取砍价活动详情
        $active = (new ActiveModel)->getDetail($activeId);
        // 获取商品详情
        $goodsInfo = (new GoodsModel)->getBasic($active['goods_id'], false);
        // 生成商品海报图
        $userId = UserService::getCurrentLoginUserId();
        $Qrcode = new GoodsPoster($active, $goodsInfo, $userId, $channel);
        return $this->renderSuccess(['imageUrl' => $Qrcode->getImage()]);
    }
}