<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
namespace app\api\service\signin;

use app\api\model\Setting as SettingModel;
use app\api\model\signin\Log as SigninLogModel;
use app\api\model\signin\Setting as SigninSettingModel;
use app\api\service\User as UserService;
use app\api\service\Signin as SigninService;
use cores\exception\BaseException;

// use app\api\model\signin\Participant as ParticipantModel;

/**
 * 服务类: 每日签到首页数据
 * Class Page
 * @package app\api\service\signin
 */
class Page extends SigninService
{
    /**
     * 获取每日签到首页数据
     * @return array
     * @throws BaseException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getPageData(): array
    {
        // 判断是否启用每日签到
        if (!SigninSettingModel::getEnabled()) {
            throwError('很抱歉，每日签到活动尚未开启~');
        }
        // 当前用户是否登录
        $isLogin = UserService::isLogin();
        // 今日是否已签到
        $isReport = $this->checkReport($isLogin);
        // 当月累计签到天数
        $currentMonthDays = $this->getCurrentMonthCount($isLogin);
        // 连续签到天数
        $continuousDays = $this->getContinuousDays($isLogin);
        // 返回页面数据
        return [
            // 当前用户是否登录
            'isLogin' => $isLogin,
            // 每日签到设置
            'setting' => SigninSettingModel::getPublic(),
            // 积分设置: 名称
            'pointsSetting' => ['points_name' => SettingModel::getPointsName()],
            // 今日是否已签到
            'isReport' => $isReport,
            // 今日签到奖励
            'nowReward' => $this->getNowReward($currentMonthDays, $continuousDays),
            // 累计签到奖励信息
            'cumulativeInfo' => $this->getCumulativeInfo($currentMonthDays),
            // 当月累计签到天数
            'currentMonthDays' => $currentMonthDays,
            // 连续签到天数
            'continuousDays' => $continuousDays,
            // 近期的签到记录 (仅显示日期 20250301)
            'signinLogs' => $this->getSigninLogs($isLogin),
        ];
    }

    /**
     * 累计签到奖励信息
     * @param int $currentMonthDays 当月累计签到天数
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    private function getCumulativeInfo(int $currentMonthDays): array
    {
        $options = SigninSettingModel::getItemOptions();
        $current = -1;
        foreach ($options['cumulative']['ruleList'] as $key => $item) {
            if ($options['cumulative']['enabled'] && $currentMonthDays < $item['days']) {
                $current = $key;
                break;
            }
        }
        return [
            // 是否开启
            'enabled' => $current > -1 && $options['cumulative']['enabled'],
            // 当前任务 (索引)
            'currentKey' => $current,
            // 当前任务 (天数)
            'currentDays' => $current > -1 ? $options['cumulative']['ruleList'][$current]['days'] : 0,
            // 奖励任务列表
            'ruleList' => $options['cumulative']['ruleList']
        ];
    }

//    /**
//     * 获取参与用户信息
//     * @param bool $isLogin
//     * @return ParticipantModel|array|mixed|\think\Model|null
//     * @throws BaseException
//     * @throws \think\db\exception\DataNotFoundException
//     * @throws \think\db\exception\DbException
//     * @throws \think\db\exception\ModelNotFoundException
//     */
//    private function getParticipant(bool $isLogin)
//    {
//        $model = new ParticipantModel;
//        return $isLogin ? $model->getDetailByUser() : [];
//    }

    /**
     * 获取近期的签到记录 (仅日期)
     * @param bool $isLogin
     * @return array
     * @throws BaseException
     */
    private function getSigninLogs(bool $isLogin): array
    {
        $model = new SigninLogModel;
        return $isLogin ? $model->getLastMonthDateList() : [];
    }
}