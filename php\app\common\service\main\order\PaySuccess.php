<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\common\service\main\order;

use app\common\library\helper;
use app\common\service\BaseService;
use app\common\model\dealer\Apply as DealerApplyModel;

/**
 * 普通订单支付成功后的回调
 * Class PaySuccess
 * @package app\common\service\main\order
 */
class PaySuccess extends BaseService
{
    /**
     * 回调方法
     * @param $order
     * @return bool
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function onPaySuccess($order): bool
    {
        // 购买指定商品成为分销商
        $this->becomeDealerUser($order);
        return true;
    }

    /**
     * 购买指定商品成为分销商
     * @param $order
     * @return void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    private function becomeDealerUser($order): void
    {
        // 整理商品id集
        $goodsIds = helper::getArrayColumn($order['goods'], 'goods_id');
        $model = new DealerApplyModel;
        $model->becomeDealerUser((int)$order['user_id'], $goodsIds, $order['store_id']);
    }
}