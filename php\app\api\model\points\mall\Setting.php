<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\api\model\points\mall;

use app\common\library\helper;
use app\common\model\points\mall\Setting as SettingModel;

/**
 * 模型类: 积分商城-基础设置
 * Class Setting
 * @package app\api\model\points\mall
 */
class Setting extends SettingModel
{
    /**
     * 获取基本配置
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function getBasic(): array
    {
        $config = static::getItem('basic');
        return helper::pick($config, ['pageTitle', 'pageBgImage', 'rulesDesc']);
    }
}