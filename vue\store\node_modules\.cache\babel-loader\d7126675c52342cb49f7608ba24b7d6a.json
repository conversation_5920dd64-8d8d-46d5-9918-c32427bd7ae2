{"remainingRequest": "D:\\2025\\gaodux\\gaodux5\\vue\\store\\node_modules\\babel-loader\\lib\\index.js!D:\\2025\\gaodux\\gaodux5\\vue\\store\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\2025\\gaodux\\gaodux5\\vue\\store\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\2025\\gaodux\\gaodux5\\vue\\store\\src\\views\\page\\modules\\components\\Components.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\2025\\gaodux\\gaodux5\\vue\\store\\src\\views\\page\\modules\\components\\Components.vue", "mtime": 1744300800000}, {"path": "D:\\2025\\gaodux\\gaodux5\\vue\\store\\babel.config.js", "mtime": 1656432000000}, {"path": "D:\\2025\\gaodux\\gaodux5\\vue\\store\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751804606056}, {"path": "D:\\2025\\gaodux\\gaodux5\\vue\\store\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751804605661}, {"path": "D:\\2025\\gaodux\\gaodux5\\vue\\store\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751804606056}, {"path": "D:\\2025\\gaodux\\gaodux5\\vue\\store\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751804675994}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": ";AAqBA;AACA;AAEA,aACA;EACAA;EACAC;EACAC,OACA;IACAF;IACAG;IACAC;EACA,GACA;IACAJ;IACAG;IACAC;EACA,GACA;IACAJ;IACAG;IACAC;EACA,GACA;IACAJ;IACAG;IACAC;EACA,GACA;IACAJ;IACAG;IACAC;EACA,GACA;IACAJ;IACAG;IACAC;EACA,GACA;IACAJ;IACAG;IACAC;EACA,GACA;IACAJ;IACAG;IACAC;EACA;AAEA,GACA;EACAJ;EACAC;EACAC,OACA;IACAF;IACAG;IACAC;EACA,GACA;IACAJ;IACAG;IACAC;EACA,GACA;IACAJ;IACAG;IACAC;EACA,GACA;IACAJ;IACAG;IACAC;EACA,GACA;IACAJ;IACAG;IACAC;EACA,GACA;IACAJ;IACAG;IACAC;IACAC;EACA,GACA;IACAL;IACAG;IACAC;IACAC;EACA,GACA;IACAL;IACAG;IACAC;IACAC;EACA,GACA;IACAL;IACAG;IACAC;IACAC;EACA,GACA;IACAL;IACAG;IACAC;IACAC;EACA,GACA;IACAL;IACAG;IACAC;IACA;EACA,GACA;IACAJ;IACAG;IACAC;EACA;AAEA,GACA;EACAJ;EACAC;EACAC,OACA;IACAF;IACAG;IACAC;EACA,GACA;IACAJ;IACAG;IACAC;EACA,GACA;IACAJ;IACAG;IACAC;EACA,GACA;IACAJ;IACAG;IACAC;EACA,GACA;IACAJ;IACAG;IACAC;EACA;AAEA,EACA;;AAEA;AACA;EAAA;IACAE;IACA;EACA;AAAA;AAEA;EACAJ;IACA;MAAAK;MAAAL;IAAA;EACA;EACAM;IACAC;MACA;MACA;QAAA;MAAA;IACA;EACA;EACAC;IACAC;MACA;IACA;EACA;AACA", "names": ["name", "key", "data", "type", "icon", "module<PERSON>ey", "item", "Icon", "computed", "componentKeys", "methods", "handleClickItem"], "sourceRoot": "src/views/page/modules/components", "sources": ["Components.vue"], "sourcesContent": ["<template>\r\n  <div class=\"components\">\r\n    <a-collapse :defaultActiveKey=\"componentKeys\" :bordered=\"false\" expandIconPosition=\"right\">\r\n      <a-collapse-panel v-for=\"group in data\" :key=\"group.key\" :header=\"group.name\">\r\n        <div class=\"module-list\">\r\n          <div\r\n            v-for=\"(item, index) in group.data\"\r\n            :key=\"index\"\r\n            class=\"module-item\"\r\n            @click=\"handleClickItem(item.type)\"\r\n          >\r\n            <a-icon class=\"module-icon\" :component=\"item.icon\" />\r\n            <span class=\"module-title\">{{ item.name }}</span>\r\n          </div>\r\n        </div>\r\n      </a-collapse-panel>\r\n    </a-collapse>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { filterModules } from '@/core/app'\r\nimport * as Icon from '@/assets/icons/page/components'\r\n\r\nconst data = [\r\n  {\r\n    name: '媒体组件',\r\n    key: 'media',\r\n    data: [\r\n      {\r\n        name: '轮播图',\r\n        type: 'banner',\r\n        icon: Icon.banner\r\n      },\r\n      {\r\n        name: '图片',\r\n        type: 'image',\r\n        icon: Icon.image\r\n      },\r\n      {\r\n        name: '图片橱窗',\r\n        type: 'window',\r\n        icon: Icon.window\r\n      },\r\n      {\r\n        name: '热区',\r\n        type: 'hotZone',\r\n        icon: Icon.window\r\n      },\r\n      {\r\n        name: '文章',\r\n        type: 'article',\r\n        icon: Icon.article\r\n      },\r\n      {\r\n        name: '视频',\r\n        type: 'video',\r\n        icon: Icon.video\r\n      },\r\n      {\r\n        name: '头条快报',\r\n        type: 'special',\r\n        icon: Icon.special\r\n      },\r\n      {\r\n        name: '标题文本',\r\n        type: 'title',\r\n        icon: Icon.title\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    name: '商城组件',\r\n    key: 'store',\r\n    data: [\r\n      {\r\n        name: '搜索框',\r\n        type: 'search',\r\n        icon: Icon.search\r\n      },\r\n      {\r\n        name: '导航组',\r\n        type: 'navBar',\r\n        icon: Icon.navBar\r\n      },\r\n      {\r\n        name: '店铺公告',\r\n        type: 'notice',\r\n        icon: Icon.notice\r\n      },\r\n      {\r\n        name: '商品组',\r\n        type: 'goods',\r\n        icon: Icon.goods\r\n      },\r\n      {\r\n        name: '商品分组',\r\n        type: 'goodsGroup',\r\n        icon: Icon.goodsGroup\r\n      },\r\n      {\r\n        name: '优惠券',\r\n        type: 'coupon',\r\n        icon: Icon.coupon,\r\n        moduleKey: 'market-coupon'\r\n      },\r\n      {\r\n        name: '砍价商品',\r\n        type: 'bargain',\r\n        icon: Icon.bargain,\r\n        moduleKey: 'apps-bargain'\r\n      },\r\n      {\r\n        name: '拼团商品',\r\n        type: 'groupon',\r\n        icon: Icon.groupon,\r\n        moduleKey: 'apps-groupon'\r\n      },\r\n      {\r\n        name: '整点秒杀',\r\n        type: 'sharp',\r\n        icon: Icon.sharp,\r\n        moduleKey: 'apps-sharp'\r\n      },\r\n       {\r\n        name: '积分商品',\r\n        type: 'pointsMall',\r\n        icon: Icon.pointsMall,\r\n        moduleKey: 'apps-pointsMall'\r\n      },\r\n      {\r\n        name: '线下门店',\r\n        type: 'shop',\r\n        icon: Icon.shop,\r\n        // moduleKey: 'store-shop'\r\n      },\r\n      {\r\n        name: '在线客服',\r\n        type: 'service',\r\n        icon: Icon.service\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    name: '其他组件',\r\n    key: 'other',\r\n    data: [\r\n      {\r\n        name: '富文本',\r\n        type: 'richText',\r\n        icon: Icon.richText\r\n      },\r\n      {\r\n        name: '辅助空白',\r\n        type: 'blank',\r\n        icon: Icon.blank\r\n      },\r\n      {\r\n        name: '辅助线',\r\n        type: 'guide',\r\n        icon: Icon.guide\r\n      },\r\n      {\r\n        name: '备案号',\r\n        type: 'ICPLicense',\r\n        icon: Icon.ICPLicense\r\n      },\r\n      {\r\n        name: '关注公众号',\r\n        type: 'officialAccount',\r\n        icon: Icon.officialAccount\r\n      },\r\n    ]\r\n  }\r\n]\r\n\r\n// 过滤开启的功能模块\r\nconst filterModulesData = data => data.map(item => {\r\n  item.data = filterModules(item.data)\r\n  return item\r\n})\r\n\r\nexport default {\r\n  data () {\r\n    return { Icon, data: filterModulesData(data) }\r\n  },\r\n  computed: {\r\n    componentKeys () {\r\n      const { data } = this\r\n      return data.map(item => item.key)\r\n    }\r\n  },\r\n  methods: {\r\n    handleClickItem (type) {\r\n      this.$emit('handleClickItem', type)\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"less\" scoped>\r\n::-webkit-scrollbar {\r\n  width: 0;\r\n  height: 0;\r\n}\r\n\r\n// 组件库\r\n.components {\r\n  width: 310px;\r\n  background: #f8f9fa;\r\n  height: 100%;\r\n  overflow-y: auto;\r\n\r\n  /deep/.ant-collapse-header {\r\n    padding: 14px 16px;\r\n    font-size: @font-size-base;\r\n    font-weight: 700;\r\n    color: #595961;\r\n  }\r\n\r\n  /deep/.ant-collapse-content-box {\r\n    padding-top: 0 !important;\r\n  }\r\n\r\n  /deep/.ant-collapse > .ant-collapse-item {\r\n    border-bottom: none;\r\n  }\r\n\r\n  .module-list {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n  }\r\n\r\n  .module-item {\r\n    width: 90px;\r\n    height: 90px;\r\n    padding: 15px 0;\r\n    text-align: center;\r\n    font-size: 12px;\r\n    color: #595961;\r\n    line-height: 20px;\r\n    position: relative;\r\n    user-select: none;\r\n    cursor: pointer;\r\n\r\n    &:hover {\r\n      background: #f7f7f7;\r\n      border-radius: 4px;\r\n    }\r\n    .module-icon {\r\n      display: block;\r\n      width: 74px;\r\n      height: 45px;\r\n      line-height: 45px;\r\n      margin: 0 auto;\r\n      font-size: 26px;\r\n      color: #9797a1;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}