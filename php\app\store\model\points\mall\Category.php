<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\store\model\points\mall;

use app\store\model\points\mall\Goods as PMGoodsModel;
use app\common\model\points\mall\Category as CategoryModel;

/**
 * 模型类: 积分商城-商品分类
 * Class Category
 * @package app\store\model\points\mall
 */
class Category extends CategoryModel
{
    /**
     * 添加新记录
     * @param array $data
     * @return bool
     */
    public function add(array $data): bool
    {
        $data['store_id'] = self::$storeId;
        return $this->save($data);
    }

    /**
     * 编辑记录
     * @param array $data
     * @return bool
     */
    public function edit(array $data): bool
    {
        return $this->save($data);
    }

    /**
     * 删除商品分类
     * @param int $categoryId
     * @return bool
     */
    public function setDelete(int $categoryId): bool
    {
        // 判断分类下是否存在商品
        $goodsCount = PMGoodsModel::getGoodsTotal(['category_id' => $categoryId]);
        if ($goodsCount > 0) {
            $this->error = "该分类下存在{$goodsCount}个商品，不允许删除";
            return false;
        }
        // 删除记录
        return $this->save(['is_delete' => 1]);
    }
}
