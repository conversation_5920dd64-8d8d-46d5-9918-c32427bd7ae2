<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\common\model\signin;

use cores\BaseModel;

/**
 * 模型类: 每日签到参与用户
 * Class Participant
 * @package app\common\model\user
 */
class Participant extends BaseModel
{
    // 定义表名
    protected $name = 'signin_participant';

    // 定义主键
    protected $pk = 'id';

    /**
     * 关联会员记录表
     * @return \think\model\relation\BelongsTo
     */
    public function user(): \think\model\relation\BelongsTo
    {
        $module = self::getCalledModule();
        return $this->belongsTo("app\\{$module}\\model\\User");
    }

    /**
     * 获取器：首次签到时间
     * @param $value
     * @return false|string
     */
    public function getFirstTimeAttr($value)
    {
        return \format_time($value);
    }

    /**
     * 获取器：上次签到时间
     * @param $value
     * @return false|string
     */
    public function getLastTimeAttr($value)
    {
        return \format_time($value);
    }
}
