<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2023 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\store\controller\client\mp\alipay;

use app\store\controller\Controller;
use app\store\model\mp\alipay\Setting as SettingModel;
use think\response\Json;

/**
 * 支付宝小程序设置
 * Class Setting
 * @package app\store\controller\client\mp\alipay
 */
class Setting extends Controller
{
    /**
     * 获取支付宝小程序基础设置
     * @return Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function basic(): Json
    {
        $detail = SettingModel::getBasic();
        // 服务端域名
        $domain = $this->request->host(true);
        return $this->renderSuccess(compact('detail', 'domain'));
    }

    /**
     * 获取支付宝小程序设置 (指定)
     * @param string $key
     * @return Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function detail(string $key): Json
    {
        // 获取支付宝小程序设置
        $detail = SettingModel::getItem($key);
        // 服务端域名
        $domain = $this->request->host(true);
        return $this->renderSuccess(compact('detail', 'domain'));
    }

    /**
     * 更新设置项
     * @param string $key
     * @return Json
     */
    public function update(string $key): Json
    {
        $model = new SettingModel;
        if ($model->edit($key, $this->postForm())) {
            return $this->renderSuccess('更新成功');
        }
        return $this->renderError($model->getError() ?: '更新失败');
    }

    /**
     * 更新基础设置
     * @return Json
     */
    public function updateBasic(): Json
    {
        $model = new SettingModel;
        if ($model->edit('basic', $this->postForm())) {
            return $this->renderSuccess('更新成功');
        }
        return $this->renderError($model->getError() ?: '更新失败');
    }
}
