<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2023 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\api\model\store\shop;

use cores\exception\BaseException;
use app\common\model\store\shop\Clerk as ClerkModel;

/**
 * 商家门店店员模型
 * Class Clerk
 * @package app\api\model\store\shop
 */
class Clerk extends ClerkModel
{
    /**
     * 隐藏字段
     * @var array
     */
    protected $hidden = [
        'is_delete',
        'store_id',
        'create_time',
        'update_time'
    ];

    /**
     * 获取店员信息
     * @param int $userId
     * @return Clerk|array|null
     * @throws BaseException
     */
    public static function getClerkInfo(int $userId)
    {
        // 获取店员记录
        $clerk = static::detail(['user_id' => $userId, 'is_delete' => 0]);
        if (empty($clerk)) {
            throwError('很抱歉，您不是门店的店员');
        }
        if (!$clerk['status']) {
            throwError('很抱歉，当前店员状态已被禁用');
        }
        return $clerk;
    }

    /**
     * 验证店员信息是否为指定门店的核销员
     * @param Clerk $clerk 店员信息
     * @param int $shopId 指定的门店ID
     * @return bool
     * @throws BaseException
     */
    public static function checkAuthority(self $clerk, int $shopId): bool
    {
        if ($clerk['shop_id'] != $shopId) {
            throwError('很抱歉，当前店员不属于该门店，没有核销权限');
        }
        if (!$clerk['status']) {
            throwError('很抱歉，当前店员状态已被禁用');
        }
        return true;
    }
}