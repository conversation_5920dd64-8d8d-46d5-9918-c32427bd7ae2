<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\api\controller\page;

use think\response\Json;
use app\api\controller\Controller;
use app\api\service\page\Template as PageTemplateService;

/**
 * 页面控制器
 * Class Index
 * @package app\api\controller
 */
class Template extends Controller
{
    /**
     * 页面数据
     * @param int $templateId 页面模板ID
     * @return Json
     * @throws \cores\exception\BaseException
     * @throws \think\Exception
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function detail(int $templateId): Json
    {
        // 页面数据
        $model = new PageTemplateService;
        $pageData = $model->getPageData($templateId);
        return $this->renderSuccess(compact('pageData'));
    }
}
