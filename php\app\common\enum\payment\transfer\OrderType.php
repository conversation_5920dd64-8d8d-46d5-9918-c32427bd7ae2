<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\common\enum\payment\transfer;

use app\common\enum\EnumBasics;

/**
 * 枚举类：转账订单类型
 * Class OrderType
 * @package app\common\enum
 */
class OrderType extends EnumBasics
{
    // 分销商提现
    const DEALER_WITHDRAW = 10;

    /**
     * 获取转账订单类型值
     * @return array
     */
    public static function data(): array
    {
        return [
            self::DEALER_WITHDRAW => [
                'name' => '分销商提现',
                'value' => self::DEALER_WITHDRAW
            ]
        ];
    }
}