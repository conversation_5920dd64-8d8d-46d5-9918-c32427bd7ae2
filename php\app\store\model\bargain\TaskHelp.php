<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\store\model\bargain;

use app\common\model\bargain\TaskHelp as TaskHelpModel;

/**
 * 砍价任务助力记录模型
 * Class TaskHelp
 * @package app\store\model\bargain
 */
class TaskHelp extends TaskHelpModel
{
    /**
     * 获取列表数据
     * @param int $taskId
     * @return \think\Paginator
     * @throws \think\db\exception\DbException
     */
    public function getList(int $taskId): \think\Paginator
    {
        // 砍价任务助力记录
        return $this->with(['user.avatar'])
            ->where('task_id', '=', $taskId)
            ->order(['create_time' => 'desc'])
            ->paginate(15);
    }
}