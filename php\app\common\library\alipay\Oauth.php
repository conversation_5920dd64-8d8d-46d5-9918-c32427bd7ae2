<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2023 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\common\library\alipay;

use Alipay\EasySDK\Kernel\Factory;
use Alipay\EasySDK\Kernel\Config;

/**
 * 支付宝小程序OAuth认证
 * Class Oauth
 * @package app\common\library\alipay
 */
class Oauth
{
    private $config;
    private $error;

    /**
     * 设置配置参数
     * @param array $config
     * @return $this
     */
    public function setOptions(array $config): Oauth
    {
        $this->config = $config;
        $this->initFactory();
        return $this;
    }

    /**
     * 初始化支付宝SDK
     */
    private function initFactory()
    {
        $options = new Config();
        $options->protocol = 'https';
        $options->gatewayHost = 'openapi.alipay.com';
        $options->signType = $this->config['signType'] ?? 'RSA2';
        $options->appId = $this->config['appId'];
        
        // 设置应用私钥
        $options->merchantPrivateKey = $this->config['merchantPrivateKey'];
        
        // 根据签名模式设置公钥或证书
        if (($this->config['signMode'] ?? 10) == 20) {
            // 公钥证书模式
            $options->alipayCertPath = $this->config['alipayCertPublicKey'] ?? '';
            $options->alipayRootCertPath = $this->config['alipayRootCert'] ?? '';
            $options->merchantCertPath = $this->config['appCertPublicKey'] ?? '';
        } else {
            // 普通公钥模式
            $options->alipayPublicKey = $this->config['alipayPublicKey'];
        }
        
        Factory::setOptions($options);
    }

    /**
     * 获取授权访问令牌
     * @param string $authCode 授权码
     * @return array|null
     */
    public function getToken(string $authCode): ?array
    {
        try {
            $response = Factory::base()->oauth()->getToken($authCode);
            
            if ($response->code === '10000') {
                return [
                    'access_token' => $response->accessToken,
                    'refresh_token' => $response->refreshToken,
                    'expires_in' => $response->expiresIn,
                    'open_id' => $response->userId,
                    'user_id' => $response->userId,
                ];
            } else {
                $this->error = $response->msg . ' (' . $response->subMsg . ')';
                return null;
            }
        } catch (\Exception $e) {
            $this->error = $e->getMessage();
            return null;
        }
    }

    /**
     * 刷新授权访问令牌
     * @param string $refreshToken 刷新令牌
     * @return array|null
     */
    public function refreshToken(string $refreshToken): ?array
    {
        try {
            $response = Factory::base()->oauth()->refreshToken($refreshToken);
            
            if ($response->code === '10000') {
                return [
                    'access_token' => $response->accessToken,
                    'refresh_token' => $response->refreshToken,
                    'expires_in' => $response->expiresIn,
                    'open_id' => $response->userId,
                    'user_id' => $response->userId,
                ];
            } else {
                $this->error = $response->msg . ' (' . $response->subMsg . ')';
                return null;
            }
        } catch (\Exception $e) {
            $this->error = $e->getMessage();
            return null;
        }
    }

    /**
     * 获取用户信息
     * @param string $accessToken 访问令牌
     * @return array|null
     */
    public function getUserInfo(string $accessToken): ?array
    {
        try {
            // 注意：支付宝小程序获取用户信息需要在前端调用 my.getOpenUserInfo
            // 这里只是提供一个接口，实际使用时需要前端配合
            $this->error = '支付宝小程序用户信息需要在前端通过 my.getOpenUserInfo 获取';
            return null;
        } catch (\Exception $e) {
            $this->error = $e->getMessage();
            return null;
        }
    }

    /**
     * 获取错误信息
     * @return string
     */
    public function getError(): string
    {
        return $this->error ?? '';
    }
}