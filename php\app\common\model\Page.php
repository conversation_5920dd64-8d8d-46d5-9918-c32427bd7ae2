<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2025 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\common\model;

use cores\BaseModel;
use app\common\library\helper;
use app\common\enum\page\PageType as PageTypeEnum;

/**
 * 店铺页面模型
 * Class Page
 * @package app\common\model
 */
class Page extends BaseModel
{
    // 定义表名
    protected $name = 'page';

    // 定义主键
    protected $pk = 'page_id';

    /**
     * 获取器：格式化页面数据
     * @param string $json
     * @return array
     */
    public function getPageDataAttr(string $json): array
    {
        // 数据转义
        $array = helper::jsonDecode(\htmlspecialchars_decode($json));
        // 合并默认数据
        return $this->mergeDefaultData($array);
    }

    /**
     * 修改器：自动转换data为json格式
     * @param array $value
     * @return string
     */
    public function setPageDataAttr(array $value): string
    {
        return helper::jsonEncode($value ?: ['items' => []]);
    }

    /**
     * 页面标题栏默认数据
     * @return array
     */
    public function getDefaultPage(): array
    {
        return [
            'params' => [
                'name' => '页面名称',
                'title' => '页面标题',
                'shareTitle' => '分享标题'
            ],
            'style' => [
                'titleTextColor' => 'black',
                'titleBackgroundColor' => '#ffffff',
            ]
        ];
    }

    /**
     * 店铺页面元素默认数据
     * @return array
     */
    public function getDefaultItems(): array
    {
        return [
            'search' => [
                'name' => '搜索框',
                'type' => 'search',
                'params' => ['placeholder' => '请输入关键字进行搜索', 'sticky' => false],
                'style' => [
                    // 文字对齐方式
                    'textAlign' => 'left',
                    // 搜索框样式 (square方形 radius圆角 round圆弧)
                    'searchStyle' => 'round',
                    // 搜索框背景颜色
                    'searchBg' => '#ffffff',
                    // 搜索框文字颜色
                    'searchFontColor' => '#999999',
                    // 组件背景颜色
                    'background' => '#f1f1f2',
                    // 上下边距
                    'paddingY' => 10,
                    // 左右边距
                    'paddingX' => 10,
                ]
            ],
            'banner' => [
                'name' => '轮播图',
                'type' => 'banner',
                'style' => [
                    'paddingTop' => 0,
                    'paddingLeft' => 0,
                    'background' => '#ffffff',
                    'btnShape' => 'round',          // 指示点形状
                    'btnColor' => '#ffffff',        // 指示点颜色
                    'interval' => 3,                // 切换时间
                    'borderRadius' => 0,            // 圆角尺寸
                ],
                'data' => [
                    [
                        'imgUrl' => \base_url() . 'assets/store/img/diy/banner/01.png',
                        'imgName' => 'image-1.jpg',
                        'link' => null
                    ],
                    [
                        'imgUrl' => \base_url() . 'assets/store/img/diy/banner/01.png',
                        'imgName' => 'image-1.jpg',
                        'link' => null
                    ]
                ]
            ],
            'image' => [
                'name' => '图片组',
                'type' => 'image',
                'style' => [
                    'paddingTop' => 0,
                    'paddingLeft' => 0,
                    'background' => '#ffffff',
                    'borderRadius' => 0,             // 圆角尺寸
                    'itemMargin' => 0,               // 图片间距
                ],
                'data' => [
                    [
                        'imgUrl' => \base_url() . 'assets/store/img/diy/banner/01.png',
                        'imgName' => 'image-1.jpg',
                        'link' => null
                    ]
                ]
            ],
            'navBar' => [
                'name' => '导航组',
                'type' => 'navBar',
                'style' => [
                    'rowsNum' => 4,
                    'background' => '#ffffff',
                    'paddingTop' => 10,
                    // 文字颜色
                    'textColor' => '#666666',
                    // 图片大小
                    'imageSize' => 44,
                ],
                'data' => [
                    [
                        'imgUrl' => \base_url() . 'assets/store/img/diy/navbar/01.png',
                        'imgName' => 'icon-1.png',
                        'link' => null,
                        'text' => '按钮文字1'
                    ],
                    [
                        'imgUrl' => \base_url() . 'assets/store/img/diy/navbar/01.png',
                        'imgName' => 'icon-2.jpg',
                        'link' => null,
                        'text' => '按钮文字2'
                    ],
                    [
                        'imgUrl' => \base_url() . 'assets/store/img/diy/navbar/01.png',
                        'imgName' => 'icon-3.jpg',
                        'link' => null,
                        'text' => '按钮文字3'
                    ],
                    [
                        'imgUrl' => \base_url() . 'assets/store/img/diy/navbar/01.png',
                        'imgName' => 'icon-4.jpg',
                        'link' => null,
                        'text' => '按钮文字4'
                    ]
                ]
            ],
            'blank' => [
                'name' => '辅助空白',
                'type' => 'blank',
                'style' => [
                    'height' => 20,
                    'background' => '#ffffff'
                ]
            ],
            'guide' => [
                'name' => '辅助线',
                'type' => 'guide',
                'style' => [
                    'background' => '#ffffff',
                    'lineStyle' => 'solid',
                    'lineHeight' => 1,
                    'lineColor' => "#000000",
                    'paddingTop' => 10
                ]
            ],
            'video' => [
                'name' => '视频组',
                'type' => 'video',
                'params' => [
                    'videoUrl' => 'http://wxsnsdy.tc.qq.com/105/20210/snsdyvideodownload?filekey=30280201010421301f0201690402534804102ca905ce620b1241b726bc41dcff44e00204012882540400.mp4',
                    'poster' => \base_url() . 'assets/store/img/diy/video_poster.png',
                    'autoplay' => 0
                ],
                'style' => [
                    'background' => '#ffffff',
                    'paddingTop' => 10,
                    'paddingLeft' => 10,
                    'height' => 202
                ]
            ],
            'article' => [
                'name' => '文章组',
                'type' => 'article',
                'params' => [
                    'source' => 'auto',         // 数据来源 (choice手动选择 auto自动获取)
                    'auto' => [
                        'category' => -1,   // -1全部
                        'showNum' => 6
                    ]
                ],
                // '自动获取' => 默认数据
                'defaultData' => [
                    [
                        'title' => '此处显示文章标题',
                        'show_type' => 10,
                        'image' => \base_url() . 'assets/store/img/diy/article/01.png',
                        'views_num' => 309
                    ],
                    [
                        'title' => '此处显示文章标题',
                        'show_type' => 10,
                        'image' => \base_url() . 'assets/store/img/diy/article/01.png',
                        'views_num' => 309
                    ]
                ],
                // '手动选择' => 默认数据
                'data' => []
            ],
            'notice' => [
                'name' => '店铺公告',
                'type' => 'notice',
                'params' => [
                    'text' => '这里是第一条自定义公告的标题', // 公告内容
                    // 'icon' => \base_url() . 'assets/store/img/diy/notice.png',
                    'link' => null,     // 链接
                    'showIcon' => true, // 是否显示图标
                    'scrollable' => true    // 是否滚动
                ],
                'style' => [
                    'paddingTop' => 0,
                    'background' => '#fffbe8',
                    // 文字颜色
                    'textColor' => '#de8c17',
                    // 文字大小
                    'fontSize' => 14,
                ]
            ],
            'richText' => [
                'name' => '富文本',
                'type' => 'richText',
                'params' => [
                    'content' => '<p>这里是文本的内容</p>'
                ],
                'style' => [
                    'paddingTop' => 0,
                    'paddingLeft' => 0,
                    'background' => '#ffffff'
                ]
            ],
            'window' => [
                'name' => '图片橱窗',
                'type' => 'window',
                'style' => [
                    'paddingTop' => 0,
                    'paddingLeft' => 0,
                    'background' => '#ffffff',
                    'layout' => 2   // 布局方式: -1橱窗
                ],
                'data' => [
                    [
                        'imgUrl' => \base_url() . 'assets/store/img/diy/window/01.jpg',
                        'imgName' => 'image-1.jpg',
                        'link' => null
                    ],
                    [
                        'imgUrl' => \base_url() . 'assets/store/img/diy/window/02.jpg',
                        'imgName' => 'image-1.jpg',
                        'link' => null
                    ],
                    [
                        'imgUrl' => \base_url() . 'assets/store/img/diy/window/03.jpg',
                        'imgName' => 'image-1.jpg',
                        'link' => null
                    ],
                    [
                        'imgUrl' => \base_url() . 'assets/store/img/diy/window/04.jpg',
                        'imgName' => 'image-1.jpg',
                        'link' => null
                    ]
                ],
                'dataNum' => 4
            ],
            'hotZone' => [
                'name' => '热区组',
                'type' => 'hotZone',
                'style' => [
                    'paddingTop' => 0,
                    'paddingLeft' => 0,
                    'background' => '#ffffff',
                    'borderRadius' => 0,             // 圆角尺寸
                ],
                'data' => [
                    'imgUrl' => \base_url() . 'assets/store/img/diy/banner/01.png',
                    'imgName' => 'image-1.jpg',
                    'maps' => [
                        [
                            'width' => 300,
                            'height' => 100,
                            'left' => 0,
                            'top' => 0,
                            'link' => null,
                            'key' => 1
                        ],
                    ]
                ]
            ],
            'goods' => [
                'name' => '商品组',
                'type' => 'goods',
                'params' => [
                    'source' => 'auto',         // 数据来源 (choice手动选择 auto自动获取)
                    'auto' => [
                        'category' => 0,        // 商品分类 0为全部
                        'goodsSort' => 'all',   // 商品排序 (all默认 sales销量 price价格)
                        'showNum' => 6          // 显示数量
                    ]
                ],
                'style' => [
                    'display' => 'list',        // 显示类型 (list列表平铺 slide横向滑动)
                    'column' => 2,              // 分列数量 (单列/双列/三列)
                    'show' => [                 // 显示内容
                        'goodsName', 'goodsPrice', 'linePrice', 'sellingPoint', 'goodsSales', 'cartBtn'
                    ],
                    'priceColor' => '#ff1051',       // 商品价格颜色
                    'sellingColor' => '#e3771f',     // 商品卖点颜色
                    'goodsNameRows' => 'two',        // 商品名称行数 (one单行 two双行)
                    'btnCartStyle' => 1,             // 购物车按钮风格
                    'btnCartColor' => '#27c29a',     // 购物车按钮颜色
                    // 'btnFontColor' => '#ffffff',     // 购物车按钮文字颜色
                    'cardType' => 'card',            // 卡片样式：flat扁平  card卡片
                    'borderRadius' => 10,            // 圆角尺寸
                    'itemMargin' => 10,              // 商品间距
                    'background' => '#f6f6f6',       // 背景颜色
                    'paddingY' => 12,                // 上下边距
                    'paddingX' => 12,                // 左右边距
                ],
                // '自动获取' => 默认数据
                'defaultData' => [
                    [
                        'goods_name' => '此处显示商品名称',
                        'goods_image' => \base_url() . 'assets/store/img/diy/goods/01.png',
                        'goods_price_min' => '99.00',
                        'line_price_min' => '139.00',
                        'selling_point' => '此款商品美观大方 不容错过',
                        'goods_sales' => 100,
                    ],
                    [
                        'goods_name' => '此处显示商品名称',
                        'goods_image' => \base_url() . 'assets/store/img/diy/goods/01.png',
                        'goods_price_min' => '99.00',
                        'line_price_min' => '139.00',
                        'selling_point' => '此款商品美观大方 不容错过',
                        'goods_sales' => 100,
                    ],
                    [
                        'goods_name' => '此处显示商品名称',
                        'goods_image' => \base_url() . 'assets/store/img/diy/goods/01.png',
                        'goods_price_min' => '99.00',
                        'line_price_min' => '139.00',
                        'selling_point' => '此款商品美观大方 不容错过',
                        'goods_sales' => 100,
                    ],
                    [
                        'goods_name' => '此处显示商品名称',
                        'goods_image' => \base_url() . 'assets/store/img/diy/goods/01.png',
                        'goods_price_min' => '99.00',
                        'line_price_min' => '139.00',
                        'selling_point' => '此款商品美观大方 不容错过',
                        'goods_sales' => 100,
                    ]
                ],
                // '手动选择' => 默认数据
                'data' => []
            ],
            'service' => [
                'name' => '在线客服',
                'type' => 'service',
                'params' => [
                    'type' => 'chat',     // '客服类型' => chat在线聊天，phone拨打电话
                    'image' => \base_url() . 'assets/store/img/diy/service.png',
                    'tel' => '' // 电话号吗
                ],
                'style' => [
                    'right' => 1,
                    'bottom' => 10,
                    'opacity' => 100
                ]
            ],
            'officialAccount' => [
                'name' => '关注公众号',
                'type' => 'officialAccount',
                'params' => [],
                'style' => []
            ],
            'shop' => [
                'name' => '线下门店',
                'type' => 'shop',
                'params' => [
                    'source' => 'auto',         // 数据来源 (choice手动选择 auto自动获取)
                    'auto' => ['showNum' => 6]
                ],
                'style' => [
                    'show' => ['logo', 'address', 'phone'], // 显示内容
                    'background' => '#ffffff', // 背景颜色
                ],
                // '自动获取' => 默认数据
                'defaultData' => [
                    [
                        'shop_name' => '此处显示门店名称',
                        'logo_url' => \base_url() . 'assets/store/img/diy/circular.png',
                        'phone' => '010-6666666',
                        'region' => [
                            'province' => 'xxx省',
                            'city' => 'xxx市',
                            'region' => 'xxx区'
                        ],
                        'address' => 'xxx街道',
                    ],
                    [
                        'shop_name' => '此处显示门店名称',
                        'logo_url' => \base_url() . 'assets/store/img/diy/circular.png',
                        'phone' => '010-6666666',
                        'region' => [
                            'province' => 'xxx省',
                            'city' => 'xxx市',
                            'region' => 'xxx区'
                        ],
                        'address' => 'xxx街道',
                    ],
                ],
                // '手动选择' => 默认数据
                'data' => []
            ],
            'bargain' => [
                'name' => '砍价商品',
                'type' => 'bargain',
                'params' => [
                    'source' => 'auto',                     // 数据来源 (choice手动选择 auto自动获取)
                    'auto' => [
                        'goodsSort' => 'all',               // 商品排序 (all默认 sales销量 price价格)
                        'showNum' => 6
                    ],
                    'display' => 'list',                    // 显示类型 (list列表平铺 slide横向滑动)
                    'column' => 1,                          // 分列数量 (单列/双列/三列)
                    'mainBtnText' => '参与砍价',              // 购买按钮文字
                    'show' => [                             // 显示的内容
                        'goodsName', 'floorPrice', 'originalPrice', 'peoples', 'mainBtn'
                    ],
                    'title' => [
                        'enable' => true,                    // 是否显示标题栏
                        'image' => \base_url() . 'assets/store/img/diy/bargain/title.png',       // 标题栏图片
                        'bgImage' => \base_url() . 'assets/store/img/diy/bargain/titleBg.png',   // 标题栏背景图
                        'rightText' => '更多',               // 右侧文字内容
                        'descText' => '低至1分钱拿回家',       // 描述文字
                    ],
                ],
                'style' => [
                    'title' => [
                        'descTextColor' => '#ffffff',                   // 描述文字颜色
                        'rightTextColor' => '#ffffff',                  // 右侧文字颜色
                    ],
                    'content' => [
                        'goodsNameRows' => 'two',                       // 商品名称行数 (one单行 two双行)
                        'priceColor' => '#fa2209',                      // 商品价格颜色
                        'mainBtnBgColor' => ['#fa2209', '#ff6335'],     // 购买按钮背景颜色
                        'mainBtnTextColor' => '#ffffff',                // 购买按钮文字颜色
                        'borderRadius' => 10,                           // 圆角尺寸
                        'itemMargin' => 12,                             // 商品间距
                        'itemPaddingY' => 10,                           // 上下边距
                        // 'background' => '#ffffff',                   // 背景颜色
                    ],
                    'paddingY' => 12,                // 上下边距
                    'paddingX' => 12,                // 左右边距
                    'background' => '#f6f6f6',       // 背景颜色
                    'borderRadius' => 10,            // 圆角尺寸
                ],
                'demo' => [
                    'helpsCount' => 2,
                    'helpList' => [
                        ['user' => ['avatar_url' => \base_url() . 'assets/store/img/diy/bargain/01.jpg']],
                        ['user' => ['avatar_url' => \base_url() . 'assets/store/img/diy/bargain/02.jpg']],
                    ]
                ],
                // '自动获取' => 默认数据
                'defaultData' => [
                    [
                        'goods_name' => '此处显示砍价商品名称 此处显示砍价商品名称',
                        'goods_image' => \base_url() . 'assets/store/img/diy/goods/01.png',
                        'floor_price' => '0.01',
                        'original_price' => '139.00',
                    ],
                    [
                        'goods_name' => '此处显示砍价商品名称 此处显示砍价商品名称',
                        'goods_image' => \base_url() . 'assets/store/img/diy/goods/01.png',
                        'floor_price' => '0.01',
                        'original_price' => '139.00',
                    ],
                    [
                        'goods_name' => '此处显示砍价商品名称 此处显示砍价商品名称',
                        'goods_image' => \base_url() . 'assets/store/img/diy/goods/01.png',
                        'floor_price' => '0.01',
                        'original_price' => '139.00',
                    ],
                    [
                        'goods_name' => '此处显示砍价商品名称 此处显示砍价商品名称',
                        'goods_image' => \base_url() . 'assets/store/img/diy/goods/01.png',
                        'floor_price' => '0.01',
                        'original_price' => '139.00',
                    ]
                ],
                // '手动选择' => 默认数据
                'data' => []
            ],
            'sharp' => [
                'name' => '整点秒杀',
                'type' => 'sharp',
                'params' => [
                    'display' => 'list',          // 显示类型 (list列表平铺 slide横向滑动)
                    'column' => 1,                // 分列数量 (单列/双列/三列)
                    // 'goodsSort' => 'all',      // 商品排序 (all默认 sales销量 price价格)
                    'showNum' => 6,               // 商品数量
                    'mainBtnText' => '马上抢',     // 购买按钮文字
                    'show' => [                   // 显示的内容
                        'goodsName', 'seckillPrice', 'originalPrice', 'progress', 'mainBtn'
                    ],
                    'title' => [
                        'enable' => true,                  // 是否显示标题栏
                        'image' => \base_url() . 'assets/store/img/diy/sharp/title.png',       // 标题栏图片
                        'bgImage' => \base_url() . 'assets/store/img/diy/sharp/titleBg.png',   // 标题栏背景图
                        'rightText' => '更多',              // 右侧文字内容
                        'showCountdown' => true,           // 是否显示倒计时
                        'countdownText' => '正在疯抢',       // 倒计时文字
                    ],
                ],
                'style' => [
                    'title' => [
                        'cdTextColor' => '#ffffff',        // 倒计时文字颜色
                        'cdNumColor' => '#fa2209',         // 倒计时数字颜色
                        'cdNumBgColor' => '#ffffff',       // 倒计时数字背景颜色
                        'rightTextColor' => '#ffffff',     // 右侧文字颜色
                    ],
                    'content' => [
                        'goodsNameRows' => 'two',                       // 商品名称行数 (one单行 two双行)
                        'priceColor' => '#fa2209',                      // 商品价格颜色
                        'progressColor' => ['#fa2209', '#ff6335'],      // 抢购进度颜色
                        'salesColor' => '#fa2209',                      // 商品销量颜色
                        'mainBtnBgColor' => ['#fa2209', '#ff6335'],     // 购买按钮背景颜色
                        'mainBtnTextColor' => '#ffffff',                // 购买按钮文字颜色
                        'borderRadius' => 10,                           // 圆角尺寸
                        'itemMargin' => 12,                             // 商品间距
                        'itemPaddingY' => 10,                           // 上下边距
                        // 'background' => '#ffffff',                   // 背景颜色
                    ],
                    'paddingY' => 12,                // 上下边距
                    'paddingX' => 12,                // 左右边距
                    'background' => '#f6f6f6',       // 背景颜色
                    'borderRadius' => 10,            // 圆角尺寸
                ],
                // 默认数据: 商品列表
                'data' => [
                    [
                        'goods_name' => '此处显示秒杀商品名称 此处显示秒杀商品名称',
                        'goods_image' => \base_url() . 'assets/store/img/diy/goods/01.png',
                        'seckill_price_min' => '69.00',
                        'original_price' => '139.00',
                        'sales_actual' => 100,
                        'progress' => 42,
                    ],
                    [
                        'goods_name' => '此处显示秒杀商品名称 此处显示秒杀商品名称',
                        'goods_image' => \base_url() . 'assets/store/img/diy/goods/01.png',
                        'seckill_price_min' => '69.00',
                        'original_price' => '139.00',
                        'sales_actual' => 100,
                        'progress' => 42,
                    ],
                    [
                        'goods_name' => '此处显示秒杀商品名称 此处显示秒杀商品名称',
                        'goods_image' => \base_url() . 'assets/store/img/diy/goods/01.png',
                        'seckill_price_min' => '69.00',
                        'original_price' => '139.00',
                        'sales_actual' => 100,
                        'progress' => 42,
                    ],
                    [
                        'goods_name' => '此处显示秒杀商品名称 此处显示秒杀商品名称',
                        'goods_image' => \base_url() . 'assets/store/img/diy/goods/01.png',
                        'seckill_price_min' => '69.00',
                        'original_price' => '139.00',
                        'sales_actual' => 100,
                        'progress' => 42,
                    ]
                ]
            ],
            'groupon' => [
                'name' => '拼团商品组',
                'type' => 'groupon',
                'params' => [
                    'source' => 'auto',                     // 数据来源 (choice手动选择 auto自动获取)
                    'auto' => [
                        'goodsSort' => 'all',               // 商品排序 (all默认 sales销量 price价格)
                        'showNum' => 6
                    ],
                    'display' => 'list',                    // 显示类型 (list列表平铺 slide横向滑动)
                    'column' => 1,                          // 分列数量 (单列/双列/三列)
                    'mainBtnText' => '去拼团',               // 购买按钮文字
                    'show' => [                             // 显示的内容
                        'goodsName', 'grouponPrice', 'originalPrice', 'people', 'activeSales', 'mainBtn'
                    ],
                    'title' => [
                        'enable' => true,                    // 是否显示标题栏
                        'image' => \base_url() . 'assets/store/img/diy/groupon/title.png',       // 标题栏图片
                        'bgImage' => \base_url() . 'assets/store/img/diy/groupon/titleBg.png',   // 标题栏背景图
                        'rightText' => '更多',               // 右侧文字内容
                        'descText' => '超值好货 拼团专享',     // 描述文字
                    ],
                ],
                'style' => [
                    'title' => [
                        'descTextColor' => '#ffffff',                   // 描述文字颜色
                        'rightTextColor' => '#ffffff',                  // 右侧文字颜色
                    ],
                    'content' => [
                        'goodsNameRows' => 'two',                       // 商品名称行数 (one单行 two双行)
                        'priceColor' => '#fa2209',                      // 商品价格颜色
                        'tagColor' => '#fa2209',                        // 活动标签颜色
                        'mainBtnBgColor' => ['#fa2209', '#ff6335'],     // 购买按钮背景颜色
                        'mainBtnTextColor' => '#ffffff',                // 购买按钮文字颜色
                        'borderRadius' => 10,                           // 圆角尺寸
                        'itemMargin' => 12,                             // 商品间距
                        'itemPaddingY' => 10,                           // 上下边距
                        // 'background' => '#ffffff',                   // 背景颜色
                    ],
                    'paddingY' => 12,                // 上下边距
                    'paddingX' => 12,                // 左右边距
                    'background' => '#f6f6f6',       // 背景颜色
                    'borderRadius' => 10,            // 圆角尺寸
                ],
                // '自动获取' => 默认数据
                'defaultData' => [
                    [
                        'goods_name' => '此处显示拼团商品名称 此处显示拼团商品名称',
                        'goods_image' => \base_url() . 'assets/store/img/diy/goods/01.png',
                        'show_people' => 3,
                        'active_sales' => 120,
                        'groupon_price' => '99.00',
                        'original_price' => '139.00',
                    ],
                    [
                        'goods_name' => '此处显示拼团商品名称 此处显示拼团商品名称',
                        'goods_image' => \base_url() . 'assets/store/img/diy/goods/01.png',
                        'show_people' => 3,
                        'active_sales' => 120,
                        'groupon_price' => '99.00',
                        'original_price' => '139.00',
                    ],
                    [
                        'goods_name' => '此处显示拼团商品名称 此处显示拼团商品名称',
                        'goods_image' => \base_url() . 'assets/store/img/diy/goods/01.png',
                        'show_people' => 3,
                        'active_sales' => 120,
                        'groupon_price' => '99.00',
                        'original_price' => '139.00',
                    ],
                    [
                        'goods_name' => '此处显示拼团商品名称 此处显示拼团商品名称',
                        'goods_image' => \base_url() . 'assets/store/img/diy/goods/01.png',
                        'show_people' => 3,
                        'active_sales' => 120,
                        'groupon_price' => '99.00',
                        'original_price' => '139.00',
                    ]
                ],
                // '手动选择' => 默认数据
                'data' => []
            ],
            'pointsMall' => [
                'name' => '积分商品',
                'type' => 'pointsMall',
                'params' => [
                    'source' => 'auto',         // 数据来源 (choice手动选择 auto自动获取)
                    'auto' => [
                        'category' => 0,        // 商品分类 0为全部
                        'goodsSort' => 'all',   // 商品排序 (all默认 sales销量 price价格)
                        'showNum' => 6          // 显示数量
                    ]
                ],
                'style' => [
                    'display' => 'list',        // 显示类型 (list列表平铺 slide横向滑动)
                    'column' => 2,              // 分列数量 (双列/三列)
                    'show' => [                 // 显示内容
                        'goodsName', 'goodsPrice', 'sellingPoint', 'goodsSales',
                    ],
                    'priceColor' => '#ff1051',       // 商品价格颜色
                    'sellingColor' => '#e3771f',     // 商品卖点颜色
                    'goodsNameRows' => 'two',        // 商品名称行数 (one单行 two双行)
                    'cardType' => 'card',            // 卡片样式：flat扁平  card卡片
                    'borderRadius' => 10,            // 圆角尺寸
                    'itemMargin' => 10,              // 商品间距
                    'background' => '#f6f6f6',       // 背景颜色
                    'paddingY' => 12,                // 上下边距
                    'paddingX' => 12,                // 左右边距
                ],
                // '自动获取' => 默认数据
                'defaultData' => [
                    [
                        'goods_name' => '此处显示积分商品名称',
                        'goods_image' => \base_url() . 'assets/store/img/diy/goods/01.png',
                        'points_price_min' => 100,
                        'paid_price_min' => '0',
                        'selling_point' => '此款商品美观大方 不容错过',
                        'buy_type' => 10,
                        'goods_sales' => 100,
                    ],
                    [
                        'goods_name' => '此处显示积分商品名称',
                        'goods_image' => \base_url() . 'assets/store/img/diy/goods/01.png',
                        'points_price_min' => 80,
                        'paid_price_min' => '9.00',
                        'selling_point' => '此款商品美观大方 不容错过',
                        'buy_type' => 20,
                        'goods_sales' => 100,
                    ],
                    [
                        'goods_name' => '此处显示积分商品名称',
                        'goods_image' => \base_url() . 'assets/store/img/diy/goods/01.png',
                        'points_price_min' => 200,
                        'paid_price_min' => '0',
                        'selling_point' => '此款商品美观大方 不容错过',
                        'buy_type' => 10,
                        'goods_sales' => 100,
                    ],
                    [
                        'goods_name' => '此处显示积分商品名称',
                        'goods_image' => \base_url() . 'assets/store/img/diy/goods/01.png',
                        'points_price_min' => 120,
                        'paid_price_min' => '19.00',
                        'selling_point' => '此款商品美观大方 不容错过',
                        'buy_type' => 20,
                        'goods_sales' => 100,
                    ]
                ],
                // '手动选择' => 默认数据
                'data' => []
            ],
            'coupon' => [
                'name' => '优惠券',
                'type' => 'coupon',
                'style' => [
                    'paddingTop' => 10,
                    'background' => '#ffffff',
                    'marginRight' => 20,                // 左右边距
                    'couponBgColor' => '#ffa708',       // 优惠券背景颜色
                    'couponTextColor' => '#ffffff',     // 优惠券文字颜色
                    'receiveBgColor' => '#717070',      // 领取按钮颜色
                    'receiveTextColor' => '#ffffff',    // 领取文字颜色
                ],
                'params' => [
                    'source' => 'auto',         // 数据来源 (choice手动选择 auto自动获取)
                    'showNum' => 5
                ],
                // '自动获取' => 默认数据
                'defaultData' => [
                    [
                        'min_price' => '100.00',
                        'coupon_type' => 10,
                        'reduce_price' => '10',
                        'state' => ['text' => '正常', 'value' => 1]
                    ],
                    [
                        'min_price' => '200.00',
                        'coupon_type' => 10,
                        'reduce_price' => '20',
                        'state' => ['text' => '正常', 'value' => 1]
                    ],
                    [
                        'min_price' => '300.00',
                        'coupon_type' => 10,
                        'reduce_price' => '30',
                        'state' => ['text' => '正常', 'value' => 1]
                    ]
                ],
                // '手动选择' => 默认数据
                'data' => []
            ],
            'special' => [
                'name' => '头条快报',
                'type' => 'special',
                'params' => [
                    'source' => 'auto',         // 数据来源 (choice手动选择 auto自动获取)
                    'auto' => [
                        'category' => -1, // 文章分类ID -1为全部
                        'showNum' => 6
                    ],
                    'display' => 1, // 显示行数
                    'image' => \base_url() . 'assets/store/img/diy/special.png',
                ],
                'style' => [
                    'background' => '#ffffff', // 背景颜色
                    'textColor' => '#141414', // 文字颜色
                    'paddingTop' => 0, // 上下边距
                ],
                // '自动获取' => 默认数据
                'defaultData' => [
                    ['title' => '张小龙4小时演讲：你和高手之间，隔着“简单”二字'],
                    ['title' => '张小龙4小时演讲：你和高手之间，隔着“简单”二字']
                ],
                // '手动选择' => 默认数据
                'data' => []
            ],
            'ICPLicense' => [
                'name' => '备案号',
                'type' => 'ICPLicense',
                'params' => [
                    'text' => '网站备案号：粤ICP备10000000号-1',
                    'link' => 'https://beian.miit.gov.cn/',
                ],
                'style' => [
                    'fontSize' => '13',         // 文字大小
                    'textAlign' => 'center',    // 文字对齐
                    'textColor' => '#696969',   // 文字颜色
                    'paddingTop' => 6,          // 上下边距
                    'paddingLeft' => 0,         // 左右边距
                    'background' => '#ffffff',  // 背景颜色
                ]
            ],
            'goodsGroup' => [
                'name' => '商品分组',
                'type' => 'goodsGroup',
                'params' => [
                    'tabs' => [
                        [
                            'name' => '商品分组1',
                            'subName' => '安全健康',
                            'goodsParams' => [
                                'category' => 0,        // 商品分类 0为全部
                                'goodsSort' => 'all',   // 商品排序 (all默认 sales销量 price价格)
                                'showNum' => 10         // 显示数量
                            ]
                        ],
                        [
                            'name' => '商品分组2',
                            'subName' => '吃的放心',
                            'goodsParams' => [
                                'category' => 0,        // 商品分类 0为全部
                                'goodsSort' => 'all',   // 商品排序 (all默认 sales销量 price价格)
                                'showNum' => 10         // 显示数量
                            ]
                        ],
                        [
                            'name' => '商品分组3',
                            'subName' => '新品发布',
                            'goodsParams' => [
                                'category' => 0,        // 商品分类 0为全部
                                'goodsSort' => 'all',   // 商品排序 (all默认 sales销量 price价格)
                                'showNum' => 10         // 显示数量
                            ]
                        ],
                        [
                            'name' => '商品分组4',
                            'subName' => '严选好货',
                            'goodsParams' => [
                                'category' => 0,        // 商品分类 0为全部
                                'goodsSort' => 'all',   // 商品排序 (all默认 sales销量 price价格)
                                'showNum' => 10         // 显示数量
                            ]
                        ],
                    ]
                ],
                'style' => [
                    'tabTextColor' => '#000000',            // 选项卡文字颜色
                    'tabActiveBgColor' => '#27c29a',        // 选项卡选中背景颜色
                    'tabActiveFontColor' => '#ffffff',      // 选项卡选中文字颜色
                    'cardType' => 'card',            // 卡片样式：flat扁平  card卡片
                    'itemMargin' => 10,              // 商品间距
                    'borderRadius' => 10,            // 圆角尺寸
                    'show' => [                      // 显示内容
                        'goodsName', 'goodsPrice', 'linePrice', 'sellingPoint', 'goodsSales', 'cartBtn'
                    ],
                    'priceColor' => '#ff1051',       // 商品价格颜色
                    'sellingColor' => '#e3771f',     // 商品卖点颜色
                    'goodsNameRows' => 'two',        // 商品名称行数 (one单行 two双行)
                    'btnCartStyle' => 1,             // 购物车按钮风格
                    'btnCartColor' => '#27c29a',     // 购物车按钮背景颜色
                    // 'btnFontColor' => '#ffffff',     // 购物车按钮文字颜色
                    'paddingY' => 12,                // 上下边距
                    'paddingX' => 12,                // 左右边距
                    'background' => '#f6f6f6',       // 背景颜色
                ],
                // 默认数据
                'defaultData' => [
                    [
                        'goods_name' => '此处显示商品名称',
                        'goods_image' => \base_url() . 'assets/store/img/diy/goods/01.png',
                        'goods_price_min' => '39.00',
                        'line_price_min' => '59.00',
                        'selling_point' => '性价比较高 不容错过',
                        'goods_sales' => 2779,
                    ],
                    [
                        'goods_name' => '此处显示商品名称',
                        'goods_image' => \base_url() . 'assets/store/img/diy/goods/01.png',
                        'goods_price_min' => '29.9',
                        'line_price_min' => '49.90',
                        'selling_point' => '性价比较高 不容错过',
                        'goods_sales' => 1193,
                    ],
                    [
                        'goods_name' => '此处显示商品名称',
                        'goods_image' => \base_url() . 'assets/store/img/diy/goods/01.png',
                        'goods_price_min' => '39.00',
                        'line_price_min' => '59.00',
                        'selling_point' => '性价比较高 不容错过',
                        'goods_sales' => 2779,
                    ],
                    [
                        'goods_name' => '此处显示商品名称',
                        'goods_image' => \base_url() . 'assets/store/img/diy/goods/01.png',
                        'goods_price_min' => '29.9',
                        'line_price_min' => '49.90',
                        'selling_point' => '性价比较高 不容错过',
                        'goods_sales' => 1193,
                    ],
                ]
            ],
            'title' => [
                'name' => '标题文本',
                'type' => 'title',
                'params' => [
                    'title' => '这是标题',               // 标题文字内容
                    'desc' => '这里是描述',              // 描述文字内容
                    'titleFontSize' => 15,             // 标题大小: 12px-18px
                    'titleFontWeight' => 'bold',       // 标题字体: normal常规 bold加粗
                    'descFontSize' => 12,              // 描述大小: 12px-16px
                    'descFontWeight' => 'normal',      // 描述字体: normal常规 bold加粗
                    'more' => [
                        'enable' => true,              // 是否显示查看更多
                        'text' => '查看更多',           // 文字内容
                        'enableIcon' => true,         // 是否显示箭头图标
                        'link' => null,               // 跳转链接
                    ]
                ],
                'style' => [
                    // 'textAlign' => 'left',           // 显示位置: left居左、center居中
                    'titleTextColor' => '#323233',      // 标题文字颜色
                    'descTextColor' => '#969799',       // 描述文字颜色
                    'moreTextColor' => '#969799',       // 更多文字颜色
                    'background' => '#ffffff',          // 背景颜色
                    'paddingY' => 8,                    // 上下边距
                ]
            ],
        ];
    }

    /**
     * 获取指定的页面详情
     * @param int $pageId
     * @return static|array|null
     */
    public static function detail(int $pageId)
    {
        return static::get(['page_id' => $pageId]);
    }

    /**
     * 获取首页页面详情
     * @return static|array|null
     */
    public static function getHomePage()
    {
        return static::get(['page_type' => PageTypeEnum::HOME]);
    }

    /**
     * 合并默认数据
     * @param array $array
     * @return array
     */
    public function mergeDefaultData(array $array): array
    {
        $array['page'] = \array_merge_multiple($this->getDefaultPage(), $array['page']);
        $defaultItems = $this->getDefaultItems();
        foreach ($array['items'] as &$item) {
            if (isset($defaultItems[$item['type']])) {
                \array_key_exists('data', $item) && $defaultItems[$item['type']]['data'] = [];
                $item = \array_merge_multiple($defaultItems[$item['type']], $item);
            }
        }
        return $array;
    }
}
