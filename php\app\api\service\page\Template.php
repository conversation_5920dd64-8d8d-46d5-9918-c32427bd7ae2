<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\api\service\page;

use app\common\service\BaseService;
use app\api\model\Page as PageModel;
use cores\cloud\PageTemplate as PageTemplateCloud;
use cores\exception\BaseException;

/**
 * 服务类: 店铺页面 - 模版市场
 * Class Template
 * @package app\api\service\page
 */
class Template extends BaseService
{
    /**
     * 页面模版详情
     * @param int $templateId 页面模版ID
     * @return array
     * @throws BaseException
     */
    public function getDetail(int $templateId): array
    {
        $PageTemplateCloud = new PageTemplateCloud;
        $data = $PageTemplateCloud->detail($templateId);
        return $data['detail'];
    }

    /**
     * DIY页面详情
     * @param int|null $templateId 页面模板ID
     * @return mixed
     * @throws \cores\exception\BaseException
     * @throws \think\Exception
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getPageData(int $templateId = null)
    {
        // 页面详情
        $detail = $this->getDetail($templateId);
        empty($detail) && throwError('很抱歉，未找到该页面');
        // 合并默认的页面数据
        $pageData = $this->mergeDefaultData($detail['page_data']);
        // 获取动态数据
        foreach ($pageData['items'] as &$item) {
            // 移出无效的数据
            $item = $this->removeInvalidData($item);
            // 图片橱窗
            if ($item['type'] === 'window') {
                $item['data'] = array_values($item['data']);
            } // 商品分组
            else if ($item['type'] === 'goodsGroup') {
                $item['data'] = $this->getGoodsGroupList($item);
            }
        }
        return $pageData;
    }

    /**
     * 合并默认的页面数据
     * @param array $pageData
     * @return array
     */
    private function mergeDefaultData(array $pageData)
    {
        return (new PageModel)->mergeDefaultData($pageData);
    }

    /**
     * 移出无效的数据(默认的或demo)
     * @param array $item
     * @return array
     */
    private function removeInvalidData(array $item): array
    {
        if (array_key_exists('defaultData', $item)) {
            $item['data'] = $item['defaultData'];
            unset($item['defaultData']);
        }
//        if (array_key_exists('demo', $item)) {
//            unset($item['demo']);
//        }
        return $item;
    }

    /**
     * 商品分组：获取商品数据
     * @param $item
     * @return array
     */
    private function getGoodsGroupList($item): array
    {
        $data = [];
        foreach ($item['params']['tabs'] as $tabItem) {
            // 打乱商品列表顺序
            \shuffle($item['data']);
            $data[] = $item['data'];
        }
        return $data;
    }
}